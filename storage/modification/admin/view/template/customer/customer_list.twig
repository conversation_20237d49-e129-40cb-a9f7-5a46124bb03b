{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-customer').toggleClass('hidden-sm hidden-xs');" class="btn btn-default hidden-md hidden-lg"><i class="fa fa-filter"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>

                <a href="index.php?route=extension/module/excelport&user_token={{ user_token }}" class="btn btn-info" data-toggle="tooltip" title="Export / Import"><i class="fa fa-file-excel-o"></i></a>
            
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-customer').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">{% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <div id="filter-customer" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label class="control-label" for="input-name">{{ entry_name }}</label>
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-email">{{ entry_email }}</label>
              <input type="text" name="filter_email" value="{{ filter_email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-customer-group">{{ entry_customer_group }}</label>
              <select name="filter_customer_group_id" id="input-customer-group" class="form-control">
                <option value=""></option>
                {% for customer_group in customer_groups %}
                {% if customer_group.customer_group_id == filter_customer_group_id %}
                <option value="{{ customer_group.customer_group_id }}" selected="selected">{{ customer_group.name }}</option>
                {% else %}
                <option value="{{ customer_group.customer_group_id }}">{{ customer_group.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                {% if filter_status == '1' %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                {% endif %}
                {% if filter_status == '0' %}
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% else %}
                <option value="0">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-ip">{{ entry_ip }}</label>
              <input type="text" name="filter_ip" value="{{ filter_ip }}" placeholder="{{ entry_ip }}" id="input-ip" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-date-added">{{ entry_date_added }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" data-date-format="YYYY-MM-DD" id="input-date-added" class="form-control" />
                <span class="input-group-btn">
                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
            <div class="form-group text-right">
              <button type="button" id="button-filter" class="btn btn-default"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9 col-md-pull-3 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-customer">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked).trigger('change');" /></td>
                      <td class="text-left">{% if sort == 'name' %}<a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_name }}</a>{% else %}<a href="{{ sort_name }}">{{ column_name }}</a>{% endif %}</td>
                      <td class="text-left">{% if sort == 'c.email' %}<a href="{{ sort_email }}" class="{{ order|lower }}">{{ column_email }}</a>{% else %}<a href="{{ sort_email }}">{{ column_email }}</a>{% endif %}</td>
                      <td class="text-left">{% if sort == 'customer_group' %}<a href="{{ sort_customer_group }}" class="{{ order|lower }}">{{ column_customer_group }}</a> {% else %} <a href="{{ sort_customer_group }}">{{ column_customer_group }}</a>{% endif %}</td>
                      <td class="text-left">{% if sort == 'c.status' %}<a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>{% else %}<a href="{{ sort_status }}">{{ column_status }}</a>{% endif %}</td>
                      <td class="text-left">{% if sort == 'c.ip' %}<a href="{{ sort_ip }}" class="{{ order|lower }}">{{ column_ip }}</a>{% else %}<a href="{{ sort_ip }}">{{ column_ip }}</a>{% endif %}</td>
                      <td class="text-left">{% if sort == 'c.date_added' %}<a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a>{% else %}<a href="{{ sort_date_added }}">{{ column_date_added }}</a>{% endif %}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                  {% if customers %}
                  {% for customer in customers %}
                  <tr>
                    <td class="text-center">{% if customer.customer_id in selected %}
                      <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" checked="checked" />
                      {% else %}
                      <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" />
                      {% endif %}
                    </td>
                      <td class="text-left">{{ customer.name }}</td>
                    <td class="text-left">{{ customer.email }}</td>
                    <td class="text-left">{{ customer.customer_group }}</td>
                    <td class="text-left">{{ customer.status }}</td>
                    <td class="text-left">{{ customer.ip }}</td>
                    <td class="text-left">{{ customer.date_added }}</td>
                    <td class="text-right">
                      <div class="btn-group" style="min-width: 65px;">
                        <a href="{{ customer.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                        <button type="button" data-toggle="dropdown" class="btn btn-primary dropdown-toggle"><span class="caret"></span></button>
                        <ul class="dropdown-menu dropdown-menu-right">
                          <li class="dropdown-header">{{ text_option }}</li>
                          {% if customer.unlock %}
                          <li><a href="{{ customer.unlock }}"><i class="fa fa-unlock"></i> {{ text_unlock }}</a></li>
                          {% else %}
                          <li class="disabled"><a><i class="fa fa-unlock"></i> {{ text_unlock }}</a></li>
                          {% endif %}
                          <li role="separator" class="divider"></li>
                          <li class="dropdown-header">{{ text_login }}</li>
                          {% for store in customer.store %}
                          <li><a href="{{ store.href }}" target="_blank"><i class="fa fa-lock"></i> {{ store.name }}</a></li>
                          {% endfor %}
                        </ul>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="8">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                    </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
  $('.table-responsive').on('shown.bs.dropdown', function (e) {
    var t = $(this),
      m = $(e.target).find('.dropdown-menu'),
      tb = t.offset().top + t.height(),
      mb = m.offset().top + m.outerHeight(true),
      d = 20;
    if (t[0].scrollWidth > t.innerWidth()) {
      if (mb + d > tb) {
        t.css('padding-bottom', ((mb + d) - tb));
      }
    } else {
      t.css('overflow', 'visible');
    }
  }).on('hidden.bs.dropdown', function () {
    $(this).css({'padding-bottom': '', 'overflow': ''});
  });
  //--></script>
  <script type="text/javascript"><!--
  $('#button-filter').on('click', function() {
    url = 'index.php?route=customer/customer&user_token={{ user_token }}';
    var filter_name = $('input[name=\'filter_name\']').val();
    if (filter_name) {
      url += '&filter_name=' + encodeURIComponent(filter_name);
    }
    var filter_email = $('input[name=\'filter_email\']').val();
    if (filter_email) {
      url += '&filter_email=' + encodeURIComponent(filter_email);
    }
    var filter_customer_group_id = $('select[name=\'filter_customer_group_id\']').val();
    if (filter_customer_group_id !== '') {
      url += '&filter_customer_group_id=' + encodeURIComponent(filter_customer_group_id);
    }
    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status !== '') {
      url += '&filter_status=' + encodeURIComponent(filter_status); 
    }
    var filter_ip = $('input[name=\'filter_ip\']').val();
    if (filter_ip) {
      url += '&filter_ip=' + encodeURIComponent(filter_ip);
    }
    var filter_date_added = $('input[name=\'filter_date_added\']').val();
    if (filter_date_added) {
      url += '&filter_date_added=' + encodeURIComponent(filter_date_added);
    }
    location = url;
  });
  //--></script>
  <script type="text/javascript"><!--
  $('input[name=\'filter_name\']').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response($.map(json, function(item) {
            return {
              label: item['name'],
              value: item['customer_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      $('input[name=\'filter_name\']').val(item['label']);
    }
  });
  $('input[name=\'filter_email\']').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_email=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response($.map(json, function(item) {
            return {
              label: item['email'],
              value: item['customer_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      $('input[name=\'filter_email\']').val(item['label']);
    }
  });
  //--></script>
  <script type="text/javascript"><!--
  $('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
  });
  //--></script>
</div>
{{ footer }}

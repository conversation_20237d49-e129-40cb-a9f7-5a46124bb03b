
{% if pim_status == true %}

<script type="text/javascript"><!--
$(document).ready(function() {
	
  $(document).undelegate('a[data-toggle=\'image\']', 'click');
  
  $(document).delegate('a[data-toggle=\'image\']', 'click', function(e) {
    e.preventDefault();    
    var element = this;
    $(element).popover({
      html: true,
      placement: 'right',
      trigger: 'manual',
      content: function() {
        return '<button type="button" id="button-image" class="btn btn-primary"><i class="fa fa-pencil"></i></button> <button type="button" id="button-clear" class="btn btn-danger"><i class="fa fa-trash-o"></i></button>';
      }
    });
    location.hash = 'pim';
    $(element).popover('toggle');

    $('#button-image').on('click', function() {
      $(element).popover('hide');
      var target = $(element).parent().find('input').attr('id');
      var thumb = $(element).attr('id');
      var fm = $('<div/>').dialogelfinder({
        url : 'index.php?route=common/filemanager/connector&user_token='+getURLVar('user_token')+'&pim_available=<?php echo $pim_available; ?>',
        lang : '{{ lang }}',
        width : {{ width }},
        height: {{ height }},
        destroyOnClose : true,
        
        uiOptions : {toolbar : [['home', 'back', 'forward'],['reload'],['mkdir', 'upload'],['open', 'download', 'getfile'],['info'],['quicklook'],['copy', 'cut', 'paste'],['rm'],['duplicate', 'rename', 'edit', 'resize'],['extract', 'archive','multiupload'],['search'],['view'],['help']]},
  
        contextmenu: {navbar: ["open", "|", "copy", "cut", "paste", "duplicate", "|", "rm", "|", "info"],cwd: ["reload", "back", "|", "upload", "mkdir", "mkfile", "paste", "|", "sort", "|", "info"],files: ["getfile", "|", "open", "quicklook", "|", "download", "|", "copy", "cut", "paste", "duplicate", "|", "rm", "|", "edit", "rename", "resize", "|", "archive","multiupload", "extract", "|", "info"]},
        
        getFileCallback : function(files, fm) {
          a = files.url;

					b = a.replace('{{ image_url }}','');	
					b = b.replace('{{ image_url2 }}','');	
          
          
          $('#'+thumb).find('img').attr('src', files.tmb);
          $('#'+target).val(decodeURIComponent(b));
          $('#radio-'+target).removeAttr('disabled');
          $('#radio-'+target).val(b);
        },
        commandsOptions : {
          getfile : {
            oncomplete : 'close',
          }
        }
      }).dialogelfinder('instance');
      return;
    });

    $('#button-clear').on('click', function() {
      $(element).find('img').attr('src', $(element).find('img').attr('data-placeholder'));
      $(element).parent().find('input').attr('value', '');
      $(element).popover('hide');
    });
  });

  $(document).delegate('a[data-toggle=\'manager\']', 'click', function(e) {
    e.preventDefault();
    var fm = $('<div/>').dialogelfinder({
      url : 'index.php?route=common/filemanager/connector&user_token='+getURLVar('user_token')+'&pim_available=<?php echo $pim_available; ?>',
      lang : '{{ lang }}',
      width : {{ width }},
      height: {{ height }},
      destroyOnClose : true,
      
      uiOptions : {toolbar : [['home', 'back', 'forward'],['reload'],['mkdir', 'upload'],['open', 'download', 'getfile'],['info'],['quicklook'],['copy', 'cut', 'paste'],['rm'],['duplicate', 'rename', 'edit', 'resize'],['extract', 'archive','multiupload', 'sort'],['search'],['view'],['help']]},

      contextmenu: {navbar: ["open", "|", "copy", "cut", "paste", "duplicate", "|", "rm", "|", "info"],cwd: ["reload", "back", "|", "upload", "mkdir", "mkfile", "paste", "|", "sort", "|", "info"],files: ["getfile", "|", "open", "quicklook", "|", "download", "|", "copy", "cut", "paste", "duplicate", "|", "rm", "|", "edit", "rename", "resize", "|", "archive","multiupload", "extract", "|", "info"]},
      
      getFileCallback : function(files, fm) {
        a = files.url;
					b = a.replace('{{ image_url }}','');	
					b = b.replace('{{ image_url2 }}','');	
        addMultiImage(decodeURIComponent(b));
      },
      commandsOptions : {
        getfile : {
          oncomplete : 'close',
          folders : false
        }
      }
    }).dialogelfinder('instance');
  });

 $(document).undelegate('button[data-toggle=\'image\']', 'click');
 
    $(document).delegate('button[data-toggle=\'image\']', 'click', function(e) {
			e.preventDefault();
      location.hash = '';
      var fm = $('<div/>').dialogelfinder({
        url : 'index.php?route=common/filemanager/connector&user_token=' + getURLVar('user_token')+'&pim_available=<?php echo $pim_available; ?>',
        lang : '{{ lang }}',
        width : {{ width }},
        height: {{ height }},
        destroyOnClose : true,
        getFileCallback : function(files, fm) {
          var range, sel = window.getSelection();  
          if (sel.rangeCount) {
            var img = document.createElement('img');
            a = files.url;
            b = a.replace(files.baseUrl,'');
            img.src = files.baseUrl+''+b;
            range = sel.getRangeAt(0);
            range.insertNode(img);
          }
        },
        commandsOptions : {
          getfile : {
            oncomplete : 'close',
            folders : false
          }
        }
      }).dialogelfinder('instance');
    });
		
	$(document).ready(function() {
		// Override summernotes image manager
		$('[data-toggle=\'summernote\']').each(function() {
			var element = this;
			
			if ($(this).attr('data-lang')) {
				$('head').append('<script type="text/javascript" src="view/javascript/summernote/lang/summernote-' + $(this).attr('data-lang') + '.js"></script>');
			}

			$(element).summernote({
				lang: $(this).attr('data-lang'),
				disableDragAndDrop: true,
				height: 300,
				emptyPara: '',
				codemirror: { // codemirror options
					mode: 'text/html',
					htmlMode: true,
					lineNumbers: true,
					theme: 'monokai'
				},			
				fontsize: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '30', '36', '48' , '64'],
				toolbar: [
					['style', ['style']],
					['font', ['bold', 'underline', 'clear']],
					['fontname', ['fontname']],
					['fontsize', ['fontsize']],
					['color', ['color']],
					['para', ['ul', 'ol', 'paragraph']],
					['table', ['table']],
					['insert', ['link', 'image', 'video']],
					['view', ['fullscreen', 'codeview', 'help']]
				],
				popover: {
								image: [
						['custom', ['imageAttributes']],
						['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],
						['float', ['floatLeft', 'floatRight', 'floatNone']],
						['remove', ['removeMedia']]
					],
				},			
				buttons: {
						image: function() {
						var ui = $.summernote.ui;
								
						// create button
						var button = ui.button({
							contents: '<i class="note-icon-picture" />',
							tooltip: $.summernote.lang[$.summernote.options.lang].image.image,
							click: function () {
								$('#modal-image').remove();
								var fm = $('<div/>').dialogelfinder({
									url : 'index.php?route=common/filemanager/connector&user_token=' + getURLVar('user_token')+'&pim_available=<?php echo $pim_available; ?>',
									lang : '{{ lang }}',
									width : {{ width }},
									height: {{ height }},
									destroyOnClose : true,
									getFileCallback : function(files, fm) {
											var img = document.createElement('img');
											a = files.url;
											
											$(element).summernote('insertImage', a);
									},
									commandsOptions : {
										getfile : {
											oncomplete : 'close',
											folders : false
										}
									}
								}).dialogelfinder('instance');			
							}
						});
					
						return button.render();
					}
					}
			});
		});
	});	
		
});
//--></script> 					

		{% endif %}

        
<footer id="footer">{{ text_footer }}<br/>{{ text_version }}</footer></div>
</body></html>

<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
<meta charset="UTF-8" />
<title>{{ title }}</title>
<base href="{{ base }}" />
{% if description %}
<meta name="description" content="{{ description }}" />
{% endif %}
{% if keywords %}
<meta name="keywords" content="{{ keywords }}" />
{% endif %}
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<script type="text/javascript" src="view/javascript/jquery/jquery-2.1.1.min.js"></script>

				{% if pim_status == true  %}
					
				
          
          
          <link rel="stylesheet" href="view/javascript/jquery/jquery-ui-1.11.4.custom/jquery-ui.css" />
          <script src="view/javascript/jquery/jquery-ui-1.11.4.custom/jquery-ui.min.js"></script>
          <script type="text/javascript" src="view/javascript/pim/pim.min.js"></script>          
          <link rel="stylesheet" type="text/css" media="screen" href="view/stylesheet/pim/pim.min.css">
          <link rel="stylesheet" type="text/css" media="screen" href="view/stylesheet/pim/theme.css">
					{% if lang %}
						<script type="text/javascript" src="view/javascript/pim/i18n/{{ lang }}.js"></script>  
					{% endif %}             
          
          {% endif %}
        
<script type="text/javascript" src="view/javascript/bootstrap/js/bootstrap.min.js"></script>
<link href="view/stylesheet/bootstrap.css" type="text/css" rel="stylesheet" />
<link href="view/javascript/font-awesome/css/font-awesome.min.css" type="text/css" rel="stylesheet" />
<script src="view/javascript/jquery/datetimepicker/moment/moment.min.js" type="text/javascript"></script>
<script src="view/javascript/jquery/datetimepicker/moment/moment-with-locales.min.js" type="text/javascript"></script>
<script src="view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
<link href="view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.css" type="text/css" rel="stylesheet" media="screen" />
<link type="text/css" href="view/stylesheet/stylesheet.css" rel="stylesheet" media="screen" />
{% for style in styles %}
<link type="text/css" href="{{ style.href }}" rel="{{ style.rel }}" media="{{ style.media }}" />
{% endfor %}
{% for link in links %}
<link href="{{ link.href }}" rel="{{ link.rel }}" />
{% endfor %}
<script src="view/javascript/common.js" type="text/javascript"></script>
{% for script in scripts %}
<script type="text/javascript" src="{{ script }}"></script>
{% endfor %}
</head>
<body>
<div id="container">
<header id="header" class="navbar navbar-static-top">
  <div class="container-fluid">
    <div id="header-logo" class="navbar-header"><a href="{{ home }}" class="navbar-brand"><img src="view/image/logo.png" alt="{{ heading_title }}" title="{{ heading_title }}" /></a></div>
    {% if logged %}<a href="#" id="button-menu" class="hidden-md hidden-lg"><span class="fa fa-bars"></span></a>
    <ul class="nav navbar-nav navbar-right">
      <li class="dropdown"><a href="#" class="dropdown-toggle" data-toggle="dropdown"><img src="{{ image }}" alt="{{ firstname }} {{ lastname }}" title="{{ username }}" id="user-profile" class="img-circle" />{{ firstname }} {{ lastname }} <i class="fa fa-caret-down fa-fw"></i></a>
        <ul class="dropdown-menu dropdown-menu-right">
          <li><a href="{{ profile }}"><i class="fa fa-user-circle-o fa-fw"></i> {{ text_profile }}</a></li>
          <li role="separator" class="divider"></li>
          <li class="dropdown-header">{{ text_store }}</li>
          {% for store in stores %}
          <li><a href="{{ store.href }}" target="_blank">{{ store.name }}</a></li>
          {% endfor %}
          <li role="separator" class="divider"></li>
          <li class="dropdown-header">{{ text_help }}</li>
          <li><a href="https://www.opencart.com" target="_blank"><i class="fa fa-opencart fa-fw"></i> {{ text_homepage }}</a></li>
          <li><a href="http://docs.opencart.com" target="_blank"><i class="fa fa-file-text-o fa-fw"></i> {{ text_documentation }}</a></li>
          <li><a href="https://forum.opencart.com" target="_blank"><i class="fa fa-comments-o fa-fw"></i> {{ text_support }}</a></li>
        </ul>
      </li>
      <li><a href="{{ logout }}"><i class="fa fa-sign-out"></i> <span class="hidden-xs hidden-sm hidden-md">{{ text_logout }}</span></a></li>
    </ul>
    {% endif %} </div>
</header>
{{ header }}
<style>
#content{
	position: static !important;
}
.w_content .alert{
	margin-top: 15px;
}
.breadcrumb{
	margin: 0px 0 8px 0 !important;
}
</style>
<link href="view/stylesheet/sys_ticket/style.css" rel="stylesheet" />
{{ column_left }}
<div id="content">
 <div class="container-fluid">
	<div class="w_wrap">
		<div class="w_header">
			<div class="flscr_btn">
				<i class="fa fa-arrows-alt"></i>
			</div>
			<h1>{{ heading_title }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
			<br />
			<div class="row">
				<div class="col-sm-4 col-sm-offset-4">
					<div class="input-group">
						<select class="form-control" onchange="location = this.options[this.selectedIndex].value;" name="store_id">
								{% for store in stores %}
								{% if store['store_id'] == store_id %}
								{% set select = 'selected=selected' %}
								{% else %}
								{% set select = '' %}
								{% endif %}
							<option {{ select }} value="{{ redirect }}&store_id={{ store['store_id'] }}">{{ store['name'] }}</option>
								{% endfor %}
						</select>
						<span class="input-group-btn">
							<button type="submit" form="form-syssetting" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-warning">Save Extension Settings</button>
						</span>
					</div>
				</div>
			</div>
		</div>
		<div class="w_content">
			{% if error_warning %}
			<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
			  <button type="button" class="close" data-dismiss="alert">&times;</button>
			</div>
		   {% endif %}
		   {% if success %}
			<div class="alert alert-success"><i class="fa fa-check-circle"></i> {{ success }}
			  <button type="button" class="close" data-dismiss="alert">&times;</button>
			</div>
		   {% endif %}
		    <div class="row">
				<div class="col-sm-3">
					<ul class="nav nav-pills nav-stacked">
					  <li role="presentation" class="active"><a role="tab" data-toggle="tab" href="#tab-general" aria-expanded="true">{{tab_general}}</a></li>
					  <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-language" aria-expanded="true">{{tab_language}}</a></li>
					  <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-review" aria-expanded="true">{{tab_review}}</a></li>
					   <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-adminemail" aria-expanded="true">{{tab_adminemail}}</a></li>
					   <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-customeremail" aria-expanded="true">{{tab_customeremail}}</a></li>
					   <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-seo" aria-expanded="true">{{tab_seo}}</a></li>
					   <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-mailsetting" aria-expanded="true">{{tab_mailsetting}}</a></li>
					   <li role="presentation"><a role="tab" data-toggle="tab" href="#tab-cronjob" aria-expanded="true">{{text_cronjob}}</a></li>
					</ul>
					<div class="well" style="margin-top:15px;">
						<h3>Short Codes</h3>
						<p>
							{name} = Name<br>
							{email} = Email<br>
							{subject} = Subject<br>
							{department} = Department<br>
							{message} = Message<br>
							{store} = Store<br>
							{storeemail} = Store Email<br>
							{logo} = Store Logo
						</p>
					</div>
				</div>
				<div class="col-sm-9">
				<form enctype="multipart/form-data" method="post" id="form-syssetting" class="form-horizontal">
					<div class="tab-content">
						<div id="tab-general" role="tabpanel" class="tab-pane fade active in">
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
								<div class="col-sm-10">
								  <select name="sys_setting_status" id="input-status" class="form-control">
									{% if sys_setting_status %}
									<option value="1" selected="selected">{{ text_enabled }}</option>
									<option value="0">{{ text_disabled }}</option>
									{% else %}
									<option value="1">{{ text_enabled }}</option>
									<option value="0" selected="selected">{{ text_disabled }}</option>
									{% endif %}
								  </select>
								</div>
							 </div>
							 <div class="form-group">
								<label class="col-sm-2 control-label">{{ entry_showin }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_showin  == 'header' %} active {% endif %}">
											<input type="radio" {% if sys_setting_showin == 'header' %} checked {% endif %} value="header" name="sys_setting_showin">{{text_header}}
										</label>
										<label class="btn btn-primary btn-sm {% if sys_setting_showin  == 'footer' %} active {% endif %}">
											<input type="radio" {% if sys_setting_showin == 'footer' %} checked {% endif %} value="footer" name="sys_setting_showin">{{text_footers}}
										</label>
										<label class="btn btn-primary btn-sm {% if sys_setting_showin == 'both' %} active {% endif %}">
											<input type="radio" {% if sys_setting_showin == 'both' %} checked {% endif %} value="both" name="sys_setting_showin">{{text_both}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_showin %} active {% endif %}">
											<input type="radio" {% if not sys_setting_showin %} checked {% endif %} value="0" name="sys_setting_showin">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">{{ entry_allowtoguest }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_allowto  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_allowto == '1' %} checked {% endif %} value="1" name="sys_setting_allowto">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_allowto %} active {% endif %}">
											<input type="radio" {% if not sys_setting_allowto %} checked {% endif %} value="0" name="sys_setting_allowto">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">{{ entry_knowledgebasecat }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_knowledgebasecat  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_knowledgebasecat %} checked {% endif %} value="1" name="sys_setting_knowledgebasecat">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_knowledgebasecat %} active {% endif %}">
											<input type="radio" {% if not sys_setting_knowledgebasecat %} checked {% endif %} value="0" name="sys_setting_knowledgebasecat">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							 <div class="form-group required">
								<label class="col-sm-2 control-label" for="input-status">{{ text_default }}</label>
								<div class="col-sm-10">
									<table class="table table-bordered">
										<thead>
											<tr>
												<td>{{ text_priority }}</td>
												<td>{{ entry_status }}</td>
												<td>{{ text_agent }}</td>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td>
													<select name="sys_setting_default[priority]" id="input-status" class="form-control">
													{% for priority in prioritys %}
														<option value="{{ priority.sys_priority_id }}" {% if priority.sys_priority_id == sys_setting_default.priority %}selected="selected" {% endif %}>{{ priority.priority }}</option>
													{% endfor %}
													 </select>
													 {% if error_priority %}
														<div class="text-danger">{{ error_priority }}</div>
													  {% endif %}
												</td>
												<td>
													<select name="sys_setting_default[status]" id="input-status" class="form-control">
													{% for status in statuses %}
														<option value="{{ status.sys_status_id }}" {% if status.sys_status_id == sys_setting_default.status %}selected="selected" {% endif %}>{{ status.status }}</option>
													{% endfor %}
													 </select>
													 {% if error_status %}
														<div class="text-danger">{{ error_status }}</div>
													  {% endif %}
												</td>
												<td>
													<select name="sys_setting_default[agent]" id="input-status" class="form-control">
													{% for agent in agents %}
														<option value="{{ agent.agent_id }}" {% if agent.agent_id == sys_setting_default.agent %}selected="selected" {% endif %}>{{ agent.name }}</option>
													{% endfor %}
													 </select>
													 {% if error_agent %}
														<div class="text-danger">{{ error_agent }}</div>
													  {% endif %}
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							 </div>
							 <div class="form-group">
								<label class="col-sm-2 control-label" for="input-status">{{ text_custom }}</label>
								<div class="col-sm-10">
									<table id="custom" class="table table-striped table-bordered table-hover">
									  <thead>
										<tr>
											<td>{{ text_dept }}</td>
											<td>{{ text_priority }}</td>
											<td>{{ entry_status }}</td>
											<td>{{ text_agent }}</td>
											<td></td>
										</tr>
									  </thead>
									  <tbody>
									  
									  {% set custom_row = 0 %}
									  {% for sys_setting_custom in sys_setting_customs %}
									  <tr id="custom-row{{ custom_row }}">
										<td class="text-left">
											<select name="sys_setting_custom[{{ custom_row }}][topic]" id="input-status" class="form-control">
											{% for topic in topics %}
												<option value="{{ topic.sys_topic_id }}" {% if topic.sys_topic_id == sys_setting_custom.topic %}selected="selected" {% endif %}>{{ topic.title }}</option>
											{% endfor %}
											</select>
										</td>
										<td class="text-left">
											<select name="sys_setting_custom[{{ custom_row }}][priority]" id="input-status" class="form-control">
											{% for priority in prioritys %}
												<option value="{{ priority.sys_priority_id }}" {% if priority.sys_priority_id == sys_setting_custom.priority %}selected="selected" {% endif %}>{{ priority.priority }}</option>
											{% endfor %}
											</select>
										</td>
										<td class="text-left">
											<select name="sys_setting_custom[{{ custom_row }}][status]" id="input-status" class="form-control">
											{% for status in statuses %}
												<option value="{{ status.sys_status_id }}" {% if status.sys_status_id == sys_setting_custom.status %}selected="selected" {% endif %}>{{ status.status }}</option>
											{% endfor %}
											</select>
										</td>
										<td class="text-left">
											<select name="sys_setting_custom[{{ custom_row }}][agent]" id="input-status" class="form-control">
											{% for agent in agents %}
												<option value="{{ agent.agent_id }}" {% if agent.agent_id == sys_setting_custom.agent %}selected="selected" {% endif %}>{{ agent.name }}</option>
											{% endfor %}
											</select>
										</td>
										<td class="text-left"><button type="button" onclick="$('#custom-row{{ custom_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
									  </tr>
									  {% set custom_row = custom_row + 1 %}
									  {% endfor %}
										</tbody>
									  <tfoot>
										<tr>
										  <td colspan="4"></td>
										  <td class="text-left"><button type="button" onclick="addCustom();" data-toggle="tooltip" title="{{ button_custom_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
										</tr>
									  </tfoot>
									</table>
								</div>
							 </div>

							 <div class="form-group">
								<label class="col-sm-2 control-label" for="input-status">{{ entry_close_status }}</label>
								<div class="col-sm-10">
								  <select name="sys_setting_closed_status" id="input-status" class="form-control">
									{% if statuses %}
									{% for status in statuses %}
									<option value="{{ status.sys_status_id }}" {% if sys_setting_closed_status == status.sys_status_id %} selected="selected" {% endif %}>{{ status.status }}</option>
									{% endfor %}
									{% endif %}
								  </select>
								</div>
							 </div>
						</div>
						<div id="tab-language" role="tabpanel" class="tab-pane fade in">
							<ul class="nav nav-tabs" id="desclanguage">
								{% for language in languages %}
								<li><a href="#desclanguage{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
								{% endfor %}
							  </ul>
							  <div class="tab-content">{% for language in languages %}
								<div class="tab-pane" id="desclanguage{{ language.language_id }}">
								  <div class="form-group">
									<label class="col-sm-2 control-label" for="input-name{{ language.language_id }}">{{ entry_heading }}</label>
									<div class="col-sm-10">
									  <input type="text" name="sys_setting_desc[{{ language.language_id }}][heading]" value="{{ sys_setting_desc[language.language_id] ? sys_setting_desc[language.language_id].heading }}" placeholder="{{ entry_heading }}" id="input-name{{ language.language_id }}" class="form-control" />
									 </div>
								  </div>
								  <div class="form-group">
									<label class="col-sm-2 control-label" for="input-description{{ language.language_id }}">{{ entry_description }}</label>
									<div class="col-sm-10">
									  <textarea name="sys_setting_desc[{{ language.language_id }}][description]" placeholder="{{ entry_description }}" id="input-description{{ language.language_id }}" data-toggle="summernote" data-lang="{{ summernote }}" class="form-control">{{ sys_setting_desc[language.language_id] ? sys_setting_desc[language.language_id].description }}</textarea>
									</div>
								  </div>
								  <div class="form-group">
									<label class="col-sm-2 control-label" for="input-meta-title{{ language.language_id }}">{{ entry_meta_title }}</label>
									<div class="col-sm-10">
									  <input type="text" name="sys_setting_desc[{{ language.language_id }}][meta_title]" value="{{ sys_setting_desc[language.language_id] ? sys_setting_desc[language.language_id].meta_title }}" placeholder="{{ entry_meta_title }}" id="input-meta-title{{ language.language_id }}" class="form-control" />
									 </div>
								  </div>
								  <div class="form-group">
									<label class="col-sm-2 control-label" for="input-meta-description{{ language.language_id }}">{{ entry_meta_description }}</label>
									<div class="col-sm-10">
									  <textarea name="sys_setting_desc[{{ language.language_id }}][meta_description]" rows="5" placeholder="{{ entry_meta_description }}" id="input-meta-description{{ language.language_id }}" class="form-control">{{ sys_setting_desc[language.language_id] ? sys_setting_desc[language.language_id].meta_description }}</textarea>
									</div>
								  </div>
								  <div class="form-group">
									<label class="col-sm-2 control-label" for="input-meta-keyword{{ language.language_id }}">{{ entry_meta_keyword }}</label>
									<div class="col-sm-10">
									  <textarea name="sys_setting_desc[{{ language.language_id }}][meta_keyword]" rows="5" placeholder="{{ entry_meta_keyword }}" id="input-meta-keyword{{ language.language_id }}" class="form-control">{{ sys_setting_desc[language.language_id] ? sys_setting_desc[language.language_id].meta_keyword }}</textarea>
									</div>
								  </div>
								</div>
								{% endfor %}</div>
						</div>
						<div id="tab-mailsetting" role="tabpanel" class="tab-pane fade in">
							 <div class="form-group">
								<label class="col-sm-2 control-label" for="input-name">{{ entry_server }}</label>
								<div class="col-sm-10">
								  <input type="text" name="sys_setting_mail[server]" value="{{ sys_setting_mail['server'] ? sys_setting_mail['server'] }}" placeholder="{{ entry_server }}" id="input-name" class="form-control" />
								 </div>
							  </div>
							  <div class="form-group">
								<label class="col-sm-2 control-label" for="input-name">{{ entry_username }}</label>
								<div class="col-sm-10">
								  <input type="text" name="sys_setting_mail[username]" value="{{ sys_setting_mail['username'] ? sys_setting_mail['username'] }}" placeholder="{{ entry_username }}" id="input-name" class="form-control" />
								 </div>
							  </div>
							  <div class="form-group">
								<label class="col-sm-2 control-label" for="input-name">{{ entry_password }}</label>
								<div class="col-sm-10">
								  <input type="text" name="sys_setting_mail[password]" value="{{ sys_setting_mail['password'] ? sys_setting_mail['password'] }}" placeholder="{{ entry_password }}" id="input-name" class="form-control" />
								 </div>
							  </div>
							  <div class="form-group">
								<label class="col-sm-2 control-label">{{ entry_deletemails }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_mail['deletemails']  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_mail['deletemails'] %} checked {% endif %} value="1" name="sys_setting_mail[deletemails]">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_mail['deletemails'] %}active {% endif %}">
											<input type="radio" {% if not sys_setting_mail['deletemails'] %} checked {% endif %} value="0" name="sys_setting_mail[deletemails]">{{text_no}}
										</label>
									</div>
								</div>
							  </div>
							  <div class="form-group">
								<label class="col-sm-2 control-label" for="input-name">{{ entry_defaultdept }}</label>
								<div class="col-sm-10">
								  <select name="sys_setting_mail[department]" id="input-status" class="form-control">
									{% for topic in topics %}
										<option value="{{ topic.sys_topic_id }}" {% if topic.sys_topic_id == sys_setting_mail.department %}selected="selected" {% endif %}>{{ topic.title }}</option>
									{% endfor %}
									</select>
								  </div>
							  </div>
						</div>
						<div id="tab-cronjob" role="tabpanel" class="tab-pane fade in">
							<div class="form-group">
								<label class="col-sm-2 control-label">{{ text_cronjob }} Link</label>
								<div class="col-sm-10">
									<input readonly="readonly" type="text" name="sys_setting_crownjob"  class="form-control" value="{{ crownjob_link }}">
								</div>
							</div>
						</div>
						<div id="tab-review" role="tabpanel" class="tab-pane fade in">
							<ul class="nav nav-tabs" id="review_language">
								{% for language in languages %}
								<li><a href="#review_language{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
								{% endfor %}
							</ul>
							<div class="tab-content">
								{% for language in languages %}
								<div class="tab-pane" id="review_language{{ language.language_id }}">
									<div class="form-group">
										<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ entry_title }}</label>
										<div class="col-sm-10">
										 <input type="text" name="sys_setting_review[{{ language.language_id }}][title]" value="{{ sys_setting_review[language.language_id] ? sys_setting_review[language.language_id]['title'] }}" placeholder="{{ entry_title }}" id="input-subject{{ language.language_id }}" class="form-control" />
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-2 control-label" for="input-email{{ language.language_id }}">{{ entry_description }}</label>
										<div class="col-sm-10">
										<textarea name="sys_setting_review[{{ language.language_id }}][description]" placeholder="{{ entry_description }}" id="input-email{{ language.language_id }}" data-toggle="summernote" data-lang="{{ summernote }}" class="form-control">{{ sys_setting_review[language.language_id] ? sys_setting_review[language.language_id]['description']
										}}</textarea>
										</div>
									</div>
								</div>
								{% endfor %}
								<div class="form-group">
									<label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
									<div class="col-sm-10">
									  <select name="sys_setting_review[status]" id="input-status" class="form-control">
										{% if sys_setting_review['status'] %}
										<option value="1" selected="selected">{{ text_enabled }}</option>
										<option value="0">{{ text_disabled }}</option>
										{% else %}
										<option value="1">{{ text_enabled }}</option>
										<option value="0" selected="selected">{{ text_disabled }}</option>
										{% endif %}
									  </select>
									</div>
								</div>
							</div>
						</div>
						<div id="tab-adminemail" role="tabpanel" class="tab-pane fade in">
							<div class="form-group">
								<label class="col-sm-3 control-label">{{ entry_admin_notify }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_adminnotify  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_adminnotify %} checked {% endif %} value="1" name="sys_setting_adminnotify">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_adminnotify %} active {% endif %}">
											<input type="radio" {% if not sys_setting_adminnotify %} checked {% endif %} value="0" name="sys_setting_adminnotify">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-3 control-label">{{ entry_agent_notify }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_agentnotify  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_agentnotify %} checked {% endif %} value="1" name="sys_setting_agentnotify">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_agentnotify %} active {% endif %}">
											<input type="radio" {% if not sys_setting_agentnotify %} checked {% endif %} value="0" name="sys_setting_agentnotify">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							<ul class="nav nav-tabs" id="language1">
								{% for language in languages %}
								<li><a href="#language1{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
								{% endfor %}
							</ul>
							<div class="tab-content">
								{% for language in languages %}
								<div class="tab-pane" id="language1{{ language.language_id }}">
									<div class="form-group">
										<label class="col-sm-3 control-label" for="input-subject{{ language.language_id }}">{{ entry_subject }}</label>
										<div class="col-sm-9">
										 <input type="text" name="sys_setting_admin[{{ language.language_id }}][subject]" value="{{ sys_setting_admin[language.language_id] ? sys_setting_admin[language.language_id]['subject'] }}" placeholder="{{ entry_subject }}" id="input-subject{{ language.language_id }}" class="form-control" />
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-3 control-label" for="input-email{{ language.language_id }}">{{ entry_email_template }}</label>
										<div class="col-sm-9">
										<textarea name="sys_setting_admin[{{ language.language_id }}][message]" placeholder="{{ entry_email_template }}" id="input-email{{ language.language_id }}" data-toggle="summernote" data-lang="{{ summernote }}" class="form-control">{{ sys_setting_admin[language.language_id] ? sys_setting_admin[language.language_id]['message']
										}}</textarea>
										</div>
									</div>
								</div>
								{% endfor %}
							</div>
						</div>
						<div id="tab-customeremail" role="tabpanel" class="tab-pane fade in">
							<div class="form-group">
								<label class="col-sm-3 control-label">{{ entry_customer_notify }}</label>
								<div class="col-sm-6">
									<div class="btn-group" data-toggle="buttons">
										<label class="btn btn-primary btn-sm {% if sys_setting_customernotify  == '1' %} active {% endif %}">
											<input type="radio" {% if sys_setting_customernotify %} checked {% endif %} value="1" name="sys_setting_customernotify">{{text_yes}}
										</label>
										<label class="btn btn-primary btn-sm {% if not sys_setting_customernotify %} active {% endif %}">
											<input type="radio" {% if not sys_setting_customernotify %} checked {% endif %} value="0" name="sys_setting_customernotify">{{text_no}}
										</label>
									</div>
								</div>
							</div>
							<ul class="nav nav-tabs" id="language2">
								{% for language in languages %}
								<li><a href="#language2{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
								{% endfor %}
							</ul>
							<div class="tab-content">
								{% for language in languages %}
								<div class="tab-pane" id="language2{{ language.language_id }}">
									<div class="form-group">
										<label class="col-sm-3 control-label" for="input-subject{{ language.language_id }}">{{ entry_subject }}</label>
										<div class="col-sm-9">
										 <input type="text" name="sys_setting_customer[{{ language.language_id }}][subject]" value="{{ sys_setting_customer[language.language_id] ? sys_setting_customer[language.language_id]['subject'] }}" placeholder="{{ entry_subject }}" id="input-subject{{ language.language_id }}" class="form-control" />
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-3 control-label" for="input-email{{ language.language_id }}">{{ entry_email_template }}</label>
										<div class="col-sm-9">
										<textarea name="sys_setting_customer[{{ language.language_id }}][message]" placeholder="{{ entry_email_template }}" id="input-email{{ language.language_id }}" data-toggle="summernote" data-lang="{{ summernote }}" class="form-control">{{ sys_setting_customer[language.language_id] ? sys_setting_customer[language.language_id]['message']
										}}</textarea>
										</div>
									</div>
								</div>
								{% endfor %}
							</div>
						</div>
						<div id="tab-seo" role="tabpanel" class="tab-pane fade in">
							<legend>{{ text_tdashboard }}</legend>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ text_seo }}</label>
								<div class="col-sm-10">
								{% for language in languages %}
								  <div class="input-group"><span class="input-group-addon"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /></span>
									<input type="text" name="sys_setting_seo[{{ store_id }}][{{ language.language_id }}]" value="{% if sys_setting_seo[store_id][language.language_id] %}{{ sys_setting_seo[store_id][language.language_id] }}{% endif %}" placeholder="{{ entry_keyword }}" class="form-control" />
								  </div>
								   {% endfor %}
								</div>		
							</div>	
							<legend>{{ text_nticket }}</legend>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ text_seo }}</label>
								<div class="col-sm-10">
								{% for language in languages %}
								  <div class="input-group"><span class="input-group-addon"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /></span>
									<input type="text" name="sys_setting_nticketseo[{{ store_id }}][{{ language.language_id }}]" value="{% if sys_setting_nticketseo[store_id][language.language_id] %}{{ sys_setting_nticketseo[store_id][language.language_id] }}{% endif %}" placeholder="{{ entry_keyword }}" class="form-control" />
								  </div>
								   {% endfor %}
								</div>		
							</div>
							<legend>{{ text_tlist }}</legend>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ text_seo }}</label>
								<div class="col-sm-10">
								{% for language in languages %}
								  <div class="input-group"><span class="input-group-addon"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /></span>
									<input type="text" name="sys_setting_tlistseo[{{ store_id }}][{{ language.language_id }}]" value="{% if sys_setting_tlistseo[store_id][language.language_id] %}{{ sys_setting_tlistseo[store_id][language.language_id] }}{% endif %}" placeholder="{{ entry_keyword }}" class="form-control" />
								  </div>
									{% endfor %}
								</div>		
							</div>
							<legend>{{ text_atlist }}</legend>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ text_seo }}</label>
								<div class="col-sm-10">
								{% for language in languages %}
								  <div class="input-group"><span class="input-group-addon"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /></span>
									<input type="text" name="sys_setting_atlistseo[{{ store_id }}][{{ language.language_id }}]" value="{% if sys_setting_atlistseo[store_id][language.language_id] %}{{ sys_setting_atlistseo[store_id][language.language_id] }}{% endif %}" placeholder="{{ entry_keyword }}" class="form-control" />
								  </div>
								  {% endfor %}
								</div>		
							</div>	
							<legend>{{ text_knowledgbase }}</legend>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input-subject{{ language.language_id }}">{{ text_seo }}</label>
								<div class="col-sm-10">
								{% for language in languages %}
								  <div class="input-group"><span class="input-group-addon"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /></span>
									<input type="text" name="sys_setting_knowledgeseo[{{ store_id }}][{{ language.language_id }}]" value="{% if sys_setting_knowledgeseo[store_id][language.language_id] %}{{ sys_setting_knowledgeseo[store_id][language.language_id] }}{% endif %}" placeholder="{{ entry_keyword }}" class="form-control" />
								  </div>
								  {% endfor %}
								</div>		
							</div>	
						</div>
					</div>
					</form>
				</div>
			</div>
		</div>
		<div class="w_footer">
			&copy; Webx IT Solutions <br />Version: 1.0
		</div>
	</div>
  </div>
</div>
{{ footer }}
<script type="text/javascript" src="view/javascript/summernote/summernote.js"></script>
<link href="view/javascript/summernote/summernote.css" rel="stylesheet" />
<!--  Disabled by Power Image Manager. Moved in the footer; <script type="text/javascript" src="view/javascript/summernote/opencart.js"></script> -->
<script type="text/javascript"><!--
//--></script>
 <script type="text/javascript"><!--
$('#language2 a:first').tab('show');
$('#language1 a:first').tab('show');
$('#review_language a:first').tab('show');
$('#desclanguage a:first').tab('show');
//--></script>
<script type="text/javascript"><!--
var custom_row = {{ custom_row }};

function addCustom() {
	html  = '<tr id="custom-row' + custom_row + '">';
    html += '  <td class="text-left"><select name="sys_setting_custom[' + custom_row + '][topic]" id="input-status" class="form-control">';
    {% for topic in topics %}
    html += '      <option value="{{ topic.sys_topic_id }}">{{ topic.title }}</option>';
    {% endfor %}
    html += '  </select></td>';
	html += '  <td class="text-left"><select name="sys_setting_custom[' + custom_row + '][priority]" id="input-status" class="form-control">';
    {% for priority in prioritys %}
    html += '      <option value="{{ priority.sys_priority_id }}">{{ priority.priority }}</option>';
    {% endfor %}
    html += '  </select></td>';
	html += '  <td class="text-left"><select name="sys_setting_custom[' + custom_row + '][status]" id="input-status" class="form-control">';
    {% for status in statuses %}
    html += '      <option value="{{ status.sys_status_id }}">{{ status.status }}</option>';
    {% endfor %}
    html += '  </select></td>';
	html += '  <td class="text-left"><select name="sys_setting_custom[' + custom_row + '][agent]" id="input-status" class="form-control">';
    {% for agent in agents %}
    html += '      <option value="{{ agent.agent_id }}">{{ agent.name }}</option>';
    {% endfor %}
    html += '  </select></td>';
	html += '  <td class="text-left"><button type="button" onclick="$(\'#custom-row' + custom_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
	html += '</tr>';

	$('#custom tbody').append(html);

	$('.date').datetimepicker({
		language: '{{ datepicker }}',
		pickTime: false
	});

	custom_row++;
}
//--></script> 
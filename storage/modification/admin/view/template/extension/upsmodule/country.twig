{{ header }} {{ column_left }}
<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <h1 class="logo">{{text_UPS_Shipping_Module }}</h1>
            <ul class="breadcrumb">
                <li><a href="{{ home }}">{{ text_home }}</a></li>
                <li><a href="{{ country }}">{{ text_form }}</a></li>
            </ul>
        </div>
        <div class="container-fluid">
            <div class="alert alert-danger" style="display:none"><i class="fa fa-exclamation-circle"></i>{{ error_warning }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">{{ text_form }}</h3>
                </div>
                <div class="panel-body">
                    <form action="{{ action }}" method="post" id="form-article" class="form-horizontal">
                        <div class="tab-content">
                            <div class="form-group">
                                <h5 class="col-sm-3 control-label">{{text_select_country }}</h5>
                                <div class="col-sm-7">
                                    <select class="form-control" name="Country">
                                        {% for key, value in totals %}
                                            <option {% if key == isCountry %}selected{% endif %} value="{{ key }}" >{{ value }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="pull-right">
                                <button type="submit" form="form-article" class="btn btn-primary">{{ button_continue }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <link rel="stylesheet" type="text/css" href="view/stylesheet/upsmodule/common.css" />
    <script type="text/javascript" src="view/javascript/summernote/summernote.js"></script>
    <link href="view/javascript/summernote/summernote.css" rel="stylesheet" />
    <!--  Disabled by Power Image Manager. Moved in the footer; <script type="text/javascript" src="view/javascript/summernote/opencart.js"></script> -->
    <script type="text/javascript" src="view/javascript/upsmodule/common.js"></script>
{{ footer }}

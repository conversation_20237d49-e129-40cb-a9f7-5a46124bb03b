<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON>cier <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

/**
 * <AUTHOR> <b<PERSON><PERSON><PERSON>@gmail.com>
 */
class LengthValidator extends ConstraintValidator
{
    /**
     * {@inheritdoc}
     */
    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof Length) {
            throw new UnexpectedTypeException($constraint, __NAMESPACE__.'\Length');
        }

        if (null === $value || '' === $value) {
            return;
        }

        if (!is_scalar($value) && !(\is_object($value) && method_exists($value, '__toString'))) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $stringValue = (string) $value;

        if (!$invalidCharset = !@mb_check_encoding($stringValue, $constraint->charset)) {
            $length = mb_strlen($stringValue, $constraint->charset);
        }

        if ($invalidCharset) {
            if ($this->context instanceof ExecutionContextInterface) {
                $this->context->buildViolation($constraint->charsetMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ charset }}', $constraint->charset)
                    ->setInvalidValue($value)
                    ->setCode(Length::INVALID_CHARACTERS_ERROR)
                    ->addViolation();
            } else {
                $this->buildViolation($constraint->charsetMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ charset }}', $constraint->charset)
                    ->setInvalidValue($value)
                    ->setCode(Length::INVALID_CHARACTERS_ERROR)
                    ->addViolation();
            }

            return;
        }

        if (null !== $constraint->max && $length > $constraint->max) {
            if ($this->context instanceof ExecutionContextInterface) {
                $this->context->buildViolation($constraint->min == $constraint->max ? $constraint->exactMessage : $constraint->maxMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ limit }}', $constraint->max)
                    ->setInvalidValue($value)
                    ->setPlural((int) $constraint->max)
                    ->setCode(Length::TOO_LONG_ERROR)
                    ->addViolation();
            } else {
                $this->buildViolation($constraint->min == $constraint->max ? $constraint->exactMessage : $constraint->maxMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ limit }}', $constraint->max)
                    ->setInvalidValue($value)
                    ->setPlural((int) $constraint->max)
                    ->setCode(Length::TOO_LONG_ERROR)
                    ->addViolation();
            }

            return;
        }

        if (null !== $constraint->min && $length < $constraint->min) {
            if ($this->context instanceof ExecutionContextInterface) {
                $this->context->buildViolation($constraint->min == $constraint->max ? $constraint->exactMessage : $constraint->minMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ limit }}', $constraint->min)
                    ->setInvalidValue($value)
                    ->setPlural((int) $constraint->min)
                    ->setCode(Length::TOO_SHORT_ERROR)
                    ->addViolation();
            } else {
                $this->buildViolation($constraint->min == $constraint->max ? $constraint->exactMessage : $constraint->minMessage)
                    ->setParameter('{{ value }}', $this->formatValue($stringValue))
                    ->setParameter('{{ limit }}', $constraint->min)
                    ->setInvalidValue($value)
                    ->setPlural((int) $constraint->min)
                    ->setCode(Length::TOO_SHORT_ERROR)
                    ->addViolation();
            }
        }
    }
}

{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "faa4d6f172d4f9991cf2583e57f9b43c", "packages": [{"name": "guzzlehttp/guzzle", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "f3c8c22471cb55475105c14769644a49c3262b93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/f3c8c22471cb55475105c14769644a49c3262b93", "reference": "f3c8c22471cb55475105c14769644a49c3262b93", "shasum": ""}, "require": {"guzzlehttp/ringphp": "^1.1", "php": ">=5.4.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0", "psr/log": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library and framework for building RESTful web service clients", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2015-05-20 03:47:55"}, {"name": "guzzlehttp/ringphp", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/guzzle/RingPHP.git", "reference": "dbbb91d7f6c191e5e405e900e3102ac7f261bc0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/RingPHP/zipball/dbbb91d7f6c191e5e405e900e3102ac7f261bc0b", "reference": "dbbb91d7f6c191e5e405e900e3102ac7f261bc0b", "shasum": ""}, "require": {"guzzlehttp/streams": "~3.0", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple API and specification that abstracts away the details of HTTP into a single PHP function.", "time": "2015-05-20 03:37:09"}, {"name": "guzzlehttp/streams", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/streams.git", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/streams/zipball/47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "reference": "47aaa48e27dae43d39fc1cea0ccf0d84ac1a2ba5", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Provides a simple abstraction over streams of data", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "time": "2014-10-12 19:18:40"}, {"name": "react/promise", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "3b6fca09c7d56321057fa8867c8dbe1abf648627"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/3b6fca09c7d56321057fa8867c8dbe1abf648627", "reference": "3b6fca09c7d56321057fa8867c8dbe1abf648627", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "time": "2015-07-03 13:48:55"}], "packages-dev": [{"name": "andrewsville/php-token-reflection", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/Andrewsville/PHP-Token-Reflection.git", "reference": "e6d0ac2baf66cdf154be34c3d2a2aa1bd4b426ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Andrewsville/PHP-Token-Reflection/zipball/e6d0ac2baf66cdf154be34c3d2a2aa1bd4b426ee", "reference": "e6d0ac2baf66cdf154be34c3d2a2aa1bd4b426ee", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"TokenReflection": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3"], "authors": [{"name": "Ondřej Nešpor", "homepage": "https://github.com/andrewsville"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/kukulich"}], "description": "Library emulating the PHP internal reflection using just the tokenized source code.", "homepage": "http://andrewsville.github.com/PHP-Token-Reflection/", "keywords": ["library", "reflection", "tokenizer"], "time": "2014-08-06 16:37:08"}, {"name": "apigen/apigen", "version": "v4.0.1", "source": {"type": "git", "url": "https://github.com/ApiGen/ApiGen.git", "reference": "b719a6ad49107b5bc0bdf9fc8fae219a9a6dd5f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ApiGen/ApiGen/zipball/b719a6ad49107b5bc0bdf9fc8fae219a9a6dd5f1", "reference": "b719a6ad49107b5bc0bdf9fc8fae219a9a6dd5f1", "shasum": ""}, "require": {"andrewsville/php-token-reflection": "~1.4", "herrera-io/phar-update": "~2.0", "kdyby/events": "~2.0", "kukulich/fshl": "~2.1", "latte/latte": "~2.2", "michelf/php-markdown": "~1.4", "nette/application": "~2.2", "nette/bootstrap": "~2.2", "nette/di": "~2.2", "nette/mail": "~2.2", "nette/neon": "~2.2", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "php": ">=5.4", "symfony/console": "~2.6", "symfony/options-resolver": "~2.6.1", "tracy/tracy": "~2.2"}, "require-dev": {"jakub-onderka/php-parallel-lint": "~0.8", "mockery/mockery": "~0.9", "phpunit/phpunit": "~4.4", "zenify/coding-standard": "~3.0"}, "bin": ["bin/apigen"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.0-dev"}}, "autoload": {"psr-4": {"ApiGen\\": "src/ApiGen"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Ondřej Nešpor", "homepage": "https://github.com/andrewsville"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/kukulich"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://github.com/olvlvl"}], "description": "PHP source code API generator", "homepage": "http://apigen.org/", "keywords": ["api", "documentation", "generator", "phpdoc"], "time": "2015-03-09 11:03:45"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14 21:17:01"}, {"name": "guzzle/guzzle", "version": "v3.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle3.git", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle3/zipball/0645b70d953bc1c067bbc8d5bc53194706b628d9", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.3", "symfony/event-dispatcher": "~2.1"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}, "require-dev": {"doctrine/cache": "~1.3", "monolog/monolog": "~1.0", "phpunit/phpunit": "3.7.*", "psr/log": "~1.0", "symfony/class-loader": "~2.1", "zendframework/zend-cache": "2.*,<2.3", "zendframework/zend-log": "2.*,<2.3"}, "suggest": {"guzzlehttp/guzzle": "Guzzle 5 has moved to a new package name. The package you have installed, Guzzle 3, is deprecated."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "description": "PHP HTTP client. This library is deprecated in favor of https://packagist.org/packages/guzzlehttp/guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2015-03-18 18:23:50"}, {"name": "herrera-io/json", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-json.git", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-json/zipball/60c696c9370a1e5136816ca557c17f82a6fa83f1", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1", "shasum": ""}, "require": {"ext-json": "*", "justinrainbow/json-schema": ">=1.0,<2.0-dev", "php": ">=5.3.3", "seld/jsonlint": ">=1.0,<2.0-dev"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/lib/json_version.php"], "psr-0": {"Herrera\\Json": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io"}], "description": "A library for simplifying JSON linting and validation.", "homepage": "http://herrera-io.github.com/php-json", "keywords": ["json", "lint", "schema", "validate"], "time": "2013-10-30 16:51:34"}, {"name": "herrera-io/phar-update", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-phar-update.git", "reference": "15643c90d3d43620a4f45c910e6afb7a0ad4b488"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-phar-update/zipball/15643c90d3d43620a4f45c910e6afb7a0ad4b488", "reference": "15643c90d3d43620a4f45c910e6afb7a0ad4b488", "shasum": ""}, "require": {"herrera-io/json": "1.*", "herrera-io/version": "1.*", "php": ">=5.3.3"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"files": ["src/lib/constants.php"], "psr-0": {"Herrera\\Phar\\Update": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io"}], "description": "A library for self-updating <PERSON><PERSON>.", "homepage": "http://herrera-io.github.com/php-phar-update", "keywords": ["phar", "update"], "time": "2013-11-09 17:13:13"}, {"name": "herrera-io/version", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-version.git", "reference": "d39d9642b92a04d8b8a28b871b797a35a2545e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-version/zipball/d39d9642b92a04d8b8a28b871b797a35a2545e85", "reference": "d39d9642b92a04d8b8a28b871b797a35a2545e85", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Herrera\\Version": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io"}], "description": "A library for creating, editing, and comparing semantic versioning numbers.", "homepage": "http://github.com/herrera-io/php-version", "keywords": ["semantic", "version"], "time": "2014-05-27 05:29:25"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "8dc9b9d85ab639ca60ab4608b34c1279d6ae7bce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/8dc9b9d85ab639ca60ab4608b34c1279d6ae7bce", "reference": "8dc9b9d85ab639ca60ab4608b34c1279d6ae7bce", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"json-schema/json-schema-test-suite": "1.1.0", "phpdocumentor/phpdocumentor": "~2", "phpunit/phpunit": "~3.7"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"JsonSchema": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2015-07-14 16:29:50"}, {"name": "kdyby/events", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/Kdyby/Events.git", "reference": "8049e0fc7abb48178b4a2a9af230eceebe1a83bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Kdyby/Events/zipball/8049e0fc7abb48178b4a2a9af230eceebe1a83bc", "reference": "8049e0fc7abb48178b4a2a9af230eceebe1a83bc", "shasum": ""}, "require": {"nette/di": "~2.3@dev", "nette/utils": "~2.3@dev"}, "require-dev": {"jakub-onderka/php-parallel-lint": "~0.7", "latte/latte": "~2.3@dev", "nette/application": "~2.3@dev", "nette/bootstrap": "~2.3@dev", "nette/caching": "~2.3@dev", "nette/component-model": "~2.2@dev", "nette/database": "~2.3@dev", "nette/deprecated": "~2.3@dev", "nette/di": "~2.3@dev", "nette/finder": "~2.3@dev", "nette/forms": "~2.3@dev", "nette/http": "~2.3@dev", "nette/mail": "~2.3@dev", "nette/neon": "~2.3@dev", "nette/nette": "~2.3@dev", "nette/php-generator": "~2.3@dev", "nette/reflection": "~2.3@dev", "nette/robot-loader": "~2.3@dev", "nette/safe-stream": "~2.3@dev", "nette/security": "~2.3@dev", "nette/tester": "~1.4@rc", "nette/tokenizer": "~2.2@dev", "nette/utils": "~2.3@dev", "symfony/event-dispatcher": "~2.5", "tracy/tracy": "~2.3@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-0": {"Kdyby\\Events\\": "src/"}, "classmap": ["src/Kdyby/Events/exceptions.php"], "files": ["src/Doctrine/compatibility.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://filip-prochazka.com"}], "description": "Events for Nette Framework", "homepage": "http://kdyby.org", "keywords": ["kdyby", "nette"], "time": "2015-04-04 16:29:31"}, {"name": "klarna/apigen-theme", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/klarna/klarna-apigen-theme.git", "reference": "1ffcd0e4ea5620d50dbfb74639e4aa4113924911"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/klarna/klarna-apigen-theme/zipball/1ffcd0e4ea5620d50dbfb74639e4aa4113924911", "reference": "1ffcd0e4ea5620d50dbfb74639e4aa4113924911", "shasum": ""}, "require": {"latte/latte": "~2.2"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://github.com/olvlvl"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON><PERSON> theme for ApiGen", "homepage": "http://klarna.com/", "time": "2015-03-30 15:27:12"}, {"name": "kukulich/fshl", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/kukulich/fshl.git", "reference": "974c294ade5d76c0c16b6fe3fd3a584ba999b24f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kukulich/fshl/zipball/974c294ade5d76c0c16b6fe3fd3a584ba999b24f", "reference": "974c294ade5d76c0c16b6fe3fd3a584ba999b24f", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "autoload": {"psr-0": {"FSHL": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/kukulich"}], "description": "FSHL is a free, open source, universal, fast syntax highlighter written in PHP.", "homepage": "http://fshl.kukulich.cz/", "keywords": ["highlight", "library", "syntax"], "time": "2012-09-08 19:00:07"}, {"name": "latte/latte", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "025883a04b3a5ca48995246e8d82e6ff323d941e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/025883a04b3a5ca48995246e8d82e6ff323d941e", "reference": "025883a04b3a5ca48995246e8d82e6ff323d941e", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.1"}, "require-dev": {"nette/tester": "~1.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-mbstring": "to use filters like lower, upper, capitalize, ..."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Latte: the amazing template engine for PHP", "homepage": "http://latte.nette.org", "keywords": ["templating", "twig"], "time": "2015-07-03 13:37:59"}, {"name": "michelf/php-markdown", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/michelf/php-markdown.git", "reference": "e1aabe18173231ebcefc90e615565742fc1c7fd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/michelf/php-markdown/zipball/e1aabe18173231ebcefc90e615565742fc1c7fd9", "reference": "e1aabe18173231ebcefc90e615565742fc1c7fd9", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-lib": "1.4.x-dev"}}, "autoload": {"psr-0": {"Michelf": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://daringfireball.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://michelf.ca/", "role": "Developer"}], "description": "PHP <PERSON>", "homepage": "https://michelf.ca/projects/php-markdown/", "keywords": ["markdown"], "time": "2015-03-01 12:03:08"}, {"name": "nette/application", "version": "v2.3.4", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "785801e441ef83fa4d75cc47c292afedee9b3e4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/785801e441ef83fa4d75cc47c292afedee9b3e4e", "reference": "785801e441ef83fa4d75cc47c292afedee9b3e4e", "shasum": ""}, "require": {"nette/component-model": "~2.2", "nette/http": "~2.2", "nette/reflection": "~2.2", "nette/security": "~2.2", "nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.3.0", "nette/di": "~2.3", "nette/forms": "~2.2", "nette/robot-loader": "~2.2", "nette/tester": "~1.3"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Application MVC Component", "homepage": "http://nette.org", "time": "2015-07-01 15:54:47"}, {"name": "nette/bootstrap", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "8e2db45c39a1fa24f88e94c7b2a62ad09e9a306e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/8e2db45c39a1fa24f88e94c7b2a62ad09e9a306e", "reference": "8e2db45c39a1fa24f88e94c7b2a62ad09e9a306e", "shasum": ""}, "require": {"nette/di": "~2.3", "nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.2", "nette/application": "~2.3", "nette/caching": "~2.3", "nette/database": "~2.3", "nette/forms": "~2.3", "nette/http": "~2.3", "nette/mail": "~2.3", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "nette/security": "~2.3", "nette/tester": "~1.3", "tracy/tracy": "~2.3"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableDebugger()"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Bootstrap", "homepage": "http://nette.org", "time": "2015-07-11 21:07:11"}, {"name": "nette/caching", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "1523a0d2596193dd792045a07e89795eeef435db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/1523a0d2596193dd792045a07e89795eeef435db", "reference": "1523a0d2596193dd792045a07e89795eeef435db", "shasum": ""}, "require": {"nette/finder": "~2.2", "nette/utils": "~2.2", "php": ">=5.4.4"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.4", "nette/di": "~2.3", "nette/tester": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Caching Component", "homepage": "http://nette.org", "time": "2015-06-15 16:32:59"}, {"name": "nette/component-model", "version": "v2.2.3", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "fe13e630a307ef4585b3573eae0a161dc1c3d428"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/fe13e630a307ef4585b3573eae0a161dc1c3d428", "reference": "fe13e630a307ef4585b3573eae0a161dc1c3d428", "shasum": ""}, "require": {"nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Component Model", "homepage": "http://nette.org", "time": "2015-07-11 21:11:20"}, {"name": "nette/di", "version": "v2.3.5", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "c3e726f8bef49033ba78efe19e999e5fac63f433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/c3e726f8bef49033ba78efe19e999e5fac63f433", "reference": "c3e726f8bef49033ba78efe19e999e5fac63f433", "shasum": ""}, "require": {"nette/neon": "~2.3", "nette/php-generator": "~2.3", "nette/utils": "~2.3", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Dependency Injection Component", "homepage": "http://nette.org", "time": "2015-07-13 22:28:49"}, {"name": "nette/finder", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "38f803a03f4cddf352e28af70294c71f7026e516"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/38f803a03f4cddf352e28af70294c71f7026e516", "reference": "38f803a03f4cddf352e28af70294c71f7026e516", "shasum": ""}, "require": {"nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Finder: Files Searching", "homepage": "http://nette.org", "time": "2015-07-11 21:13:50"}, {"name": "nette/http", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "ff2e4608391bca2444df9af6eaf8666ac853eb02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/ff2e4608391bca2444df9af6eaf8666ac853eb02", "reference": "ff2e4608391bca2444df9af6eaf8666ac853eb02", "shasum": ""}, "require": {"nette/utils": "~2.2, >=2.2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.3", "nette/tester": "~1.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette HTTP Component", "homepage": "http://nette.org", "time": "2015-07-19 16:17:50"}, {"name": "nette/mail", "version": "v2.3.2", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "2c6c64787edf8131ec5e1b514ecc4a80a6477f80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/2c6c64787edf8131ec5e1b514ecc4a80a6477f80", "reference": "2c6c64787edf8131ec5e1b514ecc4a80a6477f80", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.3", "nette/tester": "~1.3"}, "suggest": {"ext-fileinfo": "to detect type of attached files"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Mail: Sending E-mails", "homepage": "http://nette.org", "time": "2015-07-03 13:31:38"}, {"name": "nette/neon", "version": "v2.3.2", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "48b25e0962d70e9125797e67a144f3e057243d9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/48b25e0962d70e9125797e67a144f3e057243d9d", "reference": "48b25e0962d70e9125797e67a144f3e057243d9d", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.3.1"}, "require-dev": {"nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette NEON: parser & generator for Nette Object Notation", "homepage": "http://ne-on.org", "time": "2015-07-13 22:29:08"}, {"name": "nette/php-generator", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "c47ad59b972e8a5f4bb00299951bd8053a8fc074"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/c47ad59b972e8a5f4bb00299951bd8053a8fc074", "reference": "c47ad59b972e8a5f4bb00299951bd8053a8fc074", "shasum": ""}, "require": {"nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette PHP Generator", "homepage": "http://nette.org", "time": "2015-07-11 21:19:33"}, {"name": "nette/reflection", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/reflection.git", "reference": "9c2ed2a29f1f58125a0f19ffc987812d6b17d3e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/reflection/zipball/9c2ed2a29f1f58125a0f19ffc987812d6b17d3e6", "reference": "9c2ed2a29f1f58125a0f19ffc987812d6b17d3e6", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "~2.2", "nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.3", "nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette PHP Reflection Component", "homepage": "http://nette.org", "time": "2015-07-11 21:34:53"}, {"name": "nette/robot-loader", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "69331d359bbc9e5f911c12b82187cac914d983fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/69331d359bbc9e5f911c12b82187cac914d983fb", "reference": "69331d359bbc9e5f911c12b82187cac914d983fb", "shasum": ""}, "require": {"nette/caching": "~2.2", "nette/finder": "~2.3", "nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette RobotLoader: comfortable autoloading", "homepage": "http://nette.org", "time": "2015-07-11 21:20:57"}, {"name": "nette/safe-stream", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "bf30db367b51a0932c44dcb9a378927644d48b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/bf30db367b51a0932c44dcb9a378927644d48b2e", "reference": "bf30db367b51a0932c44dcb9a378927644d48b2e", "shasum": ""}, "require": {"php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette SafeStream: Atomic Operations", "homepage": "http://nette.org", "time": "2015-07-11 20:59:15"}, {"name": "nette/security", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "744264a42b506d63009d7e3853ed72b04c99e964"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/744264a42b506d63009d7e3853ed72b04c99e964", "reference": "744264a42b506d63009d7e3853ed72b04c99e964", "shasum": ""}, "require": {"nette/utils": "~2.2", "php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.3", "nette/http": "~2.3", "nette/tester": "~1.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Security: Access Control Component", "homepage": "http://nette.org", "time": "2015-07-11 21:22:53"}, {"name": "nette/utils", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb", "reference": "ff80fce39fdc381e7e0db6cc5ffc82162e59f6bb", "shasum": ""}, "require": {"php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.0"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-mbstring": "to use Strings::lower() etc..."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Nette Utility Classes", "homepage": "http://nette.org", "time": "2015-07-13 22:30:00"}, {"name": "pdepend/pdepend", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/pdepend/pdepend.git", "reference": "a15ffcbfbcc4570d4a733ca7b76e9cac0a56c3f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pdepend/pdepend/zipball/a15ffcbfbcc4570d4a733ca7b76e9cac0a56c3f4", "reference": "a15ffcbfbcc4570d4a733ca7b76e9cac0a56c3f4", "shasum": ""}, "require": {"symfony/config": ">=2.4", "symfony/dependency-injection": ">=2.4", "symfony/filesystem": ">=2.4"}, "require-dev": {"phpunit/phpunit": "4.*@stable", "squizlabs/php_codesniffer": "@stable"}, "bin": ["src/bin/pdepend"], "type": "library", "autoload": {"psr-0": {"PDepend\\": "src/main/php/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Official version of pdepend to be handled with Composer", "time": "2015-03-02 08:06:43"}, {"name": "phploc/phploc", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phploc.git", "reference": "322ad07c112d5c6832abed4269d648cacff5959b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phploc/zipball/322ad07c112d5c6832abed4269d648cacff5959b", "reference": "322ad07c112d5c6832abed4269d648cacff5959b", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/finder-facade": "~1.1", "sebastian/git": "~1.0", "sebastian/version": "~1.0", "symfony/console": "~2.2"}, "bin": ["phploc"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "A tool for quickly measuring the size of a PHP project.", "homepage": "https://github.com/sebastian<PERSON>mann/phploc", "time": "2014-06-25 08:11:02"}, {"name": "phpmd/phpmd", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/phpmd/phpmd.git", "reference": "1a485d9db869137af5e9678bd844568c92998b25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpmd/phpmd/zipball/1a485d9db869137af5e9678bd844568c92998b25", "reference": "1a485d9db869137af5e9678bd844568c92998b25", "shasum": ""}, "require": {"pdepend/pdepend": "2.0.*", "php": ">=5.3.0", "symfony/config": "2.5.*", "symfony/dependency-injection": "2.5.*", "symfony/filesystem": "2.5.*"}, "bin": ["src/bin/phpmd"], "type": "library", "autoload": {"psr-0": {"PHPMD\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Official version of PHPMD handled with Composer.", "time": "2014-09-25 15:56:22"}, {"name": "phpunit/php-code-coverage", "version": "2.1.9", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "5bd48b86cd282da411bb80baac1398ce3fefac41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5bd48b86cd282da411bb80baac1398ce3fefac41", "reference": "5bd48b86cd282da411bb80baac1398ce3fefac41", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.3", "sebastian/environment": "~1.0", "sebastian/version": "~1.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "~4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2015-07-26 12:54:47"}, {"name": "phpunit/php-file-iterator", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/acd690379117b042d1c8af1fafd61bde001bf6bb", "reference": "acd690379117b042d1c8af1fafd61bde001bf6bb", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["File/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": [""], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2013-10-10 15:34:57"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21 13:50:34"}, {"name": "phpunit/php-timer", "version": "1.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3e82f4e9fc92665fafd9157568e4dcb01d014e5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3e82f4e9fc92665fafd9157568e4dcb01d014e5b", "reference": "3e82f4e9fc92665fafd9157568e4dcb01d014e5b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2015-06-21 08:01:12"}, {"name": "phpunit/php-token-stream", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "7a9b0969488c3c54fd62b4d504b3ec758fd005d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/7a9b0969488c3c54fd62b4d504b3ec758fd005d9", "reference": "7a9b0969488c3c54fd62b4d504b3ec758fd005d9", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2015-06-19 03:43:16"}, {"name": "phpunit/phpunit", "version": "4.2.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "c28a790620fe30b049bb693be1ef9cd4e0fe906c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/c28a790620fe30b049bb693be1ef9cd4e0fe906c", "reference": "c28a790620fe30b049bb693be1ef9cd4e0fe906c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpunit/php-code-coverage": "~2.0", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "~1.0.2", "phpunit/phpunit-mock-objects": "~2.2", "sebastian/comparator": "~1.0", "sebastian/diff": "~1.1", "sebastian/environment": "~1.0", "sebastian/exporter": "~1.0", "sebastian/version": "~1.0", "symfony/yaml": "~2.0"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["", "../../symfony/yaml/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "http://www.phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2014-09-14 09:31:24"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.3.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "18dfbcb81d05e2296c0bcddd4db96cade75e6f42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/18dfbcb81d05e2296c0bcddd4db96cade75e6f42", "reference": "18dfbcb81d05e2296c0bcddd4db96cade75e6f42", "shasum": ""}, "require": {"doctrine/instantiator": "~1.0,>=1.0.2", "php": ">=5.3.3", "phpunit/php-text-template": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2015-07-10 06:54:24"}, {"name": "psr/log", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe0936ee26643249e916849d48e3a51d5f5e278b", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Psr\\Log\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "keywords": ["log", "psr", "psr-3"], "time": "2012-12-21 11:40:51"}, {"name": "satooshi/php-coveralls", "version": "v0.6.1", "source": {"type": "git", "url": "https://github.com/satooshi/php-coveralls.git", "reference": "dd0df95bd37a7cf5c5c50304dfe260ffe4b50760"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/satooshi/php-coveralls/zipball/dd0df95bd37a7cf5c5c50304dfe260ffe4b50760", "reference": "dd0df95bd37a7cf5c5c50304dfe260ffe4b50760", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-simplexml": "*", "guzzle/guzzle": ">=3.0", "php": ">=5.3", "psr/log": "1.0.0", "symfony/config": ">=2.0", "symfony/console": ">=2.0", "symfony/stopwatch": ">=2.2", "symfony/yaml": ">=2.0"}, "require-dev": {"apigen/apigen": "2.8.*@stable", "pdepend/pdepend": "dev-master", "phpmd/phpmd": "dev-master", "phpunit/php-invoker": ">=1.1.0,<1.2.0", "phpunit/phpunit": "3.7.*@stable", "sebastian/finder-facade": "dev-master", "sebastian/phpcpd": "1.4.*@stable", "squizlabs/php_codesniffer": "1.4.*@stable", "theseer/fdomdocument": "dev-master"}, "bin": ["composer/bin/coveralls"], "type": "library", "autoload": {"psr-0": {"Contrib\\Component": "src/", "Contrib\\Bundle": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.facebook.com/satooshi.jp"}], "description": "PHP client library for Coveralls API", "homepage": "https://github.com/satooshi/php-coveralls", "keywords": ["ci", "coverage", "github", "test"], "time": "2013-05-04 08:07:33"}, {"name": "sebastian/comparator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/937efb279bd37a375bcadf584dec0726f84dbf22", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2015-07-26 15:48:44"}, {"name": "sebastian/diff", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "863df9687835c62aa423a22412d26fa2ebde3fd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/863df9687835c62aa423a22412d26fa2ebde3fd3", "reference": "863df9687835c62aa423a22412d26fa2ebde3fd3", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "http://www.github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2015-02-22 15:13:53"}, {"name": "sebastian/environment", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "4fe0a44cddd8cc19583a024bdc7374eb2fef0b87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/4fe0a44cddd8cc19583a024bdc7374eb2fef0b87", "reference": "4fe0a44cddd8cc19583a024bdc7374eb2fef0b87", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2015-07-26 06:42:57"}, {"name": "sebastian/exporter", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "7ae5513327cb536431847bcc0c10edba2701064e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/7ae5513327cb536431847bcc0c10edba2701064e", "reference": "7ae5513327cb536431847bcc0c10edba2701064e", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2015-06-21 07:55:53"}, {"name": "sebastian/finder-facade", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/finder-facade.git", "reference": "a520dcc3dd39160eea480daa3426f4fd419a327b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON><PERSON>/finder-facade/zipball/a520dcc3dd39160eea480daa3426f4fd419a327b", "reference": "a520dcc3dd39160eea480daa3426f4fd419a327b", "shasum": ""}, "require": {"symfony/finder": "~2.3", "theseer/fdomdocument": "~1.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FinderFacade is a convenience wrapper for Symfony's Finder component.", "homepage": "https://github.com/sebastian<PERSON>mann/finder-facade", "time": "2015-06-04 08:11:58"}, {"name": "sebastian/git", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/git.git", "reference": "a99fbc102e982c1404041ef3e4d431562b29bcba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/git/zipball/a99fbc102e982c1404041ef3e4d431562b29bcba", "reference": "a99fbc102e982c1404041ef3e4d431562b29bcba", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple wrapper for Git", "homepage": "http://www.github.com/sebastianbergmann/git", "keywords": ["git"], "time": "2013-08-04 09:35:29"}, {"name": "sebastian/phpcpd", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpcpd.git", "reference": "d3ad100fdf15805495f6ff19f473f4314c99390c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpcpd/zipball/d3ad100fdf15805495f6ff19f473f4314c99390c", "reference": "d3ad100fdf15805495f6ff19f473f4314c99390c", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-timer": "~1.0", "sebastian/finder-facade": "~1.1", "sebastian/version": "~1.0", "symfony/console": "~2.2", "theseer/fdomdocument": "~1.4"}, "bin": ["phpcpd"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Copy/Paste Detector (CPD) for PHP code.", "homepage": "https://github.com/sebastian<PERSON>mann/phpcpd", "time": "2015-03-26 14:47:38"}, {"name": "sebastian/recursion-context", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "994d4a811bafe801fb06dccbee797863ba2792ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/994d4a811bafe801fb06dccbee797863ba2792ba", "reference": "994d4a811bafe801fb06dccbee797863ba2792ba", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2015-06-21 08:04:50"}, {"name": "sebastian/version", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2015-06-21 13:59:46"}, {"name": "seld/jsonlint", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "863ae85c6d3ef60ca49cb12bd051c4a0648c40c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/863ae85c6d3ef60ca49cb12bd051c4a0648c40c4", "reference": "863ae85c6d3ef60ca49cb12bd051c4a0648c40c4", "shasum": ""}, "require": {"php": ">=5.3.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2015-01-04 21:18:15"}, {"name": "squizlabs/php_codesniffer", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "6f3e42d311b882b25b4d409d23a289f4d3b803d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/6f3e42d311b882b25b4d409d23a289f4d3b803d5", "reference": "6f3e42d311b882b25b4d409d23a289f4d3b803d5", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.1.2"}, "suggest": {"phpunit/php-timer": "dev-master"}, "bin": ["scripts/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-phpcs-fixer": "2.0.x-dev"}}, "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/CommentParser/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenises PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "http://www.squizlabs.com/php-codesniffer", "keywords": ["phpcs", "standards"], "time": "2014-12-04 22:32:15"}, {"name": "symfony/config", "version": "v2.5.12", "target-dir": "Symfony/Component/Config", "source": {"type": "git", "url": "https://github.com/symfony/Config.git", "reference": "c7309e33b719433d5cf3845d0b5b9608609d8c8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Config/zipball/c7309e33b719433d5cf3845d0b5b9608609d8c8e", "reference": "c7309e33b719433d5cf3845d0b5b9608609d8c8e", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/filesystem": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\Config\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony Config Component", "homepage": "http://symfony.com", "time": "2015-01-03 08:01:13"}, {"name": "symfony/console", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/symfony/Console.git", "reference": "8cf484449130cabfd98dcb4694ca9945802a21ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Console/zipball/8cf484449130cabfd98dcb4694ca9945802a21ed", "reference": "8cf484449130cabfd98dcb4694ca9945802a21ed", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.1", "symfony/phpunit-bridge": "~2.7", "symfony/process": "~2.1"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2015-07-09 16:07:40"}, {"name": "symfony/dependency-injection", "version": "v2.5.12", "target-dir": "Symfony/Component/DependencyInjection", "source": {"type": "git", "url": "https://github.com/symfony/DependencyInjection.git", "reference": "c42aee05b466cc9c66b87ddf7d263402befb6962"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/DependencyInjection/zipball/c42aee05b466cc9c66b87ddf7d263402befb6962", "reference": "c42aee05b466cc9c66b87ddf7d263402befb6962", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"symfony/config": "~2.2", "symfony/expression-language": "~2.4,>=2.4.10", "symfony/yaml": "~2.1"}, "suggest": {"symfony/config": "", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\DependencyInjection\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DependencyInjection Component", "homepage": "http://symfony.com", "time": "2015-01-25 04:37:39"}, {"name": "symfony/event-dispatcher", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/symfony/EventDispatcher.git", "reference": "9310b5f9a87ec2ea75d20fec0b0017c77c66dac3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/EventDispatcher/zipball/9310b5f9a87ec2ea75d20fec0b0017c77c66dac3", "reference": "9310b5f9a87ec2ea75d20fec0b0017c77c66dac3", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.0,>=2.0.5", "symfony/dependency-injection": "~2.6", "symfony/expression-language": "~2.6", "symfony/phpunit-bridge": "~2.7", "symfony/stopwatch": "~2.3"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2015-06-18 19:21:56"}, {"name": "symfony/filesystem", "version": "v2.5.12", "target-dir": "Symfony/Component/Filesystem", "source": {"type": "git", "url": "https://github.com/symfony/Filesystem.git", "reference": "d3c24d7d6e9c342008d8421b2fade460311647ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Filesystem/zipball/d3c24d7d6e9c342008d8421b2fade460311647ea", "reference": "d3c24d7d6e9c342008d8421b2fade460311647ea", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\Filesystem\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony Filesystem Component", "homepage": "http://symfony.com", "time": "2015-01-03 21:04:44"}, {"name": "symfony/finder", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/symfony/Finder.git", "reference": "ae0f363277485094edc04c9f3cbe595b183b78e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Finder/zipball/ae0f363277485094edc04c9f3cbe595b183b78e4", "reference": "ae0f363277485094edc04c9f3cbe595b183b78e4", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2015-07-09 16:07:40"}, {"name": "symfony/options-resolver", "version": "v2.6.11", "target-dir": "Symfony/Component/OptionsResolver", "source": {"type": "git", "url": "https://github.com/symfony/OptionsResolver.git", "reference": "31e56594cee489e9a235b852228b0598b52101c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/OptionsResolver/zipball/31e56594cee489e9a235b852228b0598b52101c1", "reference": "31e56594cee489e9a235b852228b0598b52101c1", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-0": {"Symfony\\Component\\OptionsResolver\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2015-05-13 11:33:56"}, {"name": "symfony/stopwatch", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/symfony/Stopwatch.git", "reference": "b07a866719bbac5294c67773340f97b871733310"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Stopwatch/zipball/b07a866719bbac5294c67773340f97b871733310", "reference": "b07a866719bbac5294c67773340f97b871733310", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2015-07-01 18:23:16"}, {"name": "symfony/yaml", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/symfony/Yaml.git", "reference": "4bfbe0ed3909bfddd75b70c094391ec1f142f860"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/Yaml/zipball/4bfbe0ed3909bfddd75b70c094391ec1f142f860", "reference": "4bfbe0ed3909bfddd75b70c094391ec1f142f860", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2015-07-01 11:25:50"}, {"name": "theseer/fdomdocument", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/theseer/fDOMDocument.git", "reference": "d9ad139d6c2e8edf5e313ffbe37ff13344cf0684"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/fDOMDocument/zipball/d9ad139d6c2e8edf5e313ffbe37ff13344cf0684", "reference": "d9ad139d6c2e8edf5e313ffbe37ff13344cf0684", "shasum": ""}, "require": {"ext-dom": "*", "lib-libxml": "*", "php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The classes contained within this repository extend the standard DOM to use exceptions at all occasions of errors instead of PHP warnings or notices. They also add various custom methods and shortcuts for convenience and to simplify the usage of DOM.", "homepage": "https://github.com/theseer/fDOMDocument", "time": "2015-05-27 22:58:02"}, {"name": "tracy/tracy", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "17d15b8dc83cab3bc5022a45d32c681a76cc19e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/17d15b8dc83cab3bc5022a45d32c681a76cc19e1", "reference": "17d15b8dc83cab3bc5022a45d32c681a76cc19e1", "shasum": ""}, "require": {"php": ">=5.3.1"}, "require-dev": {"nette/di": "~2.3", "nette/tester": "~1.3"}, "type": "library", "autoload": {"classmap": ["src"], "files": ["src/shortcuts.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "http://davidgrudl.com"}, {"name": "Nette Community", "homepage": "http://nette.org/contributors"}], "description": "Tracy: useful PHP debugger", "homepage": "http://tracy.nette.org", "keywords": ["debug", "debugger", "nette"], "time": "2015-07-03 12:52:35"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": []}
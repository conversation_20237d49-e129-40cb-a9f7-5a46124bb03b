<?xml version="1.0"?>
<ruleset name="Klarna PHPMD Ruleset"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation=" http://pmd.sf.net/ruleset_xml_schema.xsd"
>
    <description>Klarna PHPMD ruleset</description>

    <rule ref="rulesets/cleancode.xml"/>

    <rule ref="rulesets/codesize.xml">
        <exclude name="TooManyMethods"/>
    </rule>

    <rule ref="rulesets/codesize.xml/TooManyMethods">
        <priority>1</priority>
        <properties>
            <property name="maxmethods" value="15" />
        </properties>
    </rule>

    <rule ref="rulesets/controversial.xml"/>

    <rule ref="rulesets/design.xml"/>

    <rule ref="rulesets/naming.xml">
        <exclude name="ShortVariable"/>
    </rule>

    <rule ref="rulesets/unusedcode.xml"/>
</ruleset>

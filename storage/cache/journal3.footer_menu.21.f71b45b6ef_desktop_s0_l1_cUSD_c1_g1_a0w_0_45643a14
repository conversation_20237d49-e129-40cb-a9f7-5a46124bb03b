<?php $val = array (
  'css' => 'footer>div{background:rgba(44, 54, 64, 1)}footer .grid-row-2{padding:15px;border-width:0;border-top-width:1px;border-style:solid;border-color:rgba(58, 71, 84, 1)}footer .grid-row-2::before{display:block;left:0;width:100vw}footer .grid-row-2 .grid-col-1{width:50%}footer .grid-row-2 .grid-col-1 .grid-item{height:auto}@media (max-width: 760px){footer .grid-row-2 .grid-col-1{width:100%;margin-bottom:10px}}footer .grid-row-2 .grid-col-2{width:50%}footer .grid-row-2 .grid-col-2 .grid-item{height:auto}@media (max-width: 760px){footer .grid-row-2 .grid-col-2{width:100%}}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'footerType' => 'default',
    'grid_classes' => 
    array (
      0 => 'grid-rows',
    ),
    'rows' => 
    array (
      2 => 
      array (
        'classes' => 
        array (
          0 => 'grid-row',
          1 => 'grid-row-2',
        ),
        'columns' => 
        array (
          1 => 
          array (
            'classes' => 
            array (
              0 => 'grid-col',
              1 => 'grid-col-1',
            ),
            'items' => 
            array (
              1 => 
              array (
                'classes' => 
                array (
                  0 => 'grid-item',
                  1 => 'grid-item-1',
                ),
                'item' => 
                array (
                  'id' => '77',
                  'name' => 'Footer - Copyright',
                  'type' => 'links_menu',
                ),
              ),
            ),
          ),
          2 => 
          array (
            'classes' => 
            array (
              0 => 'grid-col',
              1 => 'grid-col-2',
            ),
            'items' => 
            array (
              1 => 
              array (
                'classes' => 
                array (
                  0 => 'grid-item',
                  1 => 'grid-item-1',
                ),
                'item' => 
                array (
                  'id' => '273',
                  'name' => 'Footer Payments',
                  'type' => 'icons_menu',
                ),
              ),
              2 => 
              array (
                'classes' => 
                array (
                  0 => 'grid-item',
                  1 => 'grid-item-2',
                ),
                'item' => 
                array (
                  'id' => '61',
                  'name' => 'Social Icons Footer',
                  'type' => 'icons_menu',
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  ),
);
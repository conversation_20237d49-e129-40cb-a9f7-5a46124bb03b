<?php $val = array (
  'css' => '.module-master_slider-327 .ms-view{overflow:hidden}.module-master_slider-327 .static-text-1{top:0;left:0;right:auto;bottom:auto;transform:none}.module-master_slider-327 .static-text-1
span{transform:scale(calc(100 / 100));transform-origin:center}.module-master_slider-327 .static-text-2{top:auto;left:0;right:auto;bottom:0;transform:translate3d(0, 0, 0)}.module-master_slider-327 .static-text-2
span{transform:scale(calc(100 / 100));transform-origin:center}.desktop .module-master_slider-327 .ms-nav-next{opacity:0}.desktop .module-master_slider-327 .ms-nav-prev{opacity:0}.desktop .module-master_slider-327 .ms-container:hover .ms-nav-next{opacity:1}.desktop .module-master_slider-327 .ms-container:hover .ms-nav-prev{opacity:1}.module-master_slider-327 .ms-nav-next{display:flex;left:auto;right:0;top:50%;bottom:auto;transform:translateY(-100%)}.module-master_slider-327 .ms-nav-prev{display:flex;left:auto;right:0;top:50%;bottom:auto;transform:translateY(0)}.module-master_slider-327 .ms-nav-next::before, .module-master_slider-327 .ms-nav-prev::before{content:\'\\e9b1\' !important;font-family:icomoon !important;color:rgba(51, 51, 51, 1)}.desktop .module-master_slider-327 .ms-nav-next:hover::before, .desktop .module-master_slider-327 .ms-nav-prev:hover::before{color:rgba(255, 255, 255, 1)}.module-master_slider-327 .ms-nav-next, .module-master_slider-327 .ms-nav-prev{width:50px;height:50px;background:rgba(250, 250, 250, 1)}.module-master_slider-327 .ms-nav-next:hover, .module-master_slider-327 .ms-nav-prev:hover{background:rgba(44, 54, 64, 1)}.desktop .module-master_slider-327 .ms-container .ms-bullets{opacity:0}.desktop .module-master_slider-327 .ms-container:hover .ms-bullets{opacity:1}.module-master_slider-327 .ms-bullets{display:block;top:0;bottom:auto;left:50%;right:auto;transform:translateX(-50%);;padding:10px}.module-master_slider-327 .ms-bullets .ms-bullets-count{flex-direction:row}.module-master_slider-327 .ms-bullets .ms-bullet{margin:calc(8px / 2) !important;background:rgba(255, 255, 255, 1);border-radius:10px}.module-master_slider-327 .ms-bullet{width:15px;height:5px}.desktop .module-master_slider-327 .ms-bullets .ms-bullet:hover, .module-master_slider-327 .ms-bullets .ms-bullet-selected{background:rgba(221, 14, 28, 1)}.module-master_slider-327 .ms-thumb-list{display:block;padding:10px
0;top:auto !important;bottom:0 !important;order:2}.module-master_slider-327 .ms-thumb-list .ms-thumb-frame{margin-right:10px !important;opacity: .75}.module-master_slider-327 .ms-thumb-frame{border-width:3px;border-style:solid;border-radius:4px}.module-master_slider-327 .ms-timerbar{display:block;top:0 !important;bottom:auto !important}.module-master_slider-327 .master-slider .ms-time-bar{background-color:rgba(221, 14, 28, 1) !important;height:1px !important}@media (max-width: 1024px){.module-master_slider-327 .ms-nav-next{display:none}.module-master_slider-327 .ms-nav-prev{display:none}}.module-master_slider-327 .module-item-1{background:rgba(219, 232, 242, 1)}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'shuffle' => false,
    'imageDimensions' => 
    array (
      'width' => 1200,
      'height' => 500,
      'resize' => 'fit',
    ),
    'sliderDimensions' => 
    array (
      'width' => NULL,
      'height' => NULL,
      'resize' => 'fill',
    ),
    'parallaxMode' => true,
    'parallax' => '35',
    'layout' => 'fillwidth',
    'layoutTablet' => '',
    'layoutPhone' => '',
    'bottomOffset' => '',
    'bottomOffsetTablet' => '',
    'bottomOffsetPhone' => '',
    'staticTextType' => 'text',
    'staticText' => '',
    'staticTextLink' => 
    array (
      'type' => '',
      'id' => '',
      'href' => '',
      'name' => '',
      'total' => NULL,
      'attrs' => 
      array (
      ),
      'classes' => 
      array (
      ),
    ),
    'static2TextType' => 'text',
    'static2Text' => '',
    'static2TextLink' => 
    array (
      'type' => '',
      'id' => '',
      'href' => '',
      'name' => '',
      'total' => NULL,
      'attrs' => 
      array (
      ),
      'classes' => 
      array (
      ),
    ),
    'delay' => '2000',
    'thumbnails' => false,
    'thumbnailsDimensions' => 
    array (
      'width' => 50,
      'height' => 50,
      'resize' => 'fill',
    ),
    'lazyLoad' => false,
    'status' => true,
    'id' => 'master_slider-68a7900adca19',
    'module_id' => 327,
    'classes' => 
    array (
      0 => 'module',
      1 => 'module-master_slider',
      2 => 'module-master_slider-327',
      'fullscreen-slider' => false,
    ),
    'width' => 1200,
    'height' => 500,
    'options' => 
    array (
      'width' => 1200,
      'height' => 500,
      'layout' => 'fillwidth',
      'smoothHeight' => false,
      'centerControls' => false,
      'parallaxMode' => 'swipe',
      'instantStartLayers' => true,
      'loop' => true,
      'dir' => 'h',
      'autoHeight' => false,
      'rtl' => false,
      'startOnAppear' => false,
      'autoplay' => false,
      'overPause' => true,
      'shuffle' => false,
      'view' => 'fadeWave',
      'speed' => '20',
      'swipe' => true,
      'mouse' => true,
      'controls' => 
      array (
        'arrows' => 
        array (
          'autohide' => false,
        ),
        'bullets' => 
        array (
          'autohide' => false,
        ),
        'timebar' => 
        array (
          'autohide' => false,
          'inset' => true,
          'align' => 'top',
        ),
      ),
    ),
    'first_image' => 'https://easoneyewear.com/image/cache/catalog/slide%20show%201-1220x500w-1200x500w.jpg',
    'first_alt' => '',
    'items' => 
    array (
      1 => 
      array (
        'type' => 'image',
        'alt' => '',
        'videoType' => 'html5',
        'link' => 
        array (
          'type' => '',
          'id' => '',
          'href' => '',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'id' => '327-1',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-1',
          2 => 'ms-slide',
        ),
        'delay' => 2.0,
        'image' => 'https://easoneyewear.com/image/cache/catalog/slide%20show%201-1220x500w-1200x500w.jpg',
        'image2x' => 'https://easoneyewear.com/image/cache/catalog/slide%20show%201-1220x500w-2400x1000w.jpg',
        'thumb' => false,
        'items' => 
        array (
        ),
      ),
    ),
    'first_image2x' => 'https://easoneyewear.com/image/cache/catalog/slide%20show%201-1220x500w-2400x1000w.jpg',
  ),
);
<?php $val = array (
  'css' => '.accordion-menu-126>.j-menu>li>a::before{content:\'\\e93f\' !important;font-family:icomoon !important}.accordion-menu-126.accordion-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-126.accordion-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-126.accordion-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-126.accordion-menu .j-menu>li>a{font-size:13px;color:rgba(51, 51, 51, 1);font-weight:700;text-transform:uppercase;padding:6px;padding-right:0px;padding-left:0px}.desktop .accordion-menu-126.accordion-menu .j-menu > li:hover > a, .accordion-menu-126.accordion-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.accordion-menu-126.accordion-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-126.accordion-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-126.accordion-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-126 .open-menu i::before{content:\'\\eba1\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-126 .open-menu[aria-expanded=\'true\'] i::before{content:\'\\eb86\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-126.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-126.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-126.accordion-menu .j-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-126.accordion-menu .j-menu .j-menu>li>a{font-size:14px;font-weight:400;text-transform:none;padding:5px}.accordion-menu-126.accordion-menu .j-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-126.accordion-menu .j-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-126.accordion-menu .j-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-126 .j-menu > li > div .j-menu>li>a{padding-left:18px !important}.accordion-menu-126 .j-menu > li > div .j-menu>li>div>.j-menu>li>a{padding-left:30px !important}.accordion-menu-126 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:40px !important}.accordion-menu-126 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:50px !important}.accordion-menu-126>.j-menu{border-radius:2px}.accordion-menu-126>.j-menu>li.accordion-menu-item>a::before{min-width:10px;font-size:12px !important;margin-right:5px}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'title' => 'Account Menu',
    'status' => true,
    'id' => 'accordion-menu-68a79f8f755c8',
    'module_id' => 126,
    'classes' => 
    array (
      0 => 'accordion-menu',
      1 => 'accordion-menu-126',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'My Account',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/account',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-1',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      2 => 
      array (
        'title' => 'Address Book',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/address',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-2',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      3 => 
      array (
        'title' => 'Wishlist',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/wishlist',
          'name' => '',
          'total' => '{{ $wishlist }}',
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
            0 => 'wishlist-badge',
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-3',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      4 => 
      array (
        'title' => 'Order History',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/order',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-4',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      5 => 
      array (
        'title' => 'Downloads',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/download',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-5',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      6 => 
      array (
        'title' => 'Recurring Payments',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/recurring',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-6',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      7 => 
      array (
        'title' => 'Reward Points',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/reward',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-7',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      8 => 
      array (
        'title' => 'Returns',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/return/add',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-8',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      9 => 
      array (
        'title' => 'Transactions',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/transaction',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-9',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      10 => 
      array (
        'title' => 'Newsletter',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/newsletter',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-10',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      11 => 
      array (
        'title' => 'Custom Menus',
        'link' => 
        array (
          'type' => '',
          'id' => '',
          'href' => '',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-11',
          'multi-level' => false,
          'dropdown' => true,
          'drop-menu' => false,
        ),
        'items' => 
        array (
          12 => 
          array (
            'title' => 'Add or Remove',
            'link' => 
            array (
              'type' => '',
              'id' => '',
              'href' => '',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'accordion-menu-item-12',
              'dropdown' => false,
            ),
            'items' => 
            array (
            ),
          ),
          13 => 
          array (
            'title' => 'Any Menu Item',
            'link' => 
            array (
              'type' => '',
              'id' => '',
              'href' => '',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'accordion-menu-item-13',
              'dropdown' => false,
            ),
            'items' => 
            array (
            ),
          ),
          14 => 
          array (
            'title' => 'This is a Fully Customizable',
            'link' => 
            array (
              'type' => '',
              'id' => '',
              'href' => '',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'accordion-menu-item-14',
              'dropdown' => false,
            ),
            'items' => 
            array (
            ),
          ),
          15 => 
          array (
            'title' => 'Accordion Menu Module',
            'link' => 
            array (
              'type' => '',
              'id' => '',
              'href' => '',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'accordion-menu-item-15',
              'dropdown' => false,
            ),
            'items' => 
            array (
            ),
          ),
        ),
      ),
    ),
  ),
);
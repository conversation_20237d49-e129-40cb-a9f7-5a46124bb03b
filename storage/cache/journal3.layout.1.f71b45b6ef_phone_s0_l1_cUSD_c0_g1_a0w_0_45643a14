<?php $val = array (
  'settings' => 
  array (
    'column_left' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'column_right' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'top' => 
    array (
      'rows' => 
      array (
        1 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-top-1',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-top-1-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-1-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '327',
                    'name' => 'Home Top Slider Copy',
                    'type' => 'master_slider',
                  ),
                ),
              ),
            ),
          ),
        ),
        2 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-top-2',
          ),
          'columns' => 
          array (
          ),
        ),
        3 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-top-3',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-top-3-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-3-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '201',
                    'name' => 'Specials Banner',
                    'type' => 'banners',
                  ),
                ),
                2 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-3-1-2',
                  ),
                  'item' => 
                  array (
                    'id' => '257',
                    'name' => 'Home Page - Main Products',
                    'type' => 'products',
                  ),
                ),
                3 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-3-1-3',
                  ),
                  'item' => 
                  array (
                    'id' => '333',
                    'name' => 'Home Page - Special',
                    'type' => 'products',
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'header_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'global' => 
    array (
      0 => 
      array (
        'module_id' => '195',
        'module_type' => 'popup',
      ),
      1 => 
      array (
        'module_id' => '56',
        'module_type' => 'header_notice',
      ),
    ),
  ),
  'php' => 
  array (
    'headerDesktop' => '',
    'headerMobile' => '',
    'footerMenu' => '',
    'footerMenuPhone' => '',
  ),
  'js' => 
  array (
  ),
  'fonts' => 
  array (
  ),
  'css' => '.grid-row-top-1{background:rgba(240, 242, 245, 1);box-shadow:0 -15px 100px -10px rgba(0,0,0,0.1)}.grid-row-top-1::before{display:block;left:50%;width:100vw}.grid-row-top-1>.grid-cols{max-width:100% !important}.grid-row-top-1 .wave-top{display:block}.grid-row-top-1 .wave-bottom{display:block}.grid-col-top-1-1{width:100%}.grid-col-top-1-1 .grid-item{height:auto}.grid-col-top-1-1 .grid-items{justify-content:flex-start}.grid-item-top-1-1-1{width:100%}.grid-row-top-2{background:rgba(255,255,255,1);padding:10px;padding-top:40px;padding-bottom:40px}.grid-row-top-2::before{display:block;left:0;width:100vw}.grid-row-top-2 .wave-top{display:block}.grid-row-top-2 .wave-bottom{display:block}@media (max-width: 760px){.grid-row-top-2{padding:10px}}.grid-row-top-3{background:rgba(255,255,255,1);padding:10px;padding-top:40px;padding-bottom:40px}.grid-row-top-3::before{display:block;left:0;width:100vw}.grid-row-top-3 .wave-top{display:block}.grid-row-top-3 .wave-bottom{display:block}@media (max-width: 760px){.grid-row-top-3{padding:10px}}.grid-col-top-3-1{width:100%}.grid-col-top-3-1 .grid-item{height:auto}.grid-col-top-3-1 .grid-items{justify-content:flex-start}.grid-item-top-3-1-1{width:100%}.grid-item-top-3-1-2{width:100%}.grid-item-top-3-1-3{width:100%}',
);
<?php $val = array (
  'css' => '.reset-filter.btn,.reset-filter.btn:visited{font-size:12px;font-weight:400;text-transform:none}.reset-filter.btn{padding:2px;padding-right:6px;padding-left:6px;min-width:20px;min-height:20px}.desktop .reset-filter.btn:hover{box-shadow:0 5px 30px -5px rgba(0,0,0,0.25)}.reset-filter.btn:active,.reset-filter.btn:hover:active,.reset-filter.btn:focus:active{box-shadow:inset 0 0 20px rgba(0,0,0,0.25)}.reset-filter.btn:focus{box-shadow:inset 0 0 20px rgba(0,0,0,0.25)}.reset-filter.btn.btn.disabled::after{font-size:20px}.reset-filter::before{content:\'\\f057\' !important;font-family:icomoon !important;font-size:12px;margin-right:3px}.mobile-filter-trigger.btn{background:rgba(80, 173, 85, 1);border-radius:30px !important}.mobile-filter-trigger.btn:hover{background:rgba(217, 185, 110, 1) !important}.mobile-filter-trigger.btn.btn.disabled::after{font-size:20px}.mobile-filter-trigger::before{content:\'\\e919\' !important;font-family:icomoon !important;margin-right:7px}.mobile-filter-trigger{left:50%;right:auto;transform:translate3d(-50%,0,0)}.module-filter-36 .count-badge{display:inline-flex}.module-filter-36 .extra-controls
input{color:rgba(51, 51, 51, 1) !important;background:rgba(250, 250, 250, 1) !important;border-width:1px !important;border-style:solid !important;border-color:rgba(221, 221, 221, 1) !important;border-radius:2px !important}.module-filter-36 .extra-controls input:focus{background:rgba(255, 255, 255, 1) !important;box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.1)}.module-filter-36 .extra-controls input:hover{border-color:rgba(217, 185, 110, 1) !important;box-shadow:0 5px 20px -5px rgba(0, 0, 0, 0.1)}.module-filter-36 .extra-controls input:focus, .module-filter-36 .extra-controls input:active{border-color:rgba(217, 185, 110, 1) !important}.module-filter-36 .filter-price{padding-right:0px;padding-left:0px}.module-filter-36 .panel-body{max-height:250px;overflow-y:auto}.module-filter-36 label
img{border-width:1px;border-style:solid;border-color:rgba(221, 221, 221, 1)}.module-filter-36 label:hover
img{border-color:rgba(0, 0, 0, 1);box-shadow:0 10px 30px rgba(0, 0, 0, 0.1)}.module-filter-36 label input:checked+img{border-color:rgba(0, 0, 0, 1)}.module-filter-36 .panel-body>div>label{padding-left:2px}.module-filter-36 .image-only label
img{border-width:2px;border-style:solid;border-color:rgba(0, 0, 0, 0);border-radius:50%}.module-filter-36 .image-only label:hover
img{border-color:rgba(0, 0, 0, 1)}.module-filter-36 .image-only label input:checked+img{border-color:rgba(221, 14, 28, 1)}.module-filter-36 .image-only .panel-body>div>label{padding:calc(7px / 2)}.module-filter-36 .irs-line{transform:scaleY(.1);;background:rgba(221, 14, 28, 1)}.module-filter-36 .irs-bar{transform:scaleY(.1);;background:rgba(233, 102, 49, 1)}.module-filter-36 .irs-line, .module-filter-36 .irs-bar{border-radius:10px}.module-filter-36 .irs-slider{background:rgba(221, 14, 28, 1);border-radius:50%;width:15px;height:15px;transform:translateY(5px)}.module-filter-36 .irs-slider:hover{background:rgba(51, 51, 51, 1)}.module-filter-36 .module-item-a10 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a10 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a32 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a32 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a32{order:100}.module-filter-36 .module-item-a32 .count-badge{display:none}.module-filter-36 .module-item-a11 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a11 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a22 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a22 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a33 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a33 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a33{order:100}.module-filter-36 .module-item-a33 .count-badge{display:none}.module-filter-36 .module-item-o1 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-o1 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o1{order:1}.module-filter-36 .module-item-o1 .count-badge{display:inline-flex}.module-filter-36 .module-item-a12 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a12 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a23 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a23 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-c .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-c .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-c{order:100}.module-filter-36 .module-item-c .count-badge{display:none}.module-filter-36 .module-item-o2 .panel-body > div > label
input{display:none}.module-filter-36 .module-item-o2 .panel-body > div > label input+*{margin:0}.module-filter-36 .module-item-o2{order:1}.module-filter-36 .module-item-o2 .count-badge{display:none}.module-filter-36 .module-item-a13 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a13 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a13 .count-badge{display:inline-flex}.module-filter-36 .module-item-a14 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a14 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a15 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a15 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a26 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a26 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o5 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-o5 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o5{order:1}.module-filter-36 .module-item-o5 .count-badge{display:inline-flex}.module-filter-36 .module-item-a27 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a27 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a28 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a28 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a28{order:100}.module-filter-36 .module-item-a28 .count-badge{display:none}.module-filter-36 .module-item-a29 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a29 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a29{order:100}.module-filter-36 .module-item-a29 .count-badge{display:none}.module-filter-36 .module-item-f1 .count-badge{display:inline-flex}.module-filter-36 .module-item-f2 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-f2 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-f2{order:10}.module-filter-36 .module-item-f2 .count-badge{display:none}.module-filter-36 .module-item-o11 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-o11 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o11{order:1}.module-filter-36 .module-item-o11 .count-badge{display:inline-flex}.module-filter-36 .module-item-p .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-p .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-p{order:-2}.module-filter-36 .module-item-a1 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a1 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-q .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-q .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-q{order:-1}.module-filter-36 .module-item-q .count-badge{display:none}.module-filter-36 .module-item-o13 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-o13 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o13{order:100}.module-filter-36 .module-item-o13 .count-badge{display:none}.module-filter-36 .module-item-a2 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a2 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o14 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-o14 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-o14{order:100}.module-filter-36 .module-item-o14 .count-badge{display:none}.module-filter-36 .module-item-a3 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a3 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a4 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a4 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-t .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-t .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-t .count-badge{display:none}.module-filter-36 .module-item-a5 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a5 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a5 .count-badge{display:none}.module-filter-36 .module-item-a7 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a7 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a8 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a8 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a9 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a9 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a30 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a30 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a30{order:100}.module-filter-36 .module-item-a30 .count-badge{display:none}.module-filter-36 .module-item-a31 .panel-body > div > label
input{display:inline-block}.module-filter-36 .module-item-a31 .panel-body > div > label input+*{margin:0
5px}.module-filter-36 .module-item-a31{order:100}.module-filter-36 .module-item-a31 .count-badge{display:none}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'title' => 'Filter',
    'imageDimensions' => 
    array (
      'width' => 42,
      'height' => 42,
      'resize' => 'fit',
    ),
    'input' => 'checkbox',
    'collapsed' => false,
    'options' => true,
    'attributes' => false,
    'filters' => true,
    'filtersCategoryCheck' => false,
    'resetText' => 'Clear',
    'mobileText' => 'Filter Products',
    'status' => true,
    'id' => 'filter-68a787be22594',
    'module_id' => 36,
    'classes' => 
    array (
      0 => 'module',
      1 => 'module-filter',
      2 => 'module-filter-36',
    ),
    'image_width' => 42,
    'image_height' => 42,
    'image_resize' => 'fit',
    'currency_left' => '$',
    'currency_right' => '',
    'items' => 
    array (
      'a10' => 
      array (
        'title' => 'test 7',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a10',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a10',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a32' => 
      array (
        'title' => 'Filter by Room',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a32',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a32',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a11' => 
      array (
        'title' => 'test 8',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a11',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a11',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a22' => 
      array (
        'title' => 'Taste',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a22',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a22',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a33' => 
      array (
        'title' => 'Filter by Room',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a33',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a33',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'o1' => 
      array (
        'title' => 'Radio',
        'display' => 'both',
        'input' => 'radio',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o1',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o1',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a12' => 
      array (
        'title' => 'Occasion',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a12',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a12',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a23' => 
      array (
        'title' => 'Type',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a23',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a23',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'c' => 
      array (
        'title' => 'NEW ARRIVAL',
        'display' => 'text',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-c',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-c',
          'text-only' => true,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'c',
        'key' => 'c',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'o2' => 
      array (
        'title' => 'Color',
        'display' => 'image',
        'input' => 'radio',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o2',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o2',
          'text-only' => false,
          'image-only' => true,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => true,
        'collapsed' => false,
      ),
      'a13' => 
      array (
        'title' => 'Style',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a13',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a13',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a14' => 
      array (
        'title' => 'Dress Length',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a14',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a14',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a15' => 
      array (
        'title' => 'Material',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a15',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a15',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a26' => 
      array (
        'title' => 'Attribute 1',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a26',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a26',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'o5' => 
      array (
        'title' => 'Power',
        'display' => 'text',
        'input' => 'radio',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o5',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o5',
          'text-only' => true,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a27' => 
      array (
        'title' => 'Attribute 2',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a27',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a27',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a28' => 
      array (
        'title' => 'Upholstery',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a28',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a28',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a29' => 
      array (
        'title' => 'Filter by Room',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a29',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a29',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'f1' => 
      array (
        'title' => 'Opencart Filter',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-f1',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-f1',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'f',
        'key' => 'f',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'f2' => 
      array (
        'title' => 'Eyewear Styles',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-f2',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-f2',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'f',
        'key' => 'f',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'o11' => 
      array (
        'title' => 'Size',
        'display' => 'text',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o11',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o11',
          'text-only' => true,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'p' => 
      array (
        'title' => 'Price',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-p',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-p',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'p',
        'key' => 'p',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a1' => 
      array (
        'title' => 'Description',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a1',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a1',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'q' => 
      array (
        'title' => 'Availability',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-q',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-q',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'q',
        'key' => 'q',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'o13' => 
      array (
        'title' => 'Color',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o13',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o13',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a2' => 
      array (
        'title' => 'No. of Cores',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a2',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a2',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'o14' => 
      array (
        'title' => 'Power',
        'display' => 'text',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-o14',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-o14',
          'text-only' => true,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'o',
        'key' => 'o',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a3' => 
      array (
        'title' => 'Clockspeed',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a3',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a3',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a4' => 
      array (
        'title' => 'test 1',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a4',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a4',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      't' => 
      array (
        'title' => 'Tags',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-t',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-t',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 't',
        'key' => 't',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a5' => 
      array (
        'title' => 'test 2',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a5',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a5',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a7' => 
      array (
        'title' => 'test 4',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a7',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a7',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a8' => 
      array (
        'title' => 'test 5',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a8',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a8',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a9' => 
      array (
        'title' => 'test 6',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a9',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a9',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => true,
      ),
      'a30' => 
      array (
        'title' => 'Filter by Room',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a30',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a30',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
      'a31' => 
      array (
        'title' => 'Filter by Room',
        'display' => 'both',
        'input' => '',
        'inStockText' => 'In Stock',
        'outOfStockText' => 'Out of Stock',
        'id' => '36-a31',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-a31',
          'text-only' => false,
          'image-only' => false,
          2 => 'panel',
        ),
        'type' => 'a',
        'key' => 'a',
        'panel_classes' => 
        array (
          0 => 'panel-collapse',
          1 => 'collapse',
        ),
        'image_only' => false,
        'collapsed' => false,
      ),
    ),
  ),
);
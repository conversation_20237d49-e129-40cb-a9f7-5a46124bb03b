<?php $val = array (
  'css' => '.top-menu-14 .j-menu li.top-menu-item-1>a::before{content:\'\\e9a8\' !important;font-family:icomoon !important}.top-menu-14>ul>.top-menu-item-1>a{text-align:left}.top-menu-14>ul>.top-menu-item-1>a>.links-text{display:block}.top-menu-14 > ul > .top-menu-item-1 > a .count-badge{position:relative}.top-menu-14 .j-menu .j-menu li.top-menu-item-2>a::before{content:\'\\e990\' !important;font-family:icomoon !important}.top-menu-14 .j-menu .j-menu li.top-menu-item-3>a::before{content:\'\\e92d\' !important;font-family:icomoon !important}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'scheduledStatus' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'status' => true,
    'id' => 'top-menu-68a788cbd008b',
    'module_id' => 14,
    'classes' => 
    array (
      0 => 'top-menu',
      1 => 'top-menu-14',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'Account <s>Login / Register</s>',
        'link' => 
        array (
          'type' => 'page',
          'id' => '7',
          'href' => 'https://easoneyewear.com/index.php?route=account/login',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-1',
          'multi-level' => false,
          'dropdown' => true,
          'drop-menu' => true,
          'icon-only' => false,
        ),
        'items' => 
        array (
          2 => 
          array (
            'title' => 'Login',
            'link' => 
            array (
              'type' => 'login_popup',
              'id' => '',
              'href' => 'javascript:open_login_popup()',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'top-menu-item-2',
              'dropdown' => false,
              'icon-only' => NULL,
            ),
            'items' => 
            array (
            ),
          ),
          3 => 
          array (
            'title' => 'Register',
            'link' => 
            array (
              'type' => 'register_popup',
              'id' => '',
              'href' => 'javascript:open_register_popup()',
              'name' => '',
              'total' => NULL,
              'attrs' => 
              array (
              ),
              'classes' => 
              array (
              ),
            ),
            'classes' => 
            array (
              0 => 'menu-item',
              1 => 'top-menu-item-3',
              'dropdown' => false,
              'icon-only' => NULL,
            ),
            'items' => 
            array (
            ),
          ),
        ),
      ),
    ),
  ),
);
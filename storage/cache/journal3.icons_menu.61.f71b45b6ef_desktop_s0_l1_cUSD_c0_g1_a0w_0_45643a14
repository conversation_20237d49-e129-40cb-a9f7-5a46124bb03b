<?php $val = array (
  'css' => '.icons-menu-61 .title.module-title{font-family:\'Montserrat\';font-weight:700;font-size:18px;color:rgba(51, 51, 51, 1);margin-bottom:15px;white-space:normal;overflow:visible;text-overflow:initial;text-align:left}.icons-menu-61 .title.module-title::after{display:none;margin-top:10px;left:initial;right:initial;margin-left:0;margin-right:auto;transform:none}.icons-menu-61 .title.module-title.page-title>span::after{display:none;margin-top:10px;left:initial;right:initial;margin-left:0;margin-right:auto;transform:none}.icons-menu-61 .title.module-title::after, .icons-menu-61 .title.module-title.page-title>span::after{width:50px;height:1px;background:rgba(217, 185, 110, 1)}.icons-menu-61
a{background:rgba(51, 51, 51, 1);width:40px;height:40px}.desktop .icons-menu-61 a:hover{background:rgba(217, 185, 110, 1);box-shadow:0 10px 30px rgba(0, 0, 0, 0.1)}.icons-menu-61 a::before{color:rgba(255, 255, 255, 1)}.icons-menu-61 .menu-item
a{border-radius:50%}.icons-menu-61>ul{margin-top:15px}.icons-menu-61 .links-text{white-space:normal;overflow:visible;text-overflow:ellipsis;display:none}.icons-menu-61>ul>.icons-menu-item{padding:calc(10px / 2)}.icons-menu-61
ul{justify-content:flex-start}.icons-menu-61 .module-title{text-align:left}.icons-menu-61 .module-title::after{left:0;right:auto;transform:none}.icons-menu-61 .icons-menu-item-1.icon-menu-icon>a::before{content:\'\\f09a\' !important;font-family:icomoon !important}.icons-menu-61 .icons-menu-item-6.icon-menu-icon>a::before{content:\'\\e9d9\' !important;font-family:icomoon !important}',
  'fonts' => 
  array (
    'fonts' => 
    array (
      'Montserrat' => 
      array (
        700 => '700',
      ),
    ),
    'subsets' => 
    array (
      'latin-ext' => 'latin-ext',
    ),
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'title' => '',
    'tooltipStatus' => true,
    'tooltipPosition' => 'top',
    'imageDimensions' => 
    array (
      'width' => NULL,
      'height' => NULL,
      'resize' => 'fill',
    ),
    'status' => true,
    'id' => 'icons-menu-68a7877dc2323',
    'module_id' => 61,
    'classes' => 
    array (
      0 => 'icons-menu',
      1 => 'icons-menu-61',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'Facebook',
        'link' => 
        array (
          'type' => 'custom',
          'id' => '',
          'href' => 'https://www.facebook.com/easoneyewear1/',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'type' => 'icon',
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'icons-menu-item',
          2 => 'icons-menu-item-1',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          3 => 'icon-menu-icon',
        ),
        'items' => 
        array (
        ),
      ),
      6 => 
      array (
        'title' => 'Pinterest',
        'link' => 
        array (
          'type' => 'custom',
          'id' => '',
          'href' => 'https://www.pinterest.ca/pin/474637248283302668/',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'type' => 'icon',
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'icons-menu-item',
          2 => 'icons-menu-item-6',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          3 => 'icon-menu-icon',
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);
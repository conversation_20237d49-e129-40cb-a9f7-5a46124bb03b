<?php $val = array (
  'css' => '.module-info_blocks-194 .module-item.swiper-slide{margin-right:10px;width:calc((100% - 0 * 10px) / 1 - 0.01px)}.module-info_blocks-194 .module-item:not(.swiper-slide){padding:5px;width:calc(100% / 1 - 0.01px)}.one-column #content .module-info_blocks-194 .module-item.swiper-slide{margin-right:10px;width:calc((100% - 0 * 10px) / 1 - 0.01px)}.one-column #content .module-info_blocks-194 .module-item:not(.swiper-slide){padding:5px;width:calc(100% / 1 - 0.01px)}.two-column #content .module-info_blocks-194 .module-item.swiper-slide{margin-right:10px;width:calc((100% - 0 * 10px) / 1 - 0.01px)}.two-column #content .module-info_blocks-194 .module-item:not(.swiper-slide){padding:5px;width:calc(100% / 1 - 0.01px)}.side-column .module-info_blocks-194 .module-item.swiper-slide{margin-right:10px;width:calc((100% - 0 * 10px) / 1 - 0.01px)}.side-column .module-info_blocks-194 .module-item:not(.swiper-slide){padding:5px;width:calc(100% / 1 - 0.01px)}.module-info_blocks-194 .info-block .info-block-title{font-family:\'Montserrat\';font-weight:700;font-size:14px;text-transform:uppercase;margin-bottom:3px;display:block}.module-info_blocks-194 .info-block .info-block-text{font-size:13px;color:rgba(139, 145, 152, 1);display:block}.module-info_blocks-194 .info-block{border-width:0;border-bottom-width:1px;border-style:solid;border-color:rgba(221, 221, 221, 1);padding:10px;display:flex;flex-direction:row;justify-content:flex-start;text-align:left}.module-info_blocks-194 .info-blocks:last-child .info-block{border-width:0;border-bottom-width:0px}.module-info_blocks-194 .info-block-content{display:flex;justify-content:flex-start}.module-info_blocks-194 .info-block::before{align-self:flex-start;width:45px;height:45px;font-size:24px;color:rgba(217, 185, 110, 1)}.module-info_blocks-194 .info-block-img{align-self:flex-start}.module-info_blocks-194 .info-block::before, .module-info_blocks-194 .info-block-img{margin-right:10px}.module-info_blocks-194 .info-block .count-badge{display:none}.module-info_blocks-194 .module-item-1 .info-block::before{content:\'\\eb7b\' !important;font-family:icomoon !important;font-size:30px}.module-info_blocks-194 .module-item-2 .info-block::before{content:\'\\eaad\' !important;font-family:icomoon !important;font-size:30px}.module-info_blocks-194 .module-item-3 .info-block::before{content:\'\\e159\' !important;font-family:icomoon !important;font-size:30px}',
  'fonts' => 
  array (
    'fonts' => 
    array (
      'Montserrat' => 
      array (
        700 => '700',
      ),
    ),
    'subsets' => 
    array (
      'latin-ext' => 'latin-ext',
    ),
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'itemsPerRow' => 
    array (
      'c0' => 
      array (
        0 => 
        array (
          'items' => 1,
          'spacing' => 10,
        ),
      ),
      'c1' => 
      array (
        0 => 
        array (
          'items' => 1,
          'spacing' => 10,
        ),
      ),
      'c2' => 
      array (
        0 => 
        array (
          'items' => 1,
          'spacing' => 10,
        ),
      ),
      'sc' => 
      array (
        0 => 
        array (
          'items' => 1,
          'spacing' => 10,
        ),
      ),
    ),
    'imageDimensions' => 
    array (
      'width' => NULL,
      'height' => NULL,
      'resize' => 'fill',
    ),
    'status' => true,
    'id' => 'info_blocks-68a78b2ca2f07',
    'module_id' => 194,
    'classes' => 
    array (
      0 => 'module',
      1 => 'module-info_blocks',
      2 => 'module-info_blocks-194',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'Store Address',
        'link' => 
        array (
          'type' => '',
          'id' => '128',
          'href' => '',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'type' => 'icon',
        'content' => 'Eason Eyewear Inc</br>
171 Greenwich St</br>
Hempstead, NY 11550</br>',
        'button' => false,
        'id' => '194-1',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-1',
          2 => 'info-blocks',
          3 => 'info-blocks-icon',
        ),
        'items' => 
        array (
        ),
      ),
      2 => 
      array (
        'title' => 'Call Us',
        'link' => 
        array (
          'type' => 'custom',
          'id' => '',
          'href' => 'tel:5162809119',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'type' => 'icon',
        'content' => 'Tel: 1.5162809119, 
       1.7186288882',
        'button' => false,
        'id' => '194-2',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-2',
          2 => 'info-blocks',
          3 => 'info-blocks-icon',
        ),
        'items' => 
        array (
        ),
      ),
      3 => 
      array (
        'title' => 'Email',
        'link' => 
        array (
          'type' => 'custom',
          'id' => '',
          'href' => 'mailto:<EMAIL>',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'type' => 'icon',
        'content' => 'Email: <EMAIL>',
        'button' => false,
        'id' => '194-3',
        'classes' => 
        array (
          0 => 'module-item',
          1 => 'module-item-3',
          2 => 'info-blocks',
          3 => 'info-blocks-icon',
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);
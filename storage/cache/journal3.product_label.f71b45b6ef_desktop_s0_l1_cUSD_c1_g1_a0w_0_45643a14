<?php $val = array (
  'data' => 
  array (
    233 => 
    array (
      'php' => 
      array (
        'type' => 'special',
        'label' => 'Label',
        'display' => 'default',
      ),
      'js' => 
      array (
      ),
      'fonts' => 
      array (
        'fonts' => 
        array (
          'Nunito Sans' => 
          array (
            700 => '700',
          ),
        ),
        'subsets' => 
        array (
          'latin-ext' => 'latin-ext',
        ),
      ),
      'css' => '.product-label-default.product-label-233{display:flex;margin-top:5px;margin-right:5px;position:relative;top:0;right:0;bottom:initial;left:initial;justify-content:flex-end}.product-label-diagonal.product-label-233{display:block}.product-label-diagonal.product-label-233>b{transform:scale(calc(10 / 10))}.product-label-233
b{font-family:\'Nunito Sans\';font-weight:700;font-size:11px;color:rgba(255, 255, 255, 1);text-transform:uppercase;background:rgba(51, 51, 51, 1);padding:6px;font-size:12px;background:rgba(221, 14, 28, 1)}.product-label-233.product-label-default
b{min-width:45px}.product-info .product-label-diagonal.product-label-233>b{transform:scale(calc(10 / 10))}',
    ),
    29 => 
    array (
      'php' => 
      array (
        'type' => 'custom',
        'label' => 'New',
        'display' => 'default',
      ),
      'js' => 
      array (
      ),
      'fonts' => 
      array (
      ),
      'css' => '.product-label-default.product-label-29{display:flex;margin-top:5px;margin-right:5px;position:relative;top:0;right:0;bottom:initial;left:initial;justify-content:flex-end}.product-label-diagonal.product-label-29{display:block}.product-label-diagonal.product-label-29>b{transform:scale(calc(10 / 10))}.product-label-29
b{background:rgba(39, 164, 204, 1)}.product-info .product-label-diagonal.product-label-29>b{transform:scale(calc(10 / 10))}',
    ),
    30 => 
    array (
      'php' => 
      array (
        'type' => 'outofstock',
        'label' => 'New',
        'display' => 'diagonal',
      ),
      'js' => 
      array (
      ),
      'fonts' => 
      array (
        'fonts' => 
        array (
          'Nunito Sans' => 
          array (
            700 => '700',
          ),
        ),
        'subsets' => 
        array (
          'latin-ext' => 'latin-ext',
        ),
      ),
      'css' => '.product-label-default.product-label-30{display:flex;position:relative;top:0;right:0;bottom:initial;left:initial;justify-content:flex-end}.product-label-diagonal.product-label-30{display:block;margin:20px}.product-label-diagonal.product-label-30>b{transform:scale(calc(10 / 10))}.product-label-30
b{font-family:\'Nunito Sans\';font-weight:700;font-size:11px;color:rgba(255, 255, 255, 1);text-transform:uppercase;background:rgba(51, 51, 51, 1);padding:6px;background:rgba(221, 14, 28, 1)}.product-label-30.product-label-default
b{min-width:45px}.product-info .product-label-diagonal.product-label-30>b{transform:scale(calc(10 / 10))}',
    ),
    146 => 
    array (
      'php' => 
      array (
        'type' => 'custom',
        'label' => 'Online Only',
        'display' => 'diagonal',
      ),
      'js' => 
      array (
      ),
      'fonts' => 
      array (
      ),
      'css' => '.product-label-default.product-label-146{display:flex;position:relative;top:0;right:0;bottom:initial;left:initial;justify-content:flex-end}.product-label-diagonal.product-label-146{display:block;margin:20px}.product-label-diagonal.product-label-146>b{transform:scale(calc(10 / 10))}.product-label-146
b{background:rgba(105, 105, 115, 1)}.product-info .product-label-diagonal.product-label-146>b{transform:scale(calc(10 / 10))}',
    ),
  ),
  'all' => 
  array (
  ),
  'special' => 
  array (
    233 => 233,
  ),
  'outofstock' => 
  array (
    30 => 30,
  ),
  'custom' => 
  array (
    7469 => 
    array (
      29 => 29,
    ),
    7468 => 
    array (
      29 => 29,
    ),
    7467 => 
    array (
      29 => 29,
    ),
    7466 => 
    array (
      29 => 29,
    ),
    7465 => 
    array (
      29 => 29,
    ),
    7464 => 
    array (
      29 => 29,
    ),
    7463 => 
    array (
      29 => 29,
    ),
    7462 => 
    array (
      29 => 29,
    ),
    7461 => 
    array (
      29 => 29,
    ),
    3715 => 
    array (
      29 => 29,
    ),
    3714 => 
    array (
      29 => 29,
    ),
    3713 => 
    array (
      29 => 29,
    ),
    3711 => 
    array (
      29 => 29,
    ),
    3710 => 
    array (
      29 => 29,
    ),
    3709 => 
    array (
      29 => 29,
    ),
    3708 => 
    array (
      29 => 29,
    ),
    3707 => 
    array (
      29 => 29,
    ),
    3706 => 
    array (
      29 => 29,
    ),
    3705 => 
    array (
      29 => 29,
    ),
    3704 => 
    array (
      29 => 29,
    ),
    3703 => 
    array (
      29 => 29,
    ),
    3702 => 
    array (
      29 => 29,
    ),
    3701 => 
    array (
      29 => 29,
    ),
    3700 => 
    array (
      29 => 29,
    ),
    3699 => 
    array (
      29 => 29,
    ),
    3698 => 
    array (
      29 => 29,
    ),
    3697 => 
    array (
      29 => 29,
    ),
    3696 => 
    array (
      29 => 29,
    ),
    3695 => 
    array (
      29 => 29,
    ),
    3694 => 
    array (
      29 => 29,
    ),
    3693 => 
    array (
      29 => 29,
    ),
    3692 => 
    array (
      29 => 29,
    ),
    3691 => 
    array (
      29 => 29,
    ),
    3690 => 
    array (
      29 => 29,
    ),
    3689 => 
    array (
      29 => 29,
    ),
    3687 => 
    array (
      29 => 29,
    ),
    3686 => 
    array (
      29 => 29,
    ),
    3685 => 
    array (
      29 => 29,
    ),
    3682 => 
    array (
      29 => 29,
    ),
    3681 => 
    array (
      29 => 29,
    ),
    3680 => 
    array (
      29 => 29,
    ),
    3679 => 
    array (
      29 => 29,
    ),
    3678 => 
    array (
      29 => 29,
    ),
    3675 => 
    array (
      29 => 29,
    ),
    3674 => 
    array (
      29 => 29,
    ),
    3673 => 
    array (
      29 => 29,
    ),
    3672 => 
    array (
      29 => 29,
    ),
    3671 => 
    array (
      29 => 29,
    ),
    3670 => 
    array (
      29 => 29,
    ),
    3668 => 
    array (
      29 => 29,
    ),
  ),
);
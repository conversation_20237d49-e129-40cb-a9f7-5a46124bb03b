{"xmeta": {"geo": false, "category_query": false, "product_query": false, "attribute_query": false, "distance": false, "first": false, "xdiscount": false}, "xmethods": [{"tab_id": 1, "name": {"1": "5% Off"}, "display": "5%", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 300, "end": 499, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.05}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}, {"tab_id": 2, "name": {"1": "10% Off"}, "display": "10% Off", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 500, "end": 749, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.1}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}, {"tab_id": 3, "name": {"1": "15% Off"}, "display": "15% Off", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 750, "end": 999, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.15}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}, {"tab_id": 4, "name": {"1": "20% Off"}, "display": "20% Off", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 1000, "end": 1499, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.2}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}, {"tab_id": 5, "name": {"1": "25% Off"}, "display": "25% Off", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 1500, "end": 1999, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.25}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}, {"tab_id": 6, "name": {"1": "30% Off"}, "display": "30% Off", "rules": {"additional_total": {"type": "in_between", "product_rule": false, "address_rule": false, "start": 2000, "end": 99999, "compare_with": "total"}}, "rates": {"type": "flat", "equation": "", "equation_specified_param": false, "final": "single", "percent_of": "sub", "overrule": false, "factor": "500", "additional": [], "cart_adjust": [], "price_adjust": [], "percent": true, "value": -0.3}, "group": 0, "inc_vat": false, "fake": false, "hidden": false, "first": false, "tax_class_id": 0, "sort_order": 0, "ingore_product_rule": false, "product_or": true, "method_specific": true, "disable": false, "disable_other": false, "hide": [], "hide_inactive": [], "need_hide_method": false, "need_inactive_hide_method": false, "have_product_specified": false, "block_req": false}]}
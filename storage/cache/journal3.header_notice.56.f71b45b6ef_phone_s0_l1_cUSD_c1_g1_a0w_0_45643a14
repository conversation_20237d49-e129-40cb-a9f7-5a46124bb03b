<?php $val = array (
  'css' => '.module-header_notice-56 .hn-close::before{content:\'\\ebeb\' !important;font-family:icomoon !important;font-size:22px}.module-header_notice-56 .hn-close:hover::before{color:rgba(51, 51, 51, 1)}.module-header_notice-56 .hn-close.btn, .module-header_notice-56 .hn-close.btn:visited{font-size:12px;color:rgba(51, 51, 51, 1);text-transform:none}.module-header_notice-56 .hn-close.btn:hover{color:rgba(217, 185, 110, 1) !important;background:none !important}.module-header_notice-56 .hn-close.btn{background:none;border-style:none;padding:3px;box-shadow:none}.module-header_notice-56 .hn-close.btn:active, .module-header_notice-56 .hn-close.btn:hover:active, .module-header_notice-56 .hn-close.btn:focus:active{background:none !important}.module-header_notice-56 .hn-close.btn:focus{background:none}.module-header_notice-56 .hn-close.btn.btn.disabled::after{font-size:20px}.module-header_notice-56 .hn-close{margin-left:8px}.module-header_notice-56 .module-body
p{margin-bottom:7px}.module-header_notice-56 .module-body{height:auto;flex-direction:row;background:rgba(219, 232, 242, 1);padding:6px}.module-header_notice-56 .header-notice-close-button{position:relative;top:auto;right:auto;bottom:auto;left:auto;transform:none}.module-header_notice-56 .hn-content
a{text-decoration:underline}.module-header_notice-56 .hn-content a:hover{color:rgba(51, 51, 51, 1);text-decoration:none}.module-header_notice-56 .hn-body::before{float:none}.module-header_notice-56 .hn-body{display:flex}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'cookie' => '8dc5ed03',
    'closeButton' => true,
    'closeLink' => 
    array (
      'type' => '',
      'id' => '',
      'href' => '',
      'name' => '',
      'total' => NULL,
      'attrs' => 
      array (
      ),
      'classes' => 
      array (
      ),
    ),
    'closeText' => '',
    'content' => '<strong>20% OFF</strong> on all products <a href="#">Shop now</a>',
    'status' => false,
    'id' => 'header_notice-68a78efa96128',
    'module_id' => 56,
    'classes' => 
    array (
      0 => 'module',
      1 => 'module-header_notice',
      2 => 'module-header_notice-56',
    ),
    'options' => 
    array (
      'cookie' => '8dc5ed03',
      'ease' => 'easeOutQuart',
      'duration' => '800',
    ),
  ),
);
<?php $val = array (
  'php' => 
  array (
    'headerType' => 'classic',
    'cartStyleCartLabel' => 'Cart',
    'cartStyleCartCustomText' => 'default',
    'cartStyleCartCheckoutLink' => true,
    'cartPosition' => 'top',
    'headerHeight' => '90',
    'headerCompactHeight' => '60',
    'headerHeightHome-hold' => '',
    'headerCompactHeightHome-hold' => '',
    'infoBlocksModule' => '',
    'langPosition' => 'top',
    'headerMainMenu' => '291',
    'headerMainMenu2' => '',
    'headerMenuLayout' => 'stretch',
    'headerMainMenu2Position' => 'menu',
    'headerMainMenuHeight' => '50',
    'menuTriggerText' => 'Menu',
    'mobileMenuOn' => '',
    'searchStyleSearchPlaceholder' => 'Search here...',
    'searchStyleSearchCategories' => 'All',
    'searchStyleSearchCategoriesSelectorStatus' => false,
    'searchStyleSearchCategoriesType' => 'top',
    'searchStyleSearchAutoSuggestStatus' => true,
    'searchStyleSearchAutoSuggestLimit' => '10',
    'searchStyleSearchAutoSuggestDescription' => true,
    'searchStyleSearchAutoSuggestSubCategories' => true,
    'searchStyleSearchViewMoreText' => 'View More',
    'searchStyleSearchNoResultsText' => 'No results found.',
    'headerMiniSearchDropdownPadding' => '',
    'headerMiniSearchDisplay' => 'default',
    'headerTopMenu2' => '',
    'secondaryMenuPosition' => 'cart',
    'headerTopMenu3' => '14',
    'stickyStatus' => true,
    'stickyFullHomePadding' => false,
    'stickyAt' => '',
    'headerTopBarHeight' => '35',
    'topBarStatus' => true,
    'headerTopMenu' => '2',
    'mobileHeaderType' => '1',
    'headerMobileCartTitle' => 'Your Cart',
    'headerMobileMainMenu' => '219',
    'headerMobileTopMenu' => '13',
    'headerMobileHeight' => '60',
    'headerMobileStickyStatus' => true,
    'mobileLangPosition' => 'top',
    'headerMobileMenuTitle' => 'Menu',
    'mobileCustomMenuLink1' => 
    array (
      'type' => 'page',
      'id' => '',
      'href' => 'https://easoneyewear.com/index.php?route=account/wishlist',
      'name' => '',
      'total' => '{{ $wishlist }}',
      'attrs' => 
      array (
      ),
      'classes' => 
      array (
        0 => 'wishlist-badge',
      ),
    ),
    'mobileCustomMenuLink2' => 
    array (
      'type' => 'page',
      'id' => '',
      'href' => 'https://easoneyewear.com/index.php?route=product/compare',
      'name' => '',
      'total' => '{{ $compare }}',
      'attrs' => 
      array (
      ),
      'classes' => 
      array (
        0 => 'compare-badge',
      ),
    ),
    'mobileCustomMenuStatus1' => true,
    'mobileCustomMenuStatus2' => false,
    'headerMobileTopBarVisibility' => true,
    'headerMobileTopBarHeight' => '44',
  ),
  'js' => 
  array (
    'headerHeight' => '90',
    'headerCompactHeight' => '60',
    'mobileMenuOn' => '',
    'searchStyleSearchAutoSuggestStatus' => true,
    'searchStyleSearchAutoSuggestDescription' => true,
    'searchStyleSearchAutoSuggestSubCategories' => true,
    'headerMiniSearchDisplay' => 'default',
    'stickyStatus' => true,
    'stickyFullHomePadding' => false,
    'stickyFullwidth' => true,
    'stickyAt' => '',
    'stickyHeight' => '50',
    'headerTopBarHeight' => '35',
    'topBarStatus' => true,
    'headerType' => 'classic',
    'headerMobileHeight' => '60',
    'headerMobileStickyStatus' => true,
    'headerMobileTopBarVisibility' => true,
    'headerMobileTopBarHeight' => '44',
  ),
  'fonts' => 
  array (
    'fonts' => 
    array (
      'Nunito Sans' => 
      array (
        400 => '400',
      ),
      'Montserrat' => 
      array (
        700 => '700',
      ),
    ),
    'subsets' => 
    array (
      'latin-ext' => 'latin-ext',
    ),
  ),
  'css' => '#cart .cart-label{display:inline-block;color:rgba(240,242,245,1)}#cart>a>i::before{font-size:24px;color:rgba(255,255,255,1);left:1px;top:-1px}#cart>a>i{background:rgba(51, 51, 51, 1);width:49px;height:49px}.desktop #cart:hover>a>i{background:rgba(217,185,110,1)}#cart-items.count-badge{font-family:\'Nunito Sans\';font-weight:400;font-size:11px;color:rgba(255,255,255,1);background:rgba(221,14,28,1);border-width:2px;border-style:solid;border-color:rgba(255,255,255,1);border-radius:10px}#cart-items{transform:translateX(5px);margin-top:-7px;display:inline-flex;z-index:1}#cart-total{display:flex;padding-right:15px;padding-left:20px;font-size:14px;font-weight:400;order:0}.desktop #cart:hover{background:rgba(240,242,245,1)}#cart{border-style:none;display:block}#cart-content{min-width:400px}div.cart-content
ul{background:rgba(250, 250, 250, 1);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}div.cart-content .cart-products tbody>tr>td{border-style:solid !important;border-color:rgba(221, 221, 221, 1) !important;vertical-align:middle}#cart-content::before{border-bottom-color:rgba(250, 250, 250, 1);margin-left:-4px;margin-top:-6px}div.cart-content .cart-products{max-height:275px;overflow-y:auto}div.cart-content .cart-products tbody .td-remove
button{color:rgba(221, 14, 28, 1)}div.cart-content .cart-products tbody .td-remove button:hover{color:rgba(80, 173, 85, 1)}div.cart-content .cart-totals tbody
td{background:rgba(238, 238, 238, 1);border-style:solid !important;border-color:rgba(221, 221, 221, 1) !important}div.cart-content .cart-totals
td{font-weight:700}div.cart-content .cart-totals .td-total-text{font-weight:700}div.cart-content .cart-buttons{border-style:solid;border-color:rgba(221, 221, 221, 1);background:rgba(230, 230, 230, 1)}div.cart-content .btn-cart{display:inline-flex}div.cart-content .btn.btn-cart::before{margin-right:5px}div.cart-content .btn-checkout{display:inline-flex}div.cart-content .btn.btn-checkout::after{content:\'\\e5c8\' !important;font-family:icomoon !important;margin-left:3px}div.cart-content .cart-buttons
.btn{width:auto}.desktop-header-active
#cart{margin-left:20px}.desktop-header-active
header{background:rgba(255, 255, 255, 1);box-shadow:0 5px 20px -5px rgba(0, 0, 0, 0.1)}.desktop-header-active .header-lg .mid-bar{height:90px}.desktop-header-active .header-default{height:90px}.desktop-header-active .header-default::before{content:\'\';height:calc(90px / 3)}.desktop-header-active .header-sm .mid-bar{height:60px}.info-blocks-wrapper{justify-content:flex-end}.language .dropdown-toggle
.symbol{display:flex;border-radius:0px}.language .dropdown-toggle .symbol+span{display:block;margin-left:5px}.language .language-flag{display:inline-flex}.language .currency-symbol{display:inline-flex}.language .language-title-dropdown{display:inline-flex}.language .currency-title-dropdown{display:inline-flex}.language .currency-code-dropdown{display:none}.language .dropdown-toggle > span, .language .dropdown::after{font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase}.desktop .language .dropdown:hover button > span, .language .dropdown:hover::after{color:rgba(217, 185, 110, 1)}.currency .dropdown-toggle
.symbol{display:flex;border-radius:0px}.currency .dropdown-toggle .symbol+span{display:block;margin-left:5px}.currency .language-flag{display:inline-flex}.currency .currency-symbol{display:inline-flex}.currency .language-title-dropdown{display:inline-flex}.currency .currency-title-dropdown{display:inline-flex}.currency .currency-code-dropdown{display:none}.currency .dropdown-toggle > span, .currency .dropdown::after{font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase}.desktop .currency .dropdown:hover button > span, .currency .dropdown:hover::after{color:rgba(217, 185, 110, 1)}.language-currency.top-menu .dropdown.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.language-currency.top-menu .dropdown.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.language-currency.top-menu .dropdown.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.language-currency.top-menu .dropdown.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.language-currency.top-menu .dropdown.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.language-currency.top-menu .dropdown.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.language-currency.top-menu .dropdown.dropdown .j-menu .dropdown>a::after{display:block}.desktop .language-currency.top-menu .dropdown.dropdown .j-menu > li:hover > a, .language-currency.top-menu .dropdown.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.language-currency.top-menu .dropdown.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.language-currency.top-menu .dropdown.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .language-currency.top-menu .dropdown.dropdown .j-menu > li:hover > a::before, .language-currency.top-menu .dropdown.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.language-currency.top-menu .dropdown.dropdown .j-menu>li+li{margin-left:0px}.language-currency.top-menu .dropdown.dropdown .j-menu a .count-badge{display:none;position:relative}.language-currency.top-menu .dropdown.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.language-currency.top-menu .dropdown.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.language-currency.top-menu .dropdown.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}.language-currency.top-menu .currency .dropdown.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.language-currency.top-menu .currency .dropdown.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.language-currency.top-menu .currency .dropdown.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.language-currency.top-menu .currency .dropdown.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.language-currency.top-menu .currency .dropdown.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.language-currency.top-menu .currency .dropdown.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.language-currency.top-menu .currency .dropdown.dropdown .j-menu .dropdown>a::after{display:block}.desktop .language-currency.top-menu .currency .dropdown.dropdown .j-menu > li:hover > a, .language-currency.top-menu .currency .dropdown.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.language-currency.top-menu .currency .dropdown.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.language-currency.top-menu .currency .dropdown.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .language-currency.top-menu .currency .dropdown.dropdown .j-menu > li:hover > a::before, .language-currency.top-menu .currency .dropdown.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.language-currency.top-menu .currency .dropdown.dropdown .j-menu>li+li{margin-left:0px}.language-currency.top-menu .currency .dropdown.dropdown .j-menu a .count-badge{display:none;position:relative}.language-currency.top-menu .currency .dropdown.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.language-currency.top-menu .currency .dropdown.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.language-currency.top-menu .currency .dropdown.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}.desktop-header-active .header .top-bar .language-currency{margin-left:auto}.desktop-header-active .header .top-bar{justify-content:space-between;height:35px}.desktop-header-active .header #logo
a{justify-content:flex-start}.desktop-header-active .header-classic .mid-bar .desktop-logo-wrapper{order:0;margin:0}.desktop-header-active .header-classic .mid-bar .desktop-search-wrapper{order:2;flex-grow:1}.desktop-logo-wrapper{width:auto}.desktop-search-wrapper{width:auto;margin-right:30px;margin-left:50px}.classic-cart-wrapper{width:auto}.desktop-header-active header:not(.header-slim) .header-compact .mid-bar{justify-content:flex-start}.desktop-header-active header:not(.header-slim) .header-compact .mid-bar>div{max-width:none}.desktop-header-active header:not(.header-slim) .header-compact .header-cart-group{margin-left:auto}.desktop-header-active header:not(.header-slim) .header-compact .mid-bar .desktop-logo-wrapper{position:relative;left:0;transform:translateX(0)}.desktop-main-menu-wrapper .first-dropdown::before{display:none !important;background-color:rgba(0, 0, 0, 0.6)}.main-menu > .j-menu .dropdown>a>.count-badge{margin-right:5px}.main-menu > .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.main-menu > .j-menu .dropdown>a::after{display:none}.main-menu>.j-menu>li>a{font-family:\'Montserrat\';font-weight:700;font-size:13px;color:rgba(51, 51, 51, 1);text-transform:uppercase;padding:5px}.desktop .main-menu>.j-menu>li:hover>a,.main-menu>.j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.main-menu > .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main-menu>.j-menu>li>a::before{margin-right:5px}.main-menu>.j-menu>li+li{margin-left:7px}.main-menu > .j-menu li .count-badge{font-family:\'Nunito Sans\';font-weight:400;font-size:11px;color:rgba(255, 255, 255, 1);background:rgba(221, 14, 28, 1);border-width:2px;border-style:solid;border-color:rgba(255, 255, 255, 1);border-radius:10px}.main-menu > .j-menu a .count-badge{display:inline-flex;position:relative;margin-top:-3px}.main-menu>ul>.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0,-10px,0)}.main-menu>ul>.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.main-menu>ul>.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.main-menu > ul >.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.main-menu > ul >.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.main-menu > ul >.dropdown .j-menu .dropdown>a::after{display:block}.main-menu > ul >.dropdown .j-menu>li>a{font-size:14px;color:rgba(51, 51, 51, 1);font-weight:400;background:rgba(255, 255, 255, 1);padding:10px}.desktop .main-menu > ul >.dropdown .j-menu > li:hover > a, .main-menu > ul >.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.main-menu > ul >.dropdown .j-menu .links-text{white-space:normal;overflow:visible;text-overflow:initial}.main-menu > ul >.dropdown .j-menu>li>a::before{margin-right:7px;min-width:20px;font-size:18px}.main-menu > ul >.dropdown .j-menu a .count-badge{display:none;position:relative}.main-menu > ul >.dropdown:not(.mega-menu) .j-dropdown{min-width:200px}.main-menu > ul >.dropdown:not(.mega-menu) .j-menu{box-shadow:30px 40px 90px -10px rgba(0, 0, 0, 0.2)}.main-menu > ul >.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(255,255,255,1);margin-left:7px;margin-top:-10px}#main-menu-2>ul>.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0,-10px,0)}#main-menu-2>ul>.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}#main-menu-2>ul>.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}#main-menu-2 > ul >.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}#main-menu-2 > ul >.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}#main-menu-2 > ul >.dropdown .j-menu .dropdown>a::after{display:block}#main-menu-2 > ul >.dropdown .j-menu>li>a{font-size:14px;color:rgba(51, 51, 51, 1);font-weight:400;background:rgba(255, 255, 255, 1);padding:10px}.desktop #main-menu-2 > ul >.dropdown .j-menu > li:hover > a, #main-menu-2 > ul >.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}#main-menu-2 > ul >.dropdown .j-menu .links-text{white-space:normal;overflow:visible;text-overflow:initial}#main-menu-2 > ul >.dropdown .j-menu>li>a::before{margin-right:7px;min-width:20px;font-size:18px}#main-menu-2 > ul >.dropdown .j-menu a .count-badge{display:none;position:relative}#main-menu-2 > ul >.dropdown:not(.mega-menu) .j-dropdown{min-width:200px}#main-menu-2 > ul >.dropdown:not(.mega-menu) .j-menu{box-shadow:30px 40px 90px -10px rgba(0, 0, 0, 0.2)}#main-menu-2 > ul >.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(255,255,255,1);margin-left:7px;margin-top:-10px}.mega-menu-content{background:rgba(255, 255, 255, 1)}.desktop .mega-menu-content{border-width:0;border-top-width:1px;border-style:solid;border-color:rgba(221,221,221,1)}.j-dropdown>.mega-menu-content{box-shadow:30px 40px 90px -10px rgba(0, 0, 0, 0.2);max-height:500px !important;overflow-y:auto}.desktop-header-active .header-compact .desktop-main-menu-wrapper{height:auto}.header-lg .desktop-main-menu-wrapper .main-menu .main-menu-item>a{height:auto}.desktop-header-active .header-compact .desktop-logo-wrapper{order:0}.desktop-main-menu-wrapper #main-menu{margin-left:0;margin-right:auto}.desktop-main-menu-wrapper .desktop-cart-wrapper{margin-left:0}.mid-bar #main-menu-2{order:5}.desktop-header-active .header .menu-stretch .main-menu-item > a .links-text{text-align:center}.desktop-main-menu-wrapper{height:50px;top:-50px}.desktop-main-menu-wrapper::before{height:50px;border-width:0;border-top-width:1px;border-style:solid;border-color:rgba(221, 221, 221, 1)}.desktop-main-menu-wrapper .main-menu-item>a{padding:0
15px}.header-compact .desktop-main-menu-wrapper #main-menu{margin-left:initial;margin-right:auto}.desktop-header-active .menu-trigger a::before{content:\'\\f0c9\' !important;font-family:icomoon !important}.header-search{border-radius:40px}.header-search
input{background:rgba(240, 242, 245, 1) !important}.header-search>.search-button::before{content:\'\\ebaf\' !important;font-family:icomoon !important;font-size:22px;color:rgba(255, 255, 255, 1)}.header-search .search-button{background:rgba(51, 51, 51, 1);min-width:45px}.desktop .header-search .search-button:hover{background:rgba(217, 185, 110, 1)}#search input::-webkit-input-placeholder{color:rgba(105, 105, 115, 1)}#search input::-moz-input-placeholder{color:rgba(105, 105, 115, 1)}#search input:-ms-input-placeholder{color:rgba(105,105,115,1)}.header-search>input{border-top-left-radius:inherit;border-bottom-left-radius:inherit}.search-categories{background:rgba(51, 51, 51, 1)}.desktop .search-categories:hover{background:rgba(217,185,110,1)}.search-categories-button,.search-categories-button>a{color:rgba(255,255,255,1)}.search-categories-button::after{content:\'\\f0d7\' !important;font-family:icomoon !important;color:rgba(255,255,255,1)}.search-categories.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%,-10px,0)}.search-categories.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%,0,0)}.search-categories.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.search-categories.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.search-categories.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.search-categories.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.search-categories.dropdown .j-menu .dropdown>a::after{display:block}.desktop .search-categories.dropdown .j-menu > li:hover > a, .search-categories.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.search-categories.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.search-categories.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .search-categories.dropdown .j-menu > li:hover > a::before, .search-categories.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.search-categories.dropdown .j-menu>li+li{margin-left:0px}.search-categories.dropdown .j-menu a .count-badge{display:none;position:relative}.search-categories.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.search-categories.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.search-categories.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}.search-categories .j-menu::before{margin-top:-10px}.tt-menu>div{box-shadow:0 10px 65px -5px rgba(0,0,0,0.5);border-radius:3px}.tt-menu:not(.tt-empty)::before{display:block;margin-left:-4px;margin-top:-10px;left:100%;transform:translateX(-150%)}.search-result .product-name{font-weight:700}.search-result>a>span{justify-content:flex-start}.search-result.view-more a::after{content:\'\\e5c8\' !important;font-family:icomoon !important}#search{display:block}.desktop-header-active .header-default .desktop-search-wrapper{order:1;flex-grow:0}.desktop-header-active .header-default .top-menu-group{order:-1;flex-grow:1}.desktop-header-active .header-search .search-button{order:5;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:inherit;border-bottom-right-radius:inherit}.desktop-header-active .header-search>input:first-child{border-top-left-radius:inherit;border-bottom-left-radius:inherit;border-top-right-radius:0;border-bottom-right-radius:0}.desktop-header-active .header-search>input{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.desktop-header-active .header-search>span:first-child{border-top-left-radius:inherit;border-bottom-left-radius:inherit;border-top-right-radius:0;border-bottom-right-radius:0}.desktop-header-active .header-search>span{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.desktop-header-active .search-categories{border-top-left-radius:inherit;border-bottom-left-radius:inherit;border-top-right-radius:0;border-bottom-right-radius:0}.desktop-header-active .mini-search .header-search
input{min-width:150px}.desktop-header-active .header .full-search
#search{height:38px}.mini-search .search-trigger::before{content:\'\\f002\' !important;font-family:icomoon !important}.mini-search #search>.dropdown-menu::before{display:block;margin-top:-10px}.mid-bar .secondary-menu{justify-content:flex-end}.desktop-header-active .is-sticky .header .desktop-main-menu-wrapper::before{width:100vw;margin-left:-50vw;left:50%;;background:rgba(255, 255, 255, 1);box-shadow:0 10px 30px rgba(0, 0, 0, 0.1)}.desktop-header-active .is-sticky .header .desktop-main-menu-wrapper{height:50px}.desktop-header-active header::before{content:\'\';height:35px}header::before{background:rgba(255, 255, 255, 1);border-width:0;border-bottom-width:1px;border-style:solid;border-color:rgba(221, 221, 221, 1)}.top-menu .j-menu>li>a{flex-direction:row;font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase;padding:5px}.top-menu .j-menu .dropdown>a>.count-badge{margin-right:0}.top-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.top-menu .j-menu .dropdown>a::after{display:block}.desktop .top-menu .j-menu > li:hover > a, .top-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.top-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.top-menu .j-menu>li>a::before{margin-right:5px;font-size:14px}.top-menu .j-menu a .count-badge{display:none;position:relative}.top-menu .dropdown.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.top-menu .dropdown.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.top-menu .dropdown.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.top-menu .dropdown.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.top-menu .dropdown.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.top-menu .dropdown.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.top-menu .dropdown.dropdown .j-menu .dropdown>a::after{display:block}.desktop .top-menu .dropdown.dropdown .j-menu > li:hover > a, .top-menu .dropdown.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.top-menu .dropdown.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.top-menu .dropdown.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .top-menu .dropdown.dropdown .j-menu > li:hover > a::before, .top-menu .dropdown.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.top-menu .dropdown.dropdown .j-menu>li+li{margin-left:0px}.top-menu .dropdown.dropdown .j-menu a .count-badge{display:none;position:relative}.top-menu .dropdown.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.top-menu .dropdown.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.top-menu .dropdown.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}@media (max-width: 1300px){#cart-items{transform:translateX(0px)}.desktop-header-active .header .mid-bar{padding-right:10px;padding-left:10px}.header .top-bar{padding-right:10px;padding-left:10px}}@media (max-width: 1024px){#cart{border-style:none}.language-currency.top-menu .dropdown.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.language-currency.top-menu .dropdown.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.language-currency.top-menu .dropdown.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.language-currency.top-menu .currency .dropdown.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.language-currency.top-menu .currency .dropdown.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.language-currency.top-menu .currency .dropdown.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.search-categories.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0,-10px,0)}.search-categories.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.search-categories.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.top-menu .dropdown.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.top-menu .dropdown.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.top-menu .dropdown.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}}@media (max-width: 470px){.language .dropdown-toggle > span, .language .dropdown::after{font-size:11px}.currency .dropdown-toggle > span, .currency .dropdown::after{font-size:11px}.top-menu .j-menu>li>a{font-size:11px}}.mobile-header-active #cart>a>i::before{font-size:24px;color:rgba(58, 71, 84, 1);margin-right:5px}.mobile-cart-wrapper #cart>a>i{background:none}.mobile-header-active #cart>a>i{width:55px;height:45px}.mobile-header-active .mobile-wrapper-header>span{font-family:\'Montserrat\';font-weight:700;text-transform:uppercase;font-family:\'Montserrat\';font-weight:700;text-transform:uppercase}.mobile-header-active .mobile-wrapper-header{background:rgba(240, 242, 245, 1);height:45px;background:rgba(240, 242, 245, 1);height:45px}.mobile-header-active .mobile-wrapper-header>a{width:45px;width:45px}.mobile-header-active .mobile-cart-content-wrapper{padding-bottom:45px;padding-bottom:45px}.mobile-header-active .mobile-filter-wrapper{padding-bottom:45px;padding-bottom:45px}.mobile-header-active .mobile-main-menu-wrapper{padding-bottom:45px;padding-bottom:45px}.mobile-header-active .mobile-filter-container-open .journal-loading-overlay{top:45px;top:45px}.mobile-header-active.mobile-header-active .mobile-container{width:30%;width:30%}.mobile-header-active.desktop-header-active .mobile-main-menu-container{width:300px;width:300px}.mobile-header-active .mobile-main-menu-container{background:rgba(255, 255, 255, 1);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2);background:rgba(255, 255, 255, 1);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .mobile-main-menu-wrapper .main-menu{padding:20px;padding:20px}.mobile-header-active .mobile-cart-content-container{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active.mobile-overlay .site-wrapper::before{background:rgba(0, 0, 0, 0.5);background:rgba(0, 0, 0, 0.5)}.mobile-header-active #cart-items.count-badge{transform:translateX(-7px);margin-top:10px;display:inline-flex}.mobile-header-active .mobile-bar{background:rgba(255, 255, 255, 1);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .mobile-1 .mobile-bar{height:60px}.mobile-header-active .mobile-2 .mobile-bar{height:60px}.mobile-header-active .mobile-3 .mobile-logo-wrapper{height:60px}.mobile-header-active .language .dropdown-toggle
.symbol{display:flex;border-radius:0px}.mobile-header-active .language .dropdown-toggle .symbol+span{display:block;margin-left:5px}.mobile-header-active .language .language-flag{display:inline-flex}.mobile-header-active .language .currency-symbol{display:inline-flex}.mobile-header-active .language .language-title-dropdown{display:inline-flex}.mobile-header-active .language .currency-title-dropdown{display:inline-flex}.mobile-header-active .language .currency-code-dropdown{display:none}.mobile-header-active .language .dropdown-toggle > span, .mobile-header-active .language .dropdown::after{font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase}.desktop .mobile-header-active .language .dropdown:hover button > span, .mobile-header-active .language .dropdown:hover::after{color:rgba(217, 185, 110, 1)}.mobile-header-active .currency .dropdown-toggle
.symbol{display:none;border-radius:0px}.mobile-header-active .currency .dropdown-toggle .language-title{display:block;margin-left:0}.mobile-header-active .currency .dropdown-toggle .currency-title{display:none}.mobile-header-active .currency .dropdown-toggle .currency-code{display:block}.mobile-header-active .currency .language-flag{display:inline-flex}.mobile-header-active .currency .currency-symbol{display:inline-flex}.mobile-header-active .currency .language-title-dropdown{display:inline-flex}.mobile-header-active .currency .currency-title-dropdown{display:inline-flex}.mobile-header-active .currency .currency-code-dropdown{display:none}.mobile-header-active .currency .dropdown-toggle > span, .mobile-header-active .currency .dropdown::after{font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase}.desktop .mobile-header-active .currency .dropdown:hover button > span, .mobile-header-active .currency .dropdown:hover::after{color:rgba(217, 185, 110, 1)}.mobile-header-active .language-currency.top-menu.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.mobile-header-active .language-currency.top-menu.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.mobile-header-active .language-currency.top-menu.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.mobile-header-active .language-currency.top-menu.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.mobile-header-active .language-currency.top-menu.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.mobile-header-active .language-currency.top-menu.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.mobile-header-active .language-currency.top-menu.dropdown .j-menu .dropdown>a::after{display:block}.desktop .mobile-header-active .language-currency.top-menu.dropdown .j-menu > li:hover > a, .mobile-header-active .language-currency.top-menu.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.mobile-header-active .language-currency.top-menu.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mobile-header-active .language-currency.top-menu.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .mobile-header-active .language-currency.top-menu.dropdown .j-menu > li:hover > a::before, .mobile-header-active .language-currency.top-menu.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.mobile-header-active .language-currency.top-menu.dropdown .j-menu>li+li{margin-left:0px}.mobile-header-active .language-currency.top-menu.dropdown .j-menu a .count-badge{display:none;position:relative}.mobile-header-active .language-currency.top-menu.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.mobile-header-active .language-currency.top-menu.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .language-currency.top-menu.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}.mobile-header-active #logo
a{padding:15px}.mobile-header-active .menu-trigger::before{content:\'\\eb7e\' !important;font-family:icomoon !important;font-size:28px;color:rgba(58, 71, 84, 1);top:-1px}.mobile-header-active .menu-trigger{width:45px;height:45px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .dropdown>a::after{display:none}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu>li>a{font-size:17px;color:rgba(51, 51, 51, 1);font-weight:400;text-transform:none;background:none;padding:10px;padding-left:0px}.desktop .mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu > li:hover > a, .mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu>li>a::before{color:rgba(139, 145, 152, 1);margin-right:10px;min-width:24px;font-size:24px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu a .count-badge{display:none;position:relative}.mobile-main-menu-wrapper .main-menu .open-menu i::before{content:\'\\eba1\' !important;font-family:icomoon !important;font-size:20px;left:5px}.mobile-main-menu-wrapper .main-menu .open-menu[aria-expanded=\'true\'] i::before{content:\'\\eb86\' !important;font-family:icomoon !important;font-size:20px;color:rgba(217, 185, 110, 1);left:5px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu>li>a{flex-direction:row;font-family:\'Nunito Sans\';font-weight:400;font-size:15px;color:rgba(105, 105, 115, 1);padding:8px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu .dropdown>a::after{display:none}.desktop .mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu > li:hover > a, .mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu .links-text{white-space:normal;overflow:visible;text-overflow:initial}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu>li>a::before{margin-right:5px}.mobile-main-menu-wrapper .main-menu.accordion-menu .j-menu .j-menu a .count-badge{display:inline-flex;position:relative}.mobile-main-menu-wrapper .main-menu .j-menu > li > div .j-menu>li>a{padding-left:30px !important}.mobile-main-menu-wrapper .main-menu .j-menu > li > div .j-menu>li>div>.j-menu>li>a{padding-left:40px !important}.mobile-main-menu-wrapper .main-menu .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:50px !important}.mobile-main-menu-wrapper .main-menu .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:60px !important}.mobile-custom-menu-1::before{content:\'\\eaa7\' !important;font-family:icomoon !important;font-size:22px;color:rgba(58,71,84,1)}.mobile-custom-menu{width:40px}.mobile-custom-menu-2::before{content:\'\\eab6\' !important;font-family:icomoon !important;font-size:22px;color:rgba(230, 230, 230, 1)}.mobile-custom-menu-1 .count-badge{transform:translateX(2px);display:inline-flex}.mobile-custom-menu-2 .count-badge{display:inline-flex}.mobile-header-active  .mini-search .search-trigger::before{content:\'\\ebaf\' !important;font-family:icomoon !important;font-size:26px;color:rgba(58, 71, 84, 1);left:5px}.mobile-header-active .header-search{border-radius:40px}.mobile-header-active .header-search
input{background:rgba(240, 242, 245, 1) !important}.mobile-header-active .header-search>.search-button::before{content:\'\\ebaf\' !important;font-family:icomoon !important;font-size:22px;color:rgba(255, 255, 255, 1)}.mobile-header-active .header-search .search-button{background:rgba(51, 51, 51, 1);min-width:45px}.desktop .mobile-header-active .header-search .search-button:hover{background:rgba(217, 185, 110, 1)}.mobile-header-active #search input::-webkit-input-placeholder{color:rgba(105, 105, 115, 1)}.mobile-header-active #search input::-moz-input-placeholder{color:rgba(105, 105, 115, 1)}.mobile-header-active #search input:-ms-input-placeholder{color:rgba(105, 105, 115, 1)}.mobile-header-active .header-search>input{border-top-left-radius:inherit;border-bottom-left-radius:inherit}.mobile-header-active .search-categories{background:rgba(51, 51, 51, 1)}.desktop .mobile-header-active .search-categories:hover{background:rgba(217, 185, 110, 1)}.mobile-header-active .search-categories-button, .mobile-header-active .search-categories-button>a{color:rgba(255, 255, 255, 1)}.mobile-header-active .search-categories-button::after{content:\'\\f0d7\' !important;font-family:icomoon !important;color:rgba(255, 255, 255, 1)}.mobile-header-active .search-categories.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.mobile-header-active .search-categories.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.mobile-header-active .search-categories.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.mobile-header-active .search-categories.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.mobile-header-active .search-categories.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.mobile-header-active .search-categories.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.mobile-header-active .search-categories.dropdown .j-menu .dropdown>a::after{display:block}.desktop .mobile-header-active .search-categories.dropdown .j-menu > li:hover > a, .mobile-header-active .search-categories.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.mobile-header-active .search-categories.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mobile-header-active .search-categories.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .mobile-header-active .search-categories.dropdown .j-menu > li:hover > a::before, .mobile-header-active .search-categories.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.mobile-header-active .search-categories.dropdown .j-menu>li+li{margin-left:0px}.mobile-header-active .search-categories.dropdown .j-menu a .count-badge{display:none;position:relative}.mobile-header-active .search-categories.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.mobile-header-active .search-categories.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .search-categories.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}.mobile-header-active .search-categories .j-menu::before{margin-top:-10px}.mobile-header-active .tt-menu>div{box-shadow:0 10px 65px -5px rgba(0, 0, 0, 0.5);border-radius:3px}.mobile-header-active .tt-menu:not(.tt-empty)::before{display:block;margin-left:-4px;margin-top:-10px}.mobile-header-active .search-result .product-name{font-weight:700}.mobile-header-active .search-result>a>span{justify-content:flex-start}.mobile-header-active .search-result.view-more a::after{content:\'\\e5c8\' !important;font-family:icomoon !important}.mobile-header-active .mini-search  #search .search-trigger{width:40px}.mobile-header-active .mobile-1 #search .header-search{padding:5px}.mobile-header-active .mobile-search-group{padding:5px}.mobile-header-active .mobile-3 .mobile-search-wrapper{padding:0
5px}.mobile-header-active #search .header-search{height:45px}.mobile-header-active .mobile-search-group, .mobile-header-active .mobile-1 .header-search{background:rgba(221, 221, 221, 1);box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .mobile-1 #search>.dropdown-menu::before{display:none;margin-left:-60px;margin-top:-10px}.mobile-header-active .mobile-header .mobile-top-bar{display:flex;height:44px;border-width:0;border-bottom-width:1px;border-style:solid;border-color:rgba(221, 221, 221, 1);padding-right:5px;padding-left:5px;background:rgba(255, 255, 255, 1);justify-content:space-between}.mobile-header-active .top-menu .j-menu>li>a{flex-direction:row;font-size:12px;color:rgba(105, 105, 115, 1);font-weight:700;text-transform:uppercase;padding:5px}.mobile-header-active .top-menu .j-menu .dropdown>a>.count-badge{margin-right:0}.mobile-header-active .top-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.mobile-header-active .top-menu .j-menu .dropdown>a::after{display:block}.desktop .mobile-header-active .top-menu .j-menu > li:hover > a, .mobile-header-active .top-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.mobile-header-active .top-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mobile-header-active .top-menu .j-menu>li>a::before{margin-right:5px;font-size:14px}.mobile-header-active .top-menu .j-menu a .count-badge{display:none;position:relative}.mobile-header-active .top-menu.drop-menu>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, -10px, 0)}.mobile-header-active .top-menu.drop-menu.animating>.j-dropdown{left:50%;right:auto;transform:translate3d(-50%, 0, 0)}.mobile-header-active .top-menu.drop-menu>.j-dropdown::before{left:50%;right:auto;transform:translateX(-50%)}.mobile-header-active .top-menu.dropdown .j-menu>li>a{flex-direction:row;font-size:13px;color:rgba(255, 255, 255, 1);text-transform:none;background:rgba(51, 51, 51, 1);padding:10px;padding-right:15px;padding-left:15px}.mobile-header-active .top-menu.dropdown .j-menu .dropdown>a>.count-badge{margin-right:0}.mobile-header-active .top-menu.dropdown .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:7px}.mobile-header-active .top-menu.dropdown .j-menu .dropdown>a::after{display:block}.desktop .mobile-header-active .top-menu.dropdown .j-menu > li:hover > a, .mobile-header-active .top-menu.dropdown .j-menu>li.active>a{color:rgba(255, 255, 255, 1);background:rgba(217, 185, 110, 1)}.mobile-header-active .top-menu.dropdown .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mobile-header-active .top-menu.dropdown .j-menu>li>a::before{color:rgba(255, 255, 255, 1);margin:0px;margin-right:5px;font-size:15px}.desktop .mobile-header-active .top-menu.dropdown .j-menu > li:hover > a::before, .mobile-header-active .top-menu.dropdown .j-menu>li.active>a::before{color:rgba(255, 255, 255, 1)}.mobile-header-active .top-menu.dropdown .j-menu>li+li{margin-left:0px}.mobile-header-active .top-menu.dropdown .j-menu a .count-badge{display:none;position:relative}.mobile-header-active .top-menu.dropdown:not(.mega-menu) .j-dropdown{min-width:100px}.mobile-header-active .top-menu.dropdown:not(.mega-menu) .j-menu{box-shadow:0 15px 90px -10px rgba(0, 0, 0, 0.2)}.mobile-header-active .top-menu.dropdown .j-dropdown::before{display:block;border-bottom-color:rgba(51, 51, 51, 1);margin-left:-2px;margin-top:-10px}@media (max-width: 1024px){.mobile-header-active.mobile-header-active .mobile-container{width:40%;width:40%}.mobile-header-active .language-currency.top-menu.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.mobile-header-active .language-currency.top-menu.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.mobile-header-active .language-currency.top-menu.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.mobile-header-active .search-categories.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.mobile-header-active .search-categories.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.mobile-header-active .search-categories.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}.mobile-header-active .top-menu.drop-menu>.j-dropdown{left:0;right:auto;transform:translate3d(0, -10px, 0)}.mobile-header-active .top-menu.drop-menu.animating>.j-dropdown{left:0;right:auto;transform:none}.mobile-header-active .top-menu.drop-menu>.j-dropdown::before{left:10px;right:auto;transform:translateX(0)}}@media (max-width: 760px){.mobile-header-active.mobile-header-active .mobile-container{width:85%;width:85%}}@media (max-width: 470px){.mobile-header-active .language .dropdown-toggle > span, .mobile-header-active .language .dropdown::after{font-size:11px}.mobile-header-active .currency .dropdown-toggle > span, .mobile-header-active .currency .dropdown::after{font-size:11px}.mobile-header-active .top-menu .j-menu>li>a{font-size:11px}}',
);
<?php $val = array (
  'css' => '.top-menu-2 .j-menu li.top-menu-item-1>a::before{content:\'\\e0cd\' !important;font-family:icomoon !important}.top-menu-2>ul>.top-menu-item-1>a{text-align:left}.top-menu-2>ul>.top-menu-item-1>a>.links-text{display:block}.top-menu-2 > ul > .top-menu-item-1 > a .count-badge{position:relative}.top-menu-2 .j-menu li.top-menu-item-2>a::before{content:\'\\f0d1\' !important;font-family:icomoon !important}.top-menu-2>ul>.top-menu-item-2>a{text-align:left}.top-menu-2>ul>.top-menu-item-2>a>.links-text{display:block}.top-menu-2 > ul > .top-menu-item-2 > a .count-badge{position:relative}.top-menu-2 .j-menu li.top-menu-item-3>a::before{content:\'\\f112\' !important;font-family:icomoon !important}.top-menu-2>ul>.top-menu-item-3>a{text-align:left}.top-menu-2>ul>.top-menu-item-3>a>.links-text{display:block}.top-menu-2 > ul > .top-menu-item-3 > a .count-badge{position:relative}.top-menu-2 .j-menu li.top-menu-item-4>a::before{content:\'\\e986\' !important;font-family:icomoon !important}.top-menu-2>ul>.top-menu-item-4>a{text-align:left}.top-menu-2>ul>.top-menu-item-4>a>.links-text{display:block}.top-menu-2 > ul > .top-menu-item-4 > a .count-badge{position:relative}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'scheduledStatus' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'status' => true,
    'id' => 'top-menu-68a787a284a7a',
    'module_id' => 2,
    'classes' => 
    array (
      0 => 'top-menu',
      1 => 'top-menu-2',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => '(516) 280-9119',
        'link' => 
        array (
          'type' => '',
          'id' => '',
          'href' => '',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-1',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
      2 => 
      array (
        'title' => 'Free Shipping Over $200',
        'link' => 
        array (
          'type' => 'information',
          'id' => '8',
          'href' => 'https://easoneyewear.com/index.php?route=information/information&amp;information_id=8',
          'name' => 'Shipping Information',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-2',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
      3 => 
      array (
        'title' => 'Return Policy',
        'link' => 
        array (
          'type' => 'information',
          'id' => '7',
          'href' => 'https://easoneyewear.com/index.php?route=information/information&amp;information_id=7',
          'name' => 'Return Policy',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-3',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
      4 => 
      array (
        'title' => 'Privacy Policy',
        'link' => 
        array (
          'type' => 'information',
          'id' => '3',
          'href' => 'https://easoneyewear.com/privacy',
          'name' => 'Privacy Policy',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-4',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);
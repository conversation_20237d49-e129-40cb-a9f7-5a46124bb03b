<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/orderimport.twig */
class __TwigTemplate_3e531685c4ad7d8ce77cd9a2bb2b1383dbc9f82e31a539265cbfc14c8ca02f8b extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["orderaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form_userimport\" class=\"form-horizontal\">

\t<div class=\"row\">
\t\t
\t\t<!-- Coupon Group File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["entry_order_import"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#form_userimport').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t</div>
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/orderimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/orderimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/orderimport.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/pim.twig */
class __TwigTemplate_9654a647d953429abde0f6b7991f22b072721778f6ea3334dadde9dd72ccbcc4 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo " ";
        echo ($context["column_left"] ?? null);
        echo " 
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
    ";
        // line 5
        if (($context["error_warning"] ?? null)) {
            echo " 
    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            // line 6
            echo ($context["error_warning"] ?? null);
            echo " 
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 9
        echo " 
    ";
        // line 10
        if (($context["success"] ?? null)) {
            echo " 
    <div class=\"alert alert-success\"><i class=\"fa fa-check-circle\"></i> ";
            // line 11
            echo ($context["success"] ?? null);
            echo " 
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 14
        echo "     
      <div class=\"pull-right\">
        <button type=\"submit\" form=\"form-ppexpress\" data-toggle=\"tooltip\" title=\"";
        // line 16
        echo ($context["button_save"] ?? null);
        echo " \" class=\"btn btn-primary\"><i class=\"fa fa-save\"></i></button>
        <a href=\"";
        // line 17
        echo ($context["cancel"] ?? null);
        echo " \" data-toggle=\"tooltip\" title=\"";
        echo ($context["button_cancel"] ?? null);
        echo " \" class=\"btn btn-default\"><i class=\"fa fa-reply\"></i></a></div>
      <h1>";
        // line 18
        echo ($context["heading_title"] ?? null);
        echo " </h1>
      <ul class=\"breadcrumb\">
        ";
        // line 20
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            echo " 
        <li><a href=\"";
            // line 21
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 21);
            echo " \">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 21);
            echo " </a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        echo " 
      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 27
        if (twig_get_attribute($this->env, $this->source, ($context["error"] ?? null), "error_warning", [], "any", false, false, false, 27)) {
            echo " 
    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            // line 28
            echo twig_get_attribute($this->env, $this->source, ($context["error"] ?? null), "error_warning", [], "any", false, false, false, 28);
            echo " 
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 31
        echo " 
    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i>Module settings</h3>
      </div>
      <div class=\"panel-body\">
        <form action=\"";
        // line 37
        echo ($context["action"] ?? null);
        echo " \" method=\"post\" enctype=\"multipart/form-data\" id=\"form-multiimage\" class=\"form-horizontal\">
          <ul class=\"nav nav-tabs\">
            <li class=\"active\"><a href=\"#tab-general\" data-toggle=\"tab\">";
        // line 39
        echo ($context["tab_general"] ?? null);
        echo " </a></li>
            <li><a href=\"#tab-volumes\" data-toggle=\"tab\">";
        // line 40
        echo ($context["tab_volume"] ?? null);
        echo " </a></li>
            <li><a href=\"#tab-modules\" data-toggle=\"tab\">";
        // line 41
        echo ($context["tab_module"] ?? null);
        echo " </a></li>  
            <li><a href=\"#tab-about\" data-toggle=\"tab\">";
        // line 42
        echo ($context["tab_help"] ?? null);
        echo " </a></li>
          </ul>
          <div class=\"tab-content\">
            <div class=\"tab-pane active\" id=\"tab-general\">
             <fieldset>
              <legend>";
        // line 47
        echo "General Settings";
        echo " </legend>

              <div class=\"tab-pane active\" id=\"tab-api-details\">
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 51
        echo ($context["entry_status"] ?? null);
        echo " </label>
                <div class=\"col-sm-2\">
                  <select name=\"pim_status\" id=\"input-status\" class=\"form-control\">
                    ";
        // line 54
        if (($context["pim_status"] ?? null)) {
            echo " 
                    <option value=\"1\" selected=\"selected\">";
            // line 55
            echo ($context["text_enabled"] ?? null);
            echo " </option>
                    <option value=\"0\">";
            // line 56
            echo ($context["text_disabled"] ?? null);
            echo " </option>
                    ";
        } else {
            // line 57
            echo "   
                    <option value=\"1\">";
            // line 58
            echo ($context["text_enabled"] ?? null);
            echo " </option>
                    <option value=\"0\" selected=\"selected\">";
            // line 59
            echo ($context["text_disabled"] ?? null);
            echo " </option>
                    ";
        }
        // line 60
        echo "  
                  </select>
                </div>
              </div>                  
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 65
        echo ($context["entry_miu_patch"] ?? null);
        echo " </label>
                <div class=\"col-sm-2\">
                  <select name=\"pim_miu\" id=\"input-status\" class=\"form-control\">
                    ";
        // line 68
        if (($context["pim_miu"] ?? null)) {
            echo " 
                    <option value=\"1\" selected=\"selected\">";
            // line 69
            echo ($context["text_enabled"] ?? null);
            echo " </option>
                    <option value=\"0\">";
            // line 70
            echo ($context["text_disabled"] ?? null);
            echo " </option>
                    ";
        } else {
            // line 71
            echo "   
                    <option value=\"1\">";
            // line 72
            echo ($context["text_enabled"] ?? null);
            echo " </option>
                    <option value=\"0\" selected=\"selected\">";
            // line 73
            echo ($context["text_disabled"] ?? null);
            echo " </option>
                    ";
        }
        // line 74
        echo "  
                  </select>
                </div>
              </div>          
              </div>
              </fieldset>
              <fieldset>
              <legend>";
        // line 81
        echo "Server Settings";
        echo " </legend>
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 83
        echo ($context["entry_delete_def_image"] ?? null);
        echo " </label>
                <div class=\"col-sm-4\">
                  <select name=\"pim_deletedef\" id=\"input-status\" class=\"form-control\">
\t\t\t\t\t\t\t\t\t\t";
        // line 86
        if (($context["pim_deletedef"] ?? null)) {
            echo " 
                    <option value=\"1\" selected=\"selected\">";
            // line 87
            echo ($context["text_yes"] ?? null);
            echo " </option>
                    <option value=\"0\">";
            // line 88
            echo ($context["text_no"] ?? null);
            echo " </option>
                    ";
        } else {
            // line 89
            echo "   
                    <option value=\"1\">";
            // line 90
            echo ($context["text_yes"] ?? null);
            echo " </option>
                    <option value=\"0\" selected=\"selected\">";
            // line 91
            echo ($context["text_no"] ?? null);
            echo " </option>
                    ";
        }
        // line 92
        echo "  \t\t\t\t\t\t\t\t
                  
                  </select>
                </div>
              </div>                  
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 98
        echo ($context["entry_copyOverwrite"] ?? null);
        echo " </label>
                <div class=\"col-sm-4\">
                  <select name=\"pim_copyOverwrite\" id=\"input-status\" class=\"form-control\">
                  ";
        // line 101
        if (($context["pim_copyOverwrite"] ?? null)) {
            echo " 
                  <option value=\"1\" selected=\"selected\">";
            // line 102
            echo ($context["text_yes"] ?? null);
            echo " </option>
                  <option value=\"0\">";
            // line 103
            echo ($context["text_no"] ?? null);
            echo " </option>
                  ";
        } else {
            // line 104
            echo "   
                  <option value=\"1\">";
            // line 105
            echo ($context["text_yes"] ?? null);
            echo " </option>
                  <option value=\"0\" selected=\"selected\">";
            // line 106
            echo ($context["text_no"] ?? null);
            echo " </option>
                  ";
        }
        // line 107
        echo "  
                  </select>
                </div>
              </div>          
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 112
        echo ($context["entry_uploadOverwrite"] ?? null);
        echo " </label>
                <div class=\"col-sm-4\">
                  <select name=\"pim_uploadOverwrite\" id=\"input-status\" class=\"form-control\">
                  ";
        // line 115
        if (($context["pim_uploadOverwrite"] ?? null)) {
            echo " 
                  <option value=\"1\" selected=\"selected\">";
            // line 116
            echo ($context["text_yes"] ?? null);
            echo " </option>
                  <option value=\"0\">";
            // line 117
            echo ($context["text_no"] ?? null);
            echo " </option>
                  ";
        } else {
            // line 118
            echo "   
                  <option value=\"1\">";
            // line 119
            echo ($context["text_yes"] ?? null);
            echo " </option>
                  <option value=\"0\" selected=\"selected\">";
            // line 120
            echo ($context["text_no"] ?? null);
            echo " </option>
                  ";
        }
        // line 121
        echo "  
                  </select>
                </div>
              </div>                  
              <div class=\"form-group\">
                <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 126
        echo ($context["entry_uploadMaxSize"] ?? null);
        echo " </label>
                <div class=\"col-sm-2\">
                   <input type=\"text\" class=\"form-control\" name=\"pim_uploadMaxSize\" value=\"";
        // line 128
        echo ($context["pim_uploadMaxSize"] ?? null);
        echo " \" size=\"4\" />
                </div>
                <div class=\"col-sm-2\"><select name=\"pim_uploadMaxType\" id=\"input-status\" class=\"form-control\">
                  ";
        // line 131
        if (((($context["pim_uploadMaxType"] ?? null) == "M") ||  !($context["pim_uploadMaxType"] ?? null))) {
            echo " 
                  <option value=\"M\" selected>MegaBytes (MB)</option>
                  <option value=\"K\">KiloBytes (KB)</option>
                  ";
        } else {
            // line 134
            echo "   
                  <option value=\"M\" >MegaBytes (MB)</option>
                  <option value=\"K\" selected>KiloBytes (KB)</option>
                  ";
        }
        // line 137
        echo " \t\t\t\t\t\t\t\t
                  </select>
                </div>                
              </div>                   
              </fieldset>
              <fieldset>
                <legend>";
        // line 143
        echo "View Settings";
        echo " </legend>
                <div class=\"form-group\">
                  <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 145
        echo ($context["entry_language"] ?? null);
        echo " </label>
                  <div class=\"col-sm-4\">
                    <select name=\"pim_language\"  class=\"form-control\">
                    <option value=\"\"> EN </option>
                    ";
        // line 149
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["langs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["l"]) {
            // line 150
            echo "                        <option value=\"";
            echo $context["l"];
            echo "\" ";
            if (($context["l"] == ($context["pim_language"] ?? null))) {
                echo " ";
                echo " selected";
            }
            echo ">";
            echo twig_upper_filter($this->env, $context["l"]);
            echo "</option>
\t\t\t\t\t\t\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['l'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 152
        echo "                     
                  </select>
                  </div>
                </div>  
                <div class=\"form-group\">
                  <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 157
        echo ($context["entry_dimensions"] ?? null);
        echo " </label>
                  <div class=\"col-sm-6\">
                     <div class=\"col-sm-3\">
                    <input type=\"text\" class=\"form-control\" name=\"pim_width\" value=\"";
        // line 160
        echo ($context["pim_width"] ?? null);
        echo " \" size=\"4\" /> </div> <div class=\"col-sm-1\">x</div> <div class=\"col-sm-3\"><input type=\"text\" class=\"form-control\" name=\"pim_height\" value=\"";
        echo ($context["pim_height"] ?? null);
        echo " \" size=\"4\" /></div>
                  </div>
                </div>                     
              </fieldset>
            </div>
            <div class=\"tab-pane\" id=\"tab-volumes\">
              <div class=\"tab-pane\">
                <div class=\"table-responsive\">
                  <table class=\"table table-bordered table-hover\">
                    <thead>
                      <tr>
                        <td class=\"text-left\">";
        // line 171
        echo ($context["column_name"] ?? null);
        echo " </td>
                        <td class=\"text-left\">";
        // line 172
        echo ($context["column_description"] ?? null);
        echo "</td>
                        <td class=\"text-right\">";
        // line 173
        echo ($context["column_action"] ?? null);
        echo " </td>
                      </tr>
                    </thead>
                    <tbody>
                      ";
        // line 177
        if (($context["volumes"] ?? null)) {
            echo " 
                      ";
            // line 178
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["volumes"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["extension"]) {
                echo " 
                      <tr>
                        <td>";
                // line 180
                echo twig_get_attribute($this->env, $this->source, $context["extension"], "name", [], "any", false, false, false, 180);
                echo " </td>
                        <td>";
                // line 181
                echo twig_get_attribute($this->env, $this->source, $context["extension"], "text", [], "any", false, false, false, 181);
                echo " </td>
                        <td class=\"text-right\">";
                // line 182
                if ( !twig_get_attribute($this->env, $this->source, $context["extension"], "installed", [], "any", false, false, false, 182)) {
                    echo " 
                          <a href=\"";
                    // line 183
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "install", [], "any", false, false, false, 183);
                    echo " \" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_install"] ?? null);
                    echo " \" class=\"btn btn-success\"><i class=\"fa fa-plus-circle\"></i></a>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                } else {
                    // line 184
                    echo "   
                          <a onclick=\"confirm('";
                    // line 185
                    echo ($context["text_confirm"] ?? null);
                    echo " ') ? location.href='";
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "uninstall", [], "any", false, false, false, 185);
                    echo " ' : false;\" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_uninstall"] ?? null);
                    echo " \" class=\"btn btn-danger\"><i class=\"fa fa-minus-circle\"></i></a>
                          ";
                }
                // line 186
                echo " 
                          ";
                // line 187
                if (twig_get_attribute($this->env, $this->source, $context["extension"], "installed", [], "any", false, false, false, 187)) {
                    echo " 
                          <a href=\"";
                    // line 188
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "edit", [], "any", false, false, false, 188);
                    echo " \" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_add"] ?? null);
                    echo " \" class=\"btn btn-primary\"><i class=\"fa fa-plus\"></i></a>
                          ";
                } else {
                    // line 189
                    echo "   
                          <button type=\"button\" class=\"btn btn-primary\" disabled=\"disabled\"><i class=\"fa fa-pencil\"></i></button>
\t\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                // line 191
                echo " 
                           </td>
                      </tr>
                        ";
                // line 194
                if ( !twig_test_empty(twig_get_attribute($this->env, $this->source, $context["extension"], "childs", [], "any", false, false, false, 194))) {
                    // line 195
                    echo "                          ";
                    $context['_parent'] = $context;
                    $context['_seq'] = twig_ensure_traversable(twig_get_attribute($this->env, $this->source, $context["extension"], "childs", [], "any", false, false, false, 195));
                    foreach ($context['_seq'] as $context["_key"] => $context["child"]) {
                        echo " 
                            <tr>
                              <td colspan=\"2\">";
                        // line 197
                        echo twig_get_attribute($this->env, $this->source, $context["extension"], "name", [], "any", false, false, false, 197);
                        echo "  > <b>(";
                        echo twig_get_attribute($this->env, $this->source, $context["child"], "name", [], "any", false, false, false, 197);
                        echo ")</b></td>
                              <td class=\"text-right\">
                                <a onclick=\"confirm('";
                        // line 199
                        echo ($context["text_confirm"] ?? null);
                        echo " ') ? location.href='";
                        echo twig_get_attribute($this->env, $this->source, $context["child"], "delete", [], "any", false, false, false, 199);
                        echo " ' : false;\" data-toggle=\"tooltip\" title=\"";
                        echo ($context["button_delete"] ?? null);
                        echo " \" class=\"btn btn-danger\"><i class=\"fa fa-trash-o\"></i></a>
                                <a href=\"";
                        // line 200
                        echo twig_get_attribute($this->env, $this->source, $context["child"], "edit", [], "any", false, false, false, 200);
                        echo " \" data-toggle=\"tooltip\" title=\"";
                        echo ($context["button_edit"] ?? null);
                        echo " \" class=\"btn btn-primary\"><i class=\"fa fa-pencil\"></i></a>
                            
                              </td>
                            </tr>
                          ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_iterated'], $context['_key'], $context['child'], $context['_parent'], $context['loop']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 205
                    echo "\t\t\t\t\t\t\t\t\t\t\t\t";
                }
                echo "                          
                       ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['extension'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 206
            echo "\t\t\t\t\t\t\t\t\t\t\t 
                       ";
        } else {
            // line 207
            echo "   
                      <tr>
                        <td class=\"text-center\" colspan=\"2\">";
            // line 209
            echo ($context["text_no_results"] ?? null);
            echo " </td>
                      </tr>
                      ";
        }
        // line 211
        echo "    
                    </tbody>
                  </table>
                </div>                                 
            </div>
          </div>            
          <div class=\"tab-pane\" id=\"tab-modules\">
          <div class=\"table-responsive\">
            <table class=\"table table-bordered table-hover\">
              <thead>
                <tr>
                  <td class=\"text-left\">";
        // line 222
        echo ($context["column_name"] ?? null);
        echo " </td>
                  <td class=\"text-left\">";
        // line 223
        echo ($context["column_description"] ?? null);
        echo "</td>
                  <td class=\"text-right\">";
        // line 224
        echo ($context["column_action"] ?? null);
        echo " </td>
                </tr>
              </thead>
              <tbody>
                ";
        // line 228
        if (($context["extensions"] ?? null)) {
            echo " 
                ";
            // line 229
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["extensions"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["extension"]) {
                echo " 
                <tr>
                  <td>";
                // line 231
                echo twig_get_attribute($this->env, $this->source, $context["extension"], "name", [], "any", false, false, false, 231);
                echo " </td>
                  <td>";
                // line 232
                echo twig_get_attribute($this->env, $this->source, $context["extension"], "text", [], "any", false, false, false, 232);
                echo " </td>
                  <td class=\"text-right\">";
                // line 233
                if ( !twig_get_attribute($this->env, $this->source, $context["extension"], "installed", [], "any", false, false, false, 233)) {
                    echo " 
                    <a href=\"";
                    // line 234
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "install", [], "any", false, false, false, 234);
                    echo " \" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_install"] ?? null);
                    echo " \" class=\"btn btn-success\"><i class=\"fa fa-plus-circle\"></i></a>
                    ";
                } else {
                    // line 235
                    echo "   
                    <a onclick=\"confirm('";
                    // line 236
                    echo ($context["text_confirm"] ?? null);
                    echo " ') ? location.href='";
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "uninstall", [], "any", false, false, false, 236);
                    echo " ' : false;\" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_uninstall"] ?? null);
                    echo " \" class=\"btn btn-danger\"><i class=\"fa fa-minus-circle\"></i></a>
                    ";
                }
                // line 237
                echo " 
                    ";
                // line 238
                if (twig_get_attribute($this->env, $this->source, $context["extension"], "installed", [], "any", false, false, false, 238)) {
                    echo " 
                    <a href=\"";
                    // line 239
                    echo twig_get_attribute($this->env, $this->source, $context["extension"], "edit", [], "any", false, false, false, 239);
                    echo " \" data-toggle=\"tooltip\" title=\"";
                    echo ($context["button_edit"] ?? null);
                    echo " \" class=\"btn btn-primary\"><i class=\"fa fa-pencil\"></i></a>
                    ";
                } else {
                    // line 240
                    echo "   
                    <button type=\"button\" class=\"btn btn-primary\" disabled=\"disabled\"><i class=\"fa fa-pencil\"></i></button>
\t\t\t\t\t\t\t\t\t\t";
                }
                // line 242
                echo " 
                     </td>
                </tr>
                 ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['extension'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 246
            echo "                 ";
        } else {
            echo "   
                <tr>
                  <td class=\"text-center\" colspan=\"2\">";
            // line 248
            echo ($context["text_no_results"] ?? null);
            echo " </td>
                </tr>
                 ";
        }
        // line 250
        echo " 
              </tbody>
            </table>
          </div>                                 
          </div>
            
            <div class=\"tab-pane\" id=\"tab-about\">
      \t\t\t  <h3>Welcome and thank you for purchsing our module!</h3>
      \t\t\t  <div class=\"panel panel-default\">
      \t\t\t    <div class=\"panel-heading\"><h4 class=\"panel-title\">About This Module</h4></div>
      \t\t\t    <div class=\"panel-body\"><strong>Power Image Manager</strong> is another great module developed by <a href=\"http://bit.ly/1vHShWu\" target=\"_blank\">Sharley's</a></div>
      \t\t\t  </div>
      \t\t\t  <div class=\"panel panel-default\">
      \t\t\t    <div class=\"panel-heading\"><h4 class=\"panel-title\">Need Support?</h4></div>
      \t\t\t    <div class=\"panel-body\">
      \t\t\t      <a href=\"mailto: <EMAIL>?subject=Power Image Manager (OCV2) support on <?php echo HTTP_CATALOG; ?>\" class=\"btn btn-success\"> <i class=\"fa fa-life-ring fa-lg\"></i> Contact Us</a>
      \t\t\t      <a href=\"http://on.fb.me/1inp4Ik\" target=\"_blank\" class=\"btn btn-primary\"> <i class=\"fa fa-thumbs-up\"></i> Follow us on Facebook</a>
      \t\t\t    </div>
      \t\t\t  </div>
      \t\t\t  <div class=\"panel panel-default\">
      \t\t\t    <div class=\"panel-heading\"><h4 class=\"panel-title\">Happy about our modules?</h4></div>
      \t\t\t    <div class=\"panel-body\">Please give as your vote &amp; comment on <a href=\"http://bit.ly/1vHShWu\" target=\"_blank\">Opencart Extension Store</a></div>
      \t\t\t  </div>
              </div>            
            </div>
          </div> 
        </form>
      </div>
    </div>
  </div>
";
    }

    public function getTemplateName()
    {
        return "extension/module/pim.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  707 => 250,  701 => 248,  695 => 246,  686 => 242,  681 => 240,  674 => 239,  670 => 238,  667 => 237,  658 => 236,  655 => 235,  648 => 234,  644 => 233,  640 => 232,  636 => 231,  629 => 229,  625 => 228,  618 => 224,  614 => 223,  610 => 222,  597 => 211,  591 => 209,  587 => 207,  583 => 206,  574 => 205,  561 => 200,  553 => 199,  546 => 197,  538 => 195,  536 => 194,  531 => 191,  526 => 189,  519 => 188,  515 => 187,  512 => 186,  503 => 185,  500 => 184,  493 => 183,  489 => 182,  485 => 181,  481 => 180,  474 => 178,  470 => 177,  463 => 173,  459 => 172,  455 => 171,  439 => 160,  433 => 157,  426 => 152,  410 => 150,  406 => 149,  399 => 145,  394 => 143,  386 => 137,  380 => 134,  373 => 131,  367 => 128,  362 => 126,  355 => 121,  350 => 120,  346 => 119,  343 => 118,  338 => 117,  334 => 116,  330 => 115,  324 => 112,  317 => 107,  312 => 106,  308 => 105,  305 => 104,  300 => 103,  296 => 102,  292 => 101,  286 => 98,  278 => 92,  273 => 91,  269 => 90,  266 => 89,  261 => 88,  257 => 87,  253 => 86,  247 => 83,  242 => 81,  233 => 74,  228 => 73,  224 => 72,  221 => 71,  216 => 70,  212 => 69,  208 => 68,  202 => 65,  195 => 60,  190 => 59,  186 => 58,  183 => 57,  178 => 56,  174 => 55,  170 => 54,  164 => 51,  157 => 47,  149 => 42,  145 => 41,  141 => 40,  137 => 39,  132 => 37,  124 => 31,  117 => 28,  113 => 27,  106 => 22,  96 => 21,  90 => 20,  85 => 18,  79 => 17,  75 => 16,  71 => 14,  64 => 11,  60 => 10,  57 => 9,  50 => 6,  46 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/pim.twig", "");
    }
}

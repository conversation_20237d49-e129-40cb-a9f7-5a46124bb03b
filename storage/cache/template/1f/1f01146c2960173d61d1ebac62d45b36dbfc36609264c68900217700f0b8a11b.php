<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/excelport.twig */
class __TwigTemplate_b29612100c116107e08b223a90ad0bd5db3d58fb27b21286aca6509a66298df1 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <form action=\"";
        // line 3
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <div class=\"pull-right\">
        <button type=\"submit\" form=\"form\" data-toggle=\"tooltip\" title=\"";
        // line 7
        echo ($context["button_save"] ?? null);
        echo "\" class=\"btn btn-primary save-changes\"><i class=\"fa fa-save\"></i></button>
        <a href=\"";
        // line 8
        echo ($context["cancel"] ?? null);
        echo "\" data-toggle=\"tooltip\" title=\"";
        echo ($context["button_cancel"] ?? null);
        echo "\" class=\"btn btn-default\"><i class=\"fa fa-reply\"></i></a></div>
      <h1>";
        // line 9
        echo ($context["heading_title_version"] ?? null);
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 11
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 12
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 12);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 12);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 14
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 18
        if (($context["error_excelport_licensed_on"] ?? null)) {
            // line 19
            echo "      <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_excelport_licensed_on"] ?? null);
            echo "
        <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
      </div>
    ";
        }
        // line 23
        echo "    ";
        if (($context["error_warning"] ?? null)) {
            // line 24
            echo "    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 28
        echo "    ";
        if (($context["success_message"] ?? null)) {
            // line 29
            echo "    <div class=\"alert alert-success\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["success_message"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 33
        echo "    <div class=\"panel panel-default\">
      <div class=\"panel-body\">
        <ul class=\"nav nav-tabs\">
          ";
        // line 36
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["tabs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["tab"]) {
            // line 37
            echo "          <li><a class=\"excelport_tab\" href=\"#tab-";
            echo twig_get_attribute($this->env, $this->source, $context["tab"], "id", [], "any", false, false, false, 37);
            echo "\" data-toggle=\"tab\">";
            echo twig_get_attribute($this->env, $this->source, $context["tab"], "name", [], "any", false, false, false, 37);
            echo "</a></li>
          ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['tab'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 39
        echo "        </ul>
        
          <div class=\"tab-content\">
          ";
        // line 42
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["tabs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["tab"]) {
            // line 43
            echo "            <div class=\"tab-pane\" id=\"tab-";
            echo twig_get_attribute($this->env, $this->source, $context["tab"], "id", [], "any", false, false, false, 43);
            echo "\">
              ";
            // line 44
            echo twig_get_attribute($this->env, $this->source, $context["tab"], "content", [], "any", false, false, false, 44);
            echo "
            </div>
          ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['tab'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 47
        echo "          </div>
        
      </div>
    </div>
  </div>
  </form>
</div>

<div id=\"progress-dialog\" class=\"modal\" data-backdrop=\"static\" style=\"display: none;\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content padding20\">
      <div id=\"progressbar\">
        <div class=\"progress\">
          <div class=\"progress-bar\" role=\"progressbar\" aria-valuenow=\"0\" aria-valuemin=\"0\" aria-valuemax=\"100\">
          </div>
        </div>
      </div>
      <div id=\"progressinfo\"></div>
      <button class=\"btn btn-default finishActionButton\" style=\"display: none;\">Abort</button>
    </div>
  </div>
</div>

<script type=\"text/javascript\">

var jq = jQuery; // Intended for MijoShop

jq('li[id^=\"toolbar-popup\"]').remove();

var xhr;
var loopXHR;
var pageTitle = jq('title').html();
var abort = false;
var sending = false;
var updateTimeout = null;
var loopXHR = null;
var site_url = null;
var lastMemory = 0;
var unidentifiedError = false;

var closeDialog = function() {
  jq(\"#progress-dialog\").modal(\"hide\");
  jq('#progressinfo').empty();
  jq('#progressbar .progress-bar').attr('aria-valuenow', '0');
  jq('#progressbar .progress-bar').css('width', '0%');
  jq('.finishActionButton').hide();
}

jq('.finishActionButton').click(function() {
  abort = true;
  loopXHR.abort();
  clearTimeout(updateTimeout);
  jq(this).html('<img src=\"./view/image/excelport/ajax-loader.gif\" class=\"loadingImage2\"/>');
  jq(this).attr('disabled', 'disabled');
  jq('#progressinfo').html('Aborting... Please wait...');
  jq('title').html(pageTitle);
  if (!sending) closeDialog();
});

jq(document).ajaxSend(function() {
  sending = true; 
});

jq(document).ajaxStop(function() {
  sending = false;
  if (abort) {
    closeDialog();
  }
});

var dependable = 'input[name=\"ExcelPort[Export][DataType]\"], input[name=\"ExcelPort[Import][DataType]\"]';

jq(dependable).each(function() {
  jq(this).change(function() {
    jq('*[data-depends-on]').each(function() {
      if (jq(jq(this).attr('data-depends-on')).is(':checked') || jq(jq(this).attr('data-depends-on')).is(':selected')) {
        jq(this).slideDown(100);
      } else {
        jq(this).hide();
      }
    });
  });
  jq(this).trigger('change');
});

jq( \"#progress-dialog\" ).modal({
  backdrop : 'static',
  show : false
});

switch (location.protocol) {
  case 'https:': 
    site_url = '";
        // line 139
        echo ($context["https_server"] ?? null);
        echo "';
    break;
  default:
    site_url = '";
        // line 142
        echo ($context["http_server"] ?? null);
        echo "';
    break;
}

if (document.location.href.indexOf('com_mijoshop') != -1) site_url += '/components/com_mijoshop/opencart';

if (document.location.href.indexOf('com_jcart') != -1) site_url += '/components/com_jcart';

if (document.location.href.indexOf('com_opencart') != -1) site_url += '/components/com_opencart';

var selected_tab = function() {
  var tab_id = localStorage.getItem('excelport_tab');

  if (!tab_id) {
    tab_id = jq('.excelport_tab').first().attr('href');
    localStorage.setItem('excelport_tab', tab_id);
  }

  return tab_id;
}

jq('.excelport_tab').click(function() {
  localStorage.setItem('excelport_tab', jq(this).attr('href'));
});

jq('.excelport_tab[href=\"' + selected_tab() + '\"]').trigger('click');

jq(window).on('load', function() {
  var downloaded = false;
  var importing = false;
  var ajaxgenerate = ";
        // line 172
        echo ($context["ajaxgenerate"] ?? null);
        echo ";
  var ajaximport = ";
        // line 173
        echo ($context["ajaximport"] ?? null);
        echo ";
  var token = '';
  var vars = window.location.search.split('&');
  var progressText = ['', ''];
  var progressRegime = ajaxgenerate ? 'Export' : 'Import';
  
  var conditions = ";
        // line 179
        echo ($context["conditions"] ?? null);
        echo ";
  var operations = ";
        // line 180
        echo ($context["operations"] ?? null);
        echo ";
  var enabled_conditions = ";
        // line 181
        echo ($context["enabled_conditions"] ?? null);
        echo ";
  
  var conditionsIndexes = {};
  for (var i in conditions) {
    conditionsIndexes[i] = 0;
  }

  if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Products') {
    progressText = [
      '";
        // line 190
        echo ($context["text_datatype_option_products"] ?? null);
        echo "',
      '";
        // line 191
        echo ($context["text_datatype_option_products_lowercase"] ?? null);
        echo "'
    ];  
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Categories') {
    progressText = [
      '";
        // line 195
        echo ($context["text_datatype_option_categories"] ?? null);
        echo "',
      '";
        // line 196
        echo ($context["text_datatype_option_categories_lowercase"] ?? null);
        echo "'
    ];    
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Options') {
    progressText = [
      '";
        // line 200
        echo ($context["text_datatype_option_options"] ?? null);
        echo "',
      '";
        // line 201
        echo ($context["text_datatype_option_options_lowercase"] ?? null);
        echo "',
    ];    
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Attributes') {
    progressText = [
      '";
        // line 205
        echo ($context["text_datatype_option_attributes"] ?? null);
        echo "',
      '";
        // line 206
        echo ($context["text_datatype_option_attributes_lowercase"] ?? null);
        echo "'
    ];    
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Filters') {
    progressText = [
      '";
        // line 210
        echo ($context["text_datatype_option_filters"] ?? null);
        echo "',
      '";
        // line 211
        echo ($context["text_datatype_option_filters_lowercase"] ?? null);
        echo "'
    ];    
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Customers') {
    progressText = [
      '";
        // line 215
        echo ($context["text_datatype_option_customers"] ?? null);
        echo "',
      '";
        // line 216
        echo ($context["text_datatype_option_customers_lowercase"] ?? null);
        echo "'
    ];    
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'CustomerGroups') {
    progressText = [
      '";
        // line 220
        echo ($context["text_datatype_option_customer_groups"] ?? null);
        echo "',
      '";
        // line 221
        echo ($context["text_datatype_option_customer_groups_lowercase"] ?? null);
        echo "'
    ];
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'OrderStatuses') {
    progressText = [
      '";
        // line 225
        echo ($context["text_datatype_option_order_statuses"] ?? null);
        echo "',
      '";
        // line 226
        echo ($context["text_datatype_option_order_statuses_lowercase"] ?? null);
        echo "'
    ];
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Options') {
    progressText = [
            '";
        // line 230
        echo ($context["text_datatype_option_options"] ?? null);
        echo "',
      '";
        // line 231
        echo ($context["text_datatype_option_options_lowercase"] ?? null);
        echo "'
        ];      
    } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Manufacturers') {
    progressText = [
            '";
        // line 235
        echo ($context["text_datatype_option_manufacturers"] ?? null);
        echo "',
      '";
        // line 236
        echo ($context["text_datatype_option_manufacturers_lowercase"] ?? null);
        echo "'
        ];      
    } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Coupons') {
    progressText = [
            '";
        // line 240
        echo ($context["text_datatype_option_coupons"] ?? null);
        echo "',
      '";
        // line 241
        echo ($context["text_datatype_option_coupons_lowercase"] ?? null);
        echo "'
        ];      
    } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Vouchers') {
    progressText = [
            '";
        // line 245
        echo ($context["text_datatype_option_vouchers"] ?? null);
        echo "',
      '";
        // line 246
        echo ($context["text_datatype_option_vouchers_lowercase"] ?? null);
        echo "'
        ];      
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Downloads') {
    progressText = [
      '";
        // line 250
        echo ($context["text_datatype_option_downloads"] ?? null);
        echo "',
      '";
        // line 251
        echo ($context["text_datatype_option_downloads_lowercase"] ?? null);
        echo "'
    ];
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'Reviews') {
    progressText = [
      '";
        // line 255
        echo ($context["text_datatype_option_reviews"] ?? null);
        echo "',
      '";
        // line 256
        echo ($context["text_datatype_option_reviews_lowercase"] ?? null);
        echo "'
    ];
  } else if (jq('input[name=\"ExcelPort[' + progressRegime + '][DataType]\"]:checked').val() == 'MigrateAffiliates') {
    progressText = [
      '";
        // line 260
        echo ($context["text_datatype_option_migrate_affiliate"] ?? null);
        echo "',
      '";
        // line 261
        echo ($context["text_datatype_option_migrate_affiliate_lowercase"] ?? null);
        echo "'
    ];
  }

  for (var i = 0; i < vars.length; i++) {
    var parts = vars[i].split('=');
    if (parts[0] == 'user_token') token = parts[1];  
  }
  var timer = null;
  var seconds;
  
  var zeroPad = function (num, places) {
    var zero = places - num.toString().length + 1;
    return Array(+(zero > 0 && zero)).join(\"0\") + num;
  }
  
    var basename = function(path) {
        return path.replace(/^.*\\/(.*?)\$/g, '\$1');
    }

    var setLastImport = function(text) {
        jq('#last_import').hide();

        if (typeof text == 'undefined') {
            text = jq('#last_import').attr('data-text');
        }

        text = basename(text);

        if (text != '') {
            var value = jq('#last_import').attr('data-template').replace('{FILE}', text);
            jq('#last_import').html(value);
            jq('#last_import_input').val(text);
            jq('#last_import').show();
        }
    }

    setLastImport();

  var progress = function(message, isError) {
    if (isError !== false) {
      jq('#progressbar .progress-bar').attr('aria-valuenow', message.percent);
      jq('#progressbar .progress-bar').css('width', message.percent + '%');
      if ((message.current === message.all && !importing && typeof(message.populateAll) == 'undefined') || message.finishedImport) {
        jq('.finishActionButton').html('Finish');
        jq('.finishActionButton').removeAttr('disabled');
        clearInterval(timer);
        clearTimeout(updateTimeout);
        loopXHR.abort();
        if (!downloaded) {
          jq('#progressinfo').html('";
        // line 311
        echo ($context["text_file_downloading"] ?? null);
        echo "');

          setTimeout(function() {
            document.location.href = \"index.php?user_token=\" + token + \"&route=extension/module/excelport/download\";
          }, 2000);

          downloaded = true;
        }
        if (importing) {
          jq('#progressinfo').html('";
        // line 320
        echo ($context["text_import_done"] ?? null);
        echo "'.replace('{COUNT}', message.current).replace('{TYPE}', progressText[1]));
          setLastImport(message.importingFile);
        }
      } else if (importing) {
        if (message.current > 0) {
          var pps = Math.round((message.current)/seconds);
          jq('#progressinfo').html('Importing. Please wait...<br />Reading from: ' + message.importingFile + '<br />' + progressText[0] + ' per second: ' + pps + \"<br />Imported: \" + message.current);
          setLastImport(message.importingFile);
        } else {
          jq('#progressbar .progress-bar').attr('aria-valuenow', '100');
          jq('#progressbar .progress-bar').css('width', '100%');
          jq('#progressinfo').html('";
        // line 331
        echo ($context["text_preparing_data"] ?? null);
        echo "');  
        }
      } else {
        if (message.current > 0) {
          if (message.percent != 100) {
            var pps = message.current/seconds;
            var allSecondsRemaining = Math.round((message.all - message.current)/pps);
            var hoursRemaining =  zeroPad(Math.floor(allSecondsRemaining/3600), 2);
            var minutesRemaining = zeroPad(Math.floor((allSecondsRemaining%3600)/60), 2);
            var secondsRemaining = zeroPad(Math.floor((allSecondsRemaining%60)), 2);
            jq('#progressinfo').html(\"Progress: \" + message.percent + \"%<br />\" + message.current + \" \" + progressText[1] + \" were \" + (importing ? \"imported\" : \"exported\") + \"...<br />\" + Math.ceil(pps) + \" \" + progressText[1] + \" per second<br />\" + \"Estimated time left: \" + hoursRemaining + ':' + minutesRemaining + ':' + secondsRemaining);
          } else {
            jq('#progressinfo').html('";
        // line 343
        echo ($context["text_file_generating"] ?? null);
        echo "'); 
          }
        } else {
          jq('#progressinfo').html('";
        // line 346
        echo ($context["text_preparing_data"] ?? null);
        echo "');    
        }
      }
    } else {
      jq('.finishActionButton').html('Finish');
      jq('.finishActionButton').removeAttr('disabled');
      jq('#progressbar .progress-bar').css('width', '0%');
      jq('#progressbar .progress-bar').attr('aria-valuenow', '0');
      jq('#progressinfo').html(message);
      clearInterval(timer);
      clearTimeout(updateTimeout);
    }
  }
  
  var countSeconds = function() {
    seconds++;
  }
  
  var updateProgressBar = function(site_root, countinueChecking, callback) {
    countinueChecking = typeof countinueChecking == 'undefined' ? true : countinueChecking;
    if (abort) return;
    loopXHR = jq.ajax({
      url: site_root+'/";
        // line 368
        echo ($context["progress_dir"] ?? null);
        echo "/";
        echo ($context["progress_name"] ?? null);
        echo "',
      type: 'GET',
      timeout: null,
      dataType: 'json',
      cache: false,
      success: function(returnData, textStatus, jqXHR) {
        if (jq( \"#progress-dialog\" ).is(':visible')) {
          if (returnData != null && returnData.error == false) {
            if (lastMemory == returnData.memory_get_usage && unidentifiedError) {
              var megabytes = Math.round(parseInt(returnData.memory_get_usage)/1048576);
              var errorMessage = 'Error: The server may be out of memory. Currently, the script is using ' + megabytes + ' MB';
              progress(errorMessage, false);
              return;
            } else {
              lastMemory = returnData.memory_get_usage;
            }
            progress(returnData, true);
            
            if (!importing) document.title = returnData.percent + '% ' + pageTitle;
            
            if ((returnData != null && returnData.current !== returnData.all && !importing) || (!returnData.finishedImport && importing)) {
              if (!countinueChecking) {
                return;
              }
              
              updateTimeout = setTimeout(function (){
                
                updateProgressBar(site_root);
              }, 1000);
            }
          } else {
            if (returnData != null) {
              progress(returnData.message, false);
              if (!countinueChecking || (returnData.current == returnData.all && !importing)) {
                return;
              }
              
              updateTimeout = setTimeout(function (){
                
                updateProgressBar(site_root);
              }, 1000); 
            } else {
              if (!countinueChecking) {
                return;
              }
              
              updateTimeout = setTimeout(function (){
                
                updateProgressBar(site_root);
              }, 1000);
            }
          }
        } else {
          
          clearTimeout(updateTimeout);
        }
      },
      error: function() {
        if (!countinueChecking) {
          
          return;
        }
        
        updateTimeout = setTimeout(function (){
          
          updateProgressBar(site_root);
        }, 1000);
      }
    });

    if (typeof callback != 'undefined') {
      callback();
    }
  }
  
  var startAjaxGenerate = function(path, data) {
    downloaded = false;
    importing = false;
    unidentifiedError = false;
    if (abort) return;
    if (!jq( \"#progress-dialog\" ).is(\":visible\")) {
      jq( \"#progress-dialog\" ).modal( \"show\" );
      jq('.loadingImage').show(); 
      jq('.finishActionButton').show();
    }
    if (timer == null) {
      seconds = 1;
      timer = setInterval(countSeconds, 1000);
    }
    
    xhr = jq.ajax({
      url: path,
      data: data,
      async: true,
      type: 'POST',
      timeout: null,
      dataType: 'json',
      cache: false,
      statusCode: {
        500: function(){
          progress('Server error 500 has occured.', false);
        }
      },
      success: function(successData) {
        if (successData == null) {
          unidentifiedError = true;
        } else {
          if (successData.current < successData.all && successData.done) {
            startAjaxGenerate(path, data);  
          }
        }
      },
      error: function(jqXHR, textStatus, errorThrown) {
        clearTimeout(updateTimeout);
        error = true;
        
        if (textStatus == 'timeout') {
          progress('A server timeout has occured.', false);
        } else if (textStatus == 'error') {
          console.log('A server error has occured.'); 
        } else if (textStatus == 'parsererror') {
          progress(jqXHR.responseText.replace(\"<br />\", ''), false);
        }
      }
    });
  }
  
  var startAjaxImport = function(path, data) {
    importing = true;
    downloaded = true;
    unidentifiedError = false;
    if (abort) return;
    if (!jq( \"#progress-dialog\" ).is(':visible')) {
      jq( \"#progress-dialog\" ).modal( \"show\" );
      jq('.loadingImage').show(); 
      jq('.finishActionButton').show();
    }
    if (timer == null) {
      seconds = 1;
      timer = setInterval(countSeconds, 1000);
    }
    
    xhr = jq.ajax({
      url: path,
      data: data,
      async: true,
      type: 'POST',
      timeout: null,
      dataType: 'json',
      cache: false,
      statusCode: {
        500: function(){
          progress('Server error 500 has occured.', false);
        }
      },
      success: function(successData) {
        if (successData == null) {
          unidentifiedError = true;
        } else {
          if (successData.error) {
            progress(successData.message, false);
          } else {
            if (successData.done && !successData.finishedImport) {
              startAjaxImport(path, data);  
            }
          }
        }
      },
      error: function(jqXHR, textStatus, errorThrown) {
        clearTimeout(updateTimeout);
        error = true;
        
        if (textStatus == 'timeout') {
          progress('A server timeout has occured.', false);
        } else if (textStatus == 'error') {
          console.log('A server error has occured.'); 
        } else if (textStatus == 'parsererror') {
          progress(jqXHR.responseText.replace(\"<br />\", ''), false);
        }
      }
    });
  }
  
  var triggerFiltersDisplay = function() {
    var checked = jq('input[name=\"ExcelPort[Export][DataType]\"]:checked').val();
    
    if (jq('#toggle_filter').val() == '1') {
      jq('.dataTypeFilter').each(function(index, element) {
        if (jq(element).attr('data-type') == checked) jq(element).slideDown();
        else jq(element).slideUp();
      });
    } else {
      jq('.dataTypeFilter').slideUp();
    }
  }
  
  var getOperations = function(category, operation, index) {
    var html = '<select name=\"ExcelPort[Export][Filters][' + category + '][' + index + '][Condition]\">';
    
    for (var i in operations) {
      html  += '<option value=\"' + i + '\"' + ((typeof(operation) != 'undefined' && operation == i) ? ' selected=\"selected\"' : '') + '>' + operations[i].html + '</option>';
    }
    
    html    += '</select>';
    
    return html;
  }
  
  var refreshOperation = function(field, type, predefine) {
    jq(field).find('option').attr('disabled', 'disabled');
    if (typeof(type) != 'undefined') {
      jq(field).closest('.hideable').show();
      
      if (type == 'text') jq(field).find('option[value^=\"text_\"]').removeAttr('disabled');
      else if (type=='number') jq(field).find('option[value^=\"number_\"]').removeAttr('disabled');
      else jq(field).find('option').removeAttr('disabled');
      
      if (predefine) {
        jq(field).find('option').removeAttr('selected');
        jq(field).find('option[disabled!=\"disabled\"]:first').attr('selected', 'selected');
      }
    }

    if (jq(field).find('option[selected][disabled!=\"disabled\"]').length == 0) {
        jq(field).find('option[disabled!=\"disabled\"]:first').attr('selected', 'selected');
    }
  }
  
  var getFields = function(category, field_name, index) {
    var html = '<select name=\"ExcelPort[Export][Filters][' + category + '][' + index + '][Field]\">';
    for (var i in conditions[category]) {
      html += '<option value=\"' + i + '\"' + ((typeof(field_name) != 'undefined' && i == field_name) ? ' selected=\"selected\"' : '') + '>' + conditions[category][i].label + '</option>';
    }
    html    += '</select>';
    
    return html;
  }
  
  var addCondition = function(category, field_name, operation, value) {
    if (typeof(category) != 'undefined') {
      html  = '<tr>';
      html += ' <td>' + getFields(category, field_name, conditionsIndexes[category]) + '';
      
      html += ' <span class=\"hideable\">' + getOperations(category, operation, conditionsIndexes[category]) +'';
      html += ' ";
        // line 612
        echo ($context["text_the_value"] ?? null);
        echo " <input type=\"text\"' + ((typeof(value) != 'undefined') ? ' value=\"' + value + '\"' : '') + ' name=\"ExcelPort[Export][Filters][' + category + '][' + conditionsIndexes[category] + '][Value]\" /></span></td>';
      html += ' <td class=\"right\"><a class=\"btn btn-danger discardCondition\"><i class=\"icon-trash icon-white\"></i> ";
        // line 613
        echo ($context["button_discard_condition"] ?? null);
        echo "</a></td>';
      html += '</tr>';
      
      jq('.dataTypeFilter[data-type=\"' + category + '\"] table tbody').append(html);
      if (typeof(field_name) != 'undefined') {
        if (typeof(conditions[category][field_name]) != 'undefined') {
        refreshOperation('select[name=\"ExcelPort[Export][Filters][' + category + '][' + conditionsIndexes[category] + '][Condition]\"]', conditions[category][field_name].type, false);
        }
      } else {
        for (var j in conditions[category]) { refreshOperation('select[name=\"ExcelPort[Export][' + category + '][' + conditionsIndexes[category] + '][Condition]\"]', conditions[category][j].type, true); break; }
      }
      jq('select[name=\"ExcelPort[Export][Filters][' + category + '][' + conditionsIndexes[category] + '][Field]\"]').change({index: conditionsIndexes[category]}, function(e, data) {
        refreshOperation('select[name=\"ExcelPort[Export][Filters][' + category + '][' + e.data.index + '][Condition]\"]', conditions[category][jq(this).val()].type, false);
      }).trigger('change');
      
      jq('.discardCondition').unbind('click').click(function() {
        jq(this).closest('tr').remove();
      });
      
      conditionsIndexes[category]++;
    }
  }
  
  for (var i in enabled_conditions) {
    var added = false;
    for (var j in enabled_conditions[i]) {
      if (typeof(enabled_conditions[i][j].Field) != 'undefined') {
        addCondition(i, enabled_conditions[i][j].Field, enabled_conditions[i][j].Condition, enabled_conditions[i][j].Value);
        added = true;
      }
    }
    if (!added) addCondition(i);
  }
  
  if (ajaxgenerate) {
    jq('#generateLoading').show();

    var exportData = {
      ExcelPort : {
        Export : {
          DataType : jq('input[name=\"ExcelPort[Export][DataType]\"]:checked').val(),
          Store : jq('input[name=\"ExcelPort[Export][Store]\"]:checked').val(),
          Language : jq('input[name=\"ExcelPort[Export][Language]\"]:checked').val(),
          ProductExportMode : jq('input[name=\"ExcelPort[Export][ProductExportMode]\"]:checked').val(),
          Filter : jq('input[name=\"ExcelPort[Export][Filter]\"]').val(),
          Filters : {}
        },
        LastImport : jq('#last_import_input').val(),
        Settings : {
          ExportLimit : jq('input[name=\"ExcelPort[Settings][ExportLimit]\"]').val(),
          DescriptionEncoding : jq('select[name=\"ExcelPort[Settings][DescriptionEncoding]\"]').val()
        }
      }
    };
    
    jq('*[name^=\"ExcelPort[Export][Filters]\"]').each(function (index, element) {
      var regex = /ExcelPort\\[Export\\]\\[Filters\\]\\[(.*?)\\]\\[(.*?)\\]\\[(.*?)\\]/gi;
      match = regex.exec(jq(element).attr('name'));
      if (match == null) {
        regex = /ExcelPort\\[Export\\]\\[Filters\\]\\[(.*?)\\]\\[(.*?)\\]/gi;
        match = regex.exec(jq(element).attr('name'));
        if (typeof(exportData.ExcelPort.Export.Filters[match[1]]) == 'undefined') exportData.ExcelPort.Export.Filters[match[1]] = {};
        exportData.ExcelPort.Export.Filters[match[1]][match[2]] = jq(element).val();
      } else {
        if (typeof(exportData.ExcelPort.Export.Filters[match[1]]) == 'undefined') exportData.ExcelPort.Export.Filters[match[1]] = {};
        if (typeof(exportData.ExcelPort.Export.Filters[match[1]][match[2]]) == 'undefined') exportData.ExcelPort.Export.Filters[match[1]][match[2]] = {};
        exportData.ExcelPort.Export.Filters[match[1]][match[2]][match[3]] = jq(element).val();
      }
    });

    updateProgressBar(site_url, true, function() {
      startAjaxGenerate('index.php?user_token='+token+'&route=extension/module/excelport/ajaxgenerate&_=' + (new Date()).getTime(), exportData);
    });
  }
  
  if (ajaximport) {
    jq('#generateLoading').show();
    updateProgressBar(site_url);
    startAjaxImport('index.php?user_token='+token+'&route=extension/module/excelport/ajaximport&_=' + Date.now(), {
      ExcelPort : {
        Import : {
          DataType : jq('input[name=\"ExcelPort[Import][DataType]\"]:checked').val(),
          Language : jq('input[name=\"ExcelPort[Import][Language]\"]:checked').val(),
          Delete : jq('input[name=\"ExcelPort[Import][Delete]\"]:checked').val(),
          AddAsNew : jq('input[name=\"ExcelPort[Import][AddAsNew]\"]:checked').val()
        },
        LastImport : jq('#last_import_input').val(),
        Settings : {
          ImportLimit : jq('input[name=\"ExcelPort[Settings][ImportLimit]\"]').val()
        }
      }
    });
  }

  jq('.needMoreSize').click(function() {
    window.open(site_url + '/view/javascript/excelport/help_increase_size.php', '_blank', 'location=no,width=830,height=580,resizable=no');
  });

  jq('.ExcelPortSubmitButton').click(function(e) {
    abort = false;
    var action = jq(this).attr('data-action');
    if (action == 'import' && jq('#checkboxDelete').is(':checked')) {
      if (!confirm('";
        // line 715
        echo ($context["text_confirm_delete_other"] ?? null);
        echo "')) return;  
    }
    jq('#form').attr('action',jq('#form').attr('action').replace(/&submitAction=.*(\$|&)/g, ''));
    if (action != undefined && action != '') {
      jq('#form').attr('action',jq('#form').attr('action')+'&submitAction='+action);
    }
    jq('#form').submit();
  });

  jq('#filter_popover').popover({ trigger: 'hover' }).click(function() {
    jq(this).toggleClass('active');
    if (jq(this).hasClass('active')) {
      jq('#toggle_filter').val('1');
    } else {
      jq('#toggle_filter').val('0');
    }
    triggerFiltersDisplay();
  });
  
  jq('input[name=\"ExcelPort[Export][DataType]\"]').change(triggerFiltersDisplay);
  triggerFiltersDisplay();
  
  jq('.addCondition').click(function() {
    addCondition(jq(this).closest('.dataTypeFilter').attr('data-type'));
  });
  
  jq('a[data-toggle=\"tooltip\"]').tooltip({placement:'right'});

});

</script>

";
        // line 747
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/module/excelport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  1003 => 747,  968 => 715,  863 => 613,  859 => 612,  610 => 368,  585 => 346,  579 => 343,  564 => 331,  550 => 320,  538 => 311,  485 => 261,  481 => 260,  474 => 256,  470 => 255,  463 => 251,  459 => 250,  452 => 246,  448 => 245,  441 => 241,  437 => 240,  430 => 236,  426 => 235,  419 => 231,  415 => 230,  408 => 226,  404 => 225,  397 => 221,  393 => 220,  386 => 216,  382 => 215,  375 => 211,  371 => 210,  364 => 206,  360 => 205,  353 => 201,  349 => 200,  342 => 196,  338 => 195,  331 => 191,  327 => 190,  315 => 181,  311 => 180,  307 => 179,  298 => 173,  294 => 172,  261 => 142,  255 => 139,  161 => 47,  152 => 44,  147 => 43,  143 => 42,  138 => 39,  127 => 37,  123 => 36,  118 => 33,  110 => 29,  107 => 28,  99 => 24,  96 => 23,  88 => 19,  86 => 18,  80 => 14,  69 => 12,  65 => 11,  60 => 9,  54 => 8,  50 => 7,  43 => 3,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/excelport.twig", "");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/productreview.twig */
class __TwigTemplate_2169c4ae82bd70396b4c7541953c1bd643fb1068de769ad5cb672f0a3e8a657b extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div>
  <div class=\"row\">
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\">";
        // line 6
        echo ($context["entry_approved"] ?? null);
        echo "</label>
\t\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  ";
        // line 9
        if (($context["filter_status"] ?? null)) {
            // line 10
            echo "\t\t  <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t\t  ";
        } else {
            // line 12
            echo "\t\t  <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t\t ";
        }
        // line 14
        echo "\t\t  ";
        if (( !($context["filter_status"] ?? null) &&  !(null === ($context["filter_status"] ?? null)))) {
            // line 15
            echo "\t\t  <option value=\"0\" selected=\"selected\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t\t  ";
        } else {
            // line 17
            echo "\t\t  <option value=\"0\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t\t ";
        }
        // line 19
        echo "\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Limit (Note:Export Data limit)</label>
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_start\" value=\"0\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\"/> -
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_limit\" value=\"";
        // line 26
        echo ($context["filter_limit"] ?? null);
        echo "\" placeholder=\"Limit\" id=\"input-limit\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label> </label>
\t\t<button type=\"button\" id=\"buttonproductreviews\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> Export </button>
\t  </div>
\t</div>
  </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/productreview.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  88 => 26,  79 => 19,  73 => 17,  67 => 15,  64 => 14,  58 => 12,  52 => 10,  50 => 9,  44 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/productreview.twig", "/home/<USER>/public_html/admin/view/template/extension/export/productreview.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* common/footer.twig */
class __TwigTemplate_e1af92872e89a2eb9b798ce231b3938d7807185c19c0dffa711c07cd2d9b8fe2 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "
";
        // line 2
        if ((($context["pim_status"] ?? null) == true)) {
            // line 3
            echo "
<script type=\"text/javascript\"><!--
\$(document).ready(function() {
\t
  \$(document).undelegate('a[data-toggle=\\'image\\']', 'click');
  
  \$(document).delegate('a[data-toggle=\\'image\\']', 'click', function(e) {
    e.preventDefault();    
    var element = this;
    \$(element).popover({
      html: true,
      placement: 'right',
      trigger: 'manual',
      content: function() {
        return '<button type=\"button\" id=\"button-image\" class=\"btn btn-primary\"><i class=\"fa fa-pencil\"></i></button> <button type=\"button\" id=\"button-clear\" class=\"btn btn-danger\"><i class=\"fa fa-trash-o\"></i></button>';
      }
    });
    location.hash = 'pim';
    \$(element).popover('toggle');

    \$('#button-image').on('click', function() {
      \$(element).popover('hide');
      var target = \$(element).parent().find('input').attr('id');
      var thumb = \$(element).attr('id');
      var fm = \$('<div/>').dialogelfinder({
        url : 'index.php?route=common/filemanager/connector&user_token='+getURLVar('user_token')+'&pim_available=<?php echo \$pim_available; ?>',
        lang : '";
            // line 29
            echo ($context["lang"] ?? null);
            echo "',
        width : ";
            // line 30
            echo ($context["width"] ?? null);
            echo ",
        height: ";
            // line 31
            echo ($context["height"] ?? null);
            echo ",
        destroyOnClose : true,
        
        uiOptions : {toolbar : [['home', 'back', 'forward'],['reload'],['mkdir', 'upload'],['open', 'download', 'getfile'],['info'],['quicklook'],['copy', 'cut', 'paste'],['rm'],['duplicate', 'rename', 'edit', 'resize'],['extract', 'archive','multiupload'],['search'],['view'],['help']]},
  
        contextmenu: {navbar: [\"open\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"info\"],cwd: [\"reload\", \"back\", \"|\", \"upload\", \"mkdir\", \"mkfile\", \"paste\", \"|\", \"sort\", \"|\", \"info\"],files: [\"getfile\", \"|\", \"open\", \"quicklook\", \"|\", \"download\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"edit\", \"rename\", \"resize\", \"|\", \"archive\",\"multiupload\", \"extract\", \"|\", \"info\"]},
        
        getFileCallback : function(files, fm) {
          a = files.url;

\t\t\t\t\tb = a.replace('";
            // line 41
            echo ($context["image_url"] ?? null);
            echo "','');\t
\t\t\t\t\tb = b.replace('";
            // line 42
            echo ($context["image_url2"] ?? null);
            echo "','');\t
          
          
          \$('#'+thumb).find('img').attr('src', files.tmb);
          \$('#'+target).val(decodeURIComponent(b));
          \$('#radio-'+target).removeAttr('disabled');
          \$('#radio-'+target).val(b);
        },
        commandsOptions : {
          getfile : {
            oncomplete : 'close',
          }
        }
      }).dialogelfinder('instance');
      return;
    });

    \$('#button-clear').on('click', function() {
      \$(element).find('img').attr('src', \$(element).find('img').attr('data-placeholder'));
      \$(element).parent().find('input').attr('value', '');
      \$(element).popover('hide');
    });
  });

  \$(document).delegate('a[data-toggle=\\'manager\\']', 'click', function(e) {
    e.preventDefault();
    var fm = \$('<div/>').dialogelfinder({
      url : 'index.php?route=common/filemanager/connector&user_token='+getURLVar('user_token')+'&pim_available=<?php echo \$pim_available; ?>',
      lang : '";
            // line 70
            echo ($context["lang"] ?? null);
            echo "',
      width : ";
            // line 71
            echo ($context["width"] ?? null);
            echo ",
      height: ";
            // line 72
            echo ($context["height"] ?? null);
            echo ",
      destroyOnClose : true,
      
      uiOptions : {toolbar : [['home', 'back', 'forward'],['reload'],['mkdir', 'upload'],['open', 'download', 'getfile'],['info'],['quicklook'],['copy', 'cut', 'paste'],['rm'],['duplicate', 'rename', 'edit', 'resize'],['extract', 'archive','multiupload', 'sort'],['search'],['view'],['help']]},

      contextmenu: {navbar: [\"open\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"info\"],cwd: [\"reload\", \"back\", \"|\", \"upload\", \"mkdir\", \"mkfile\", \"paste\", \"|\", \"sort\", \"|\", \"info\"],files: [\"getfile\", \"|\", \"open\", \"quicklook\", \"|\", \"download\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"edit\", \"rename\", \"resize\", \"|\", \"archive\",\"multiupload\", \"extract\", \"|\", \"info\"]},
      
      getFileCallback : function(files, fm) {
        a = files.url;
\t\t\t\t\tb = a.replace('";
            // line 81
            echo ($context["image_url"] ?? null);
            echo "','');\t
\t\t\t\t\tb = b.replace('";
            // line 82
            echo ($context["image_url2"] ?? null);
            echo "','');\t
        addMultiImage(decodeURIComponent(b));
      },
      commandsOptions : {
        getfile : {
          oncomplete : 'close',
          folders : false
        }
      }
    }).dialogelfinder('instance');
  });

 \$(document).undelegate('button[data-toggle=\\'image\\']', 'click');
 
    \$(document).delegate('button[data-toggle=\\'image\\']', 'click', function(e) {
\t\t\te.preventDefault();
      location.hash = '';
      var fm = \$('<div/>').dialogelfinder({
        url : 'index.php?route=common/filemanager/connector&user_token=' + getURLVar('user_token')+'&pim_available=<?php echo \$pim_available; ?>',
        lang : '";
            // line 101
            echo ($context["lang"] ?? null);
            echo "',
        width : ";
            // line 102
            echo ($context["width"] ?? null);
            echo ",
        height: ";
            // line 103
            echo ($context["height"] ?? null);
            echo ",
        destroyOnClose : true,
        getFileCallback : function(files, fm) {
          var range, sel = window.getSelection();  
          if (sel.rangeCount) {
            var img = document.createElement('img');
            a = files.url;
            b = a.replace(files.baseUrl,'');
            img.src = files.baseUrl+''+b;
            range = sel.getRangeAt(0);
            range.insertNode(img);
          }
        },
        commandsOptions : {
          getfile : {
            oncomplete : 'close',
            folders : false
          }
        }
      }).dialogelfinder('instance');
    });
\t\t
\t\$(document).ready(function() {
\t\t// Override summernotes image manager
\t\t\$('[data-toggle=\\'summernote\\']').each(function() {
\t\t\tvar element = this;
\t\t\t
\t\t\tif (\$(this).attr('data-lang')) {
\t\t\t\t\$('head').append('<script type=\"text/javascript\" src=\"view/javascript/summernote/lang/summernote-' + \$(this).attr('data-lang') + '.js\"></script>');
\t\t\t}

\t\t\t\$(element).summernote({
\t\t\t\tlang: \$(this).attr('data-lang'),
\t\t\t\tdisableDragAndDrop: true,
\t\t\t\theight: 300,
\t\t\t\temptyPara: '',
\t\t\t\tcodemirror: { // codemirror options
\t\t\t\t\tmode: 'text/html',
\t\t\t\t\thtmlMode: true,
\t\t\t\t\tlineNumbers: true,
\t\t\t\t\ttheme: 'monokai'
\t\t\t\t},\t\t\t
\t\t\t\tfontsize: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '30', '36', '48' , '64'],
\t\t\t\ttoolbar: [
\t\t\t\t\t['style', ['style']],
\t\t\t\t\t['font', ['bold', 'underline', 'clear']],
\t\t\t\t\t['fontname', ['fontname']],
\t\t\t\t\t['fontsize', ['fontsize']],
\t\t\t\t\t['color', ['color']],
\t\t\t\t\t['para', ['ul', 'ol', 'paragraph']],
\t\t\t\t\t['table', ['table']],
\t\t\t\t\t['insert', ['link', 'image', 'video']],
\t\t\t\t\t['view', ['fullscreen', 'codeview', 'help']]
\t\t\t\t],
\t\t\t\tpopover: {
\t\t\t\t\t\t\t\timage: [
\t\t\t\t\t\t['custom', ['imageAttributes']],
\t\t\t\t\t\t['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],
\t\t\t\t\t\t['float', ['floatLeft', 'floatRight', 'floatNone']],
\t\t\t\t\t\t['remove', ['removeMedia']]
\t\t\t\t\t],
\t\t\t\t},\t\t\t
\t\t\t\tbuttons: {
\t\t\t\t\t\timage: function() {
\t\t\t\t\t\tvar ui = \$.summernote.ui;
\t\t\t\t\t\t\t\t
\t\t\t\t\t\t// create button
\t\t\t\t\t\tvar button = ui.button({
\t\t\t\t\t\t\tcontents: '<i class=\"note-icon-picture\" />',
\t\t\t\t\t\t\ttooltip: \$.summernote.lang[\$.summernote.options.lang].image.image,
\t\t\t\t\t\t\tclick: function () {
\t\t\t\t\t\t\t\t\$('#modal-image').remove();
\t\t\t\t\t\t\t\tvar fm = \$('<div/>').dialogelfinder({
\t\t\t\t\t\t\t\t\turl : 'index.php?route=common/filemanager/connector&user_token=' + getURLVar('user_token')+'&pim_available=<?php echo \$pim_available; ?>',
\t\t\t\t\t\t\t\t\tlang : '";
            // line 177
            echo ($context["lang"] ?? null);
            echo "',
\t\t\t\t\t\t\t\t\twidth : ";
            // line 178
            echo ($context["width"] ?? null);
            echo ",
\t\t\t\t\t\t\t\t\theight: ";
            // line 179
            echo ($context["height"] ?? null);
            echo ",
\t\t\t\t\t\t\t\t\tdestroyOnClose : true,
\t\t\t\t\t\t\t\t\tgetFileCallback : function(files, fm) {
\t\t\t\t\t\t\t\t\t\t\tvar img = document.createElement('img');
\t\t\t\t\t\t\t\t\t\t\ta = files.url;
\t\t\t\t\t\t\t\t\t\t\t
\t\t\t\t\t\t\t\t\t\t\t\$(element).summernote('insertImage', a);
\t\t\t\t\t\t\t\t\t},
\t\t\t\t\t\t\t\t\tcommandsOptions : {
\t\t\t\t\t\t\t\t\t\tgetfile : {
\t\t\t\t\t\t\t\t\t\t\toncomplete : 'close',
\t\t\t\t\t\t\t\t\t\t\tfolders : false
\t\t\t\t\t\t\t\t\t\t}
\t\t\t\t\t\t\t\t\t}
\t\t\t\t\t\t\t\t}).dialogelfinder('instance');\t\t\t
\t\t\t\t\t\t\t}
\t\t\t\t\t\t});
\t\t\t\t\t
\t\t\t\t\t\treturn button.render();
\t\t\t\t\t}
\t\t\t\t\t}
\t\t\t});
\t\t});
\t});\t
\t\t
});
//--></script> \t\t\t\t\t

\t\t";
        }
        // line 208
        echo "
        
<footer id=\"footer\">";
        // line 210
        echo ($context["text_footer"] ?? null);
        echo "<br/>";
        echo ($context["text_version"] ?? null);
        echo "</footer></div>
</body></html>
";
    }

    public function getTemplateName()
    {
        return "common/footer.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  301 => 210,  297 => 208,  265 => 179,  261 => 178,  257 => 177,  180 => 103,  176 => 102,  172 => 101,  150 => 82,  146 => 81,  134 => 72,  130 => 71,  126 => 70,  95 => 42,  91 => 41,  78 => 31,  74 => 30,  70 => 29,  42 => 3,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "common/footer.twig", "");
    }
}

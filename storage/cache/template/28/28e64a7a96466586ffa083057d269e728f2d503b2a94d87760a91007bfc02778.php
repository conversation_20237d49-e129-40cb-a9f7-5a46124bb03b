<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/manufacturersimport.twig */
class __TwigTemplate_0f2037301f6d9498c9b68fed0abebd0cc8c4b57bb0806121ee9a42a076caeba3 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["manufactureaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"formimportmanufacturer\" class=\"form-horizontal\">
\t
\t<div class=\"row\">
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 9
        echo ($context["entry_manufacturerimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label>";
        // line 12
        echo ($context["entry_store"] ?? null);
        echo "</label>
\t\t\t<select class=\"form-control\" name=\"store_id\">
\t\t\t\t<option value=\"0\">";
        // line 14
        echo ($context["text_default"] ?? null);
        echo "</option>
\t\t\t\t\t";
        // line 15
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 16
            echo "\t\t\t\t<option value=\"";
            echo (($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 = $context["store"]) && is_array($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4) || $__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 instanceof ArrayAccess ? ($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4["store_id"] ?? null) : null);
            echo "\">";
            echo (($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 = $context["store"]) && is_array($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144) || $__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 instanceof ArrayAccess ? ($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144["name"] ?? null) : null);
            echo "</option>
\t\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 18
        echo "\t\t\t</select>
\t\t\t<i>Import your Manufactures according to Store.</i>
\t\t</div>
\t\t<!-- Manufacture File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t
\t\t\t<label>";
        // line 24
        echo ($context["entry_language"] ?? null);
        echo "</label>\t\t
\t\t\t<select class=\"form-control\" name=\"language_id\">
\t\t\t";
        // line 26
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 27
            echo "\t\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 27);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 27);
            echo "</option>
\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        echo "\t\t\t</select>
\t\t\t<i>Import your Manufacture's Seo according to Language.</i>
\t\t
\t\t</div>
\t\t
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import Button -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#formimportmanufacturer').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t
\t\t
\t</div>

</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/manufacturersimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  108 => 29,  97 => 27,  93 => 26,  88 => 24,  80 => 18,  69 => 16,  65 => 15,  61 => 14,  56 => 12,  50 => 9,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/manufacturersimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/manufacturersimport.twig");
    }
}

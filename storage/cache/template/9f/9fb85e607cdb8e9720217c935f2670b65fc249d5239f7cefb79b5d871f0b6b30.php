<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/customerexport.twig */
class __TwigTemplate_8be593be96c86baaad44698aea11960e226deba8b5a85afb6a8b2157f65807bd extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr/>
<div class=\"row\">
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">Customer Name</label>
\t\t<input type=\"text\" name=\"customer_name\" value=\"\" placeholder=\"Customer Name\" id=\"input-name\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-customer-group\">";
        // line 9
        echo ($context["entry_customer_group"] ?? null);
        echo "</label>
\t\t<select name=\"filter_customer_group_id\" id=\"input-customer-group\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  ";
        // line 12
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["customer_groups"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["customer_group"]) {
            // line 13
            echo "\t\t  <option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["customer_group"], "customer_group_id", [], "any", false, false, false, 13);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["customer_group"], "name", [], "any", false, false, false, 13);
            echo "</option>
\t\t\t  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['customer_group'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 15
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-ip\">";
        // line 18
        echo ($context["entry_ip"] ?? null);
        echo "</label>
\t\t<input type=\"text\" name=\"filter_ip\" value=\"\" placeholder=\"";
        // line 19
        echo ($context["entry_ip"] ?? null);
        echo "\" id=\"input-ip\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-eformat\">";
        // line 22
        echo ($context["export_format"] ?? null);
        echo "</label>
\t\t<select name=\"filter_eformat\" id=\"input-eformat\" class=\"form-control\">
\t\t\t<option value=\"xls\">XLS</option>
\t\t\t<option value=\"xlsx\">XLSX</option>
\t\t\t<option value=\"xml\">XML</option>
\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-email\">";
        // line 32
        echo ($context["entry_email"] ?? null);
        echo "</label>
\t\t<input type=\"text\" name=\"filter_email\" value=\"\" placeholder=\"";
        // line 33
        echo ($context["entry_email"] ?? null);
        echo "\" id=\"input-email\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\">";
        // line 36
        echo ($context["entry_status"] ?? null);
        echo "</label>
\t\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  <option value=\"1\">";
        // line 39
        echo ($context["text_enabled"] ?? null);
        echo "</option>
\t\t  <option value=\"0\">";
        // line 40
        echo ($context["text_disabled"] ?? null);
        echo "</option>
\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-date-added\">To Date</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_to_date\" value=\"\" placeholder=\"";
        // line 46
        echo ($context["entry_date_added"] ?? null);
        echo "\" data-date-format=\"YYYY-MM-DD\" id=\"input-date-added\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t\t<button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span>
\t\t</div>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\"></label>
\t\t<button type=\"button\" id=\"button-customer_export\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 54
        echo ($context["button_export"] ?? null);
        echo "</button>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t\t<div class=\"form-group\">
\t\t\t<label style=\"width:100%;\" class=\"control-label\" for=\"input-limit\">Customer ID - Condition </label>
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_idstart\" value=\"";
        // line 60
        echo ($context["minicustomer_id"] ?? null);
        echo "\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\" />
\t\t\t-
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_idend\" value=\"";
        // line 62
        echo ($context["maxcustomer_id"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_limit"] ?? null);
        echo "\" id=\"input-idend\" class=\"form-control\" />
\t   </div> 
\t   <div class=\"form-group\">
\t\t <label class=\"control-label\" for=\"input-approved\">";
        // line 65
        echo ($context["entry_approved"] ?? null);
        echo "</label>
\t\t<select name=\"filter_approved\" id=\"input-approved\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  <option value=\"1\">";
        // line 68
        echo ($context["text_yes"] ?? null);
        echo "</option>
\t\t  <option value=\"0\">";
        // line 69
        echo ($context["text_no"] ?? null);
        echo "</option>
\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-date-added\">From Date</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_from_date\" value=\"\" placeholder=\"";
        // line 75
        echo ($context["entry_date_added"] ?? null);
        echo "\" data-date-format=\"YYYY-MM-DD\" id=\"input-date-added\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t  <button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span></div>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Export in batches</label>
\t\t<input type=\"text\" name=\"filter_limit\" value=\"\" placeholder=\"export data in 2 or more files\" id=\"input-limit\" class=\"form-control\" />
\t\t<i><b>Note:</b> If you have large data then use this feature and export data in batches.</i>
\t </div>
\t</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/customerexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  177 => 75,  168 => 69,  164 => 68,  158 => 65,  150 => 62,  145 => 60,  136 => 54,  125 => 46,  116 => 40,  112 => 39,  106 => 36,  100 => 33,  96 => 32,  83 => 22,  77 => 19,  73 => 18,  68 => 15,  57 => 13,  53 => 12,  47 => 9,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/customerexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/customerexport.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/affilatesimport.twig */
class __TwigTemplate_668a95e2b7377ac133f584b5b04c45f59df1db30e31a5286cb16197b10af56ac extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["affiliatesaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"affiliatesimportform\" class=\"form-horizontal\">

\t<div class=\"row\">
\t
\t\t<!-- Affilates File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\" />
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["text_import_affiliates"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Exiting Email and password -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label class=\"control-label\" for=\"input-password\"><span data-toggle=\"tooltip\" title=\"";
        // line 17
        echo ($context["help_password"] ?? null);
        echo "\">";
        echo ($context["text_password"] ?? null);
        echo "</span></label>
\t\t\t<table class=\"table table-responsive\">
\t\t\t\t<tr>
\t\t\t\t\t<td><input type=\"radio\" name=\"password_format\" value=\"P\"/> Plan Password</td>
\t\t\t\t\t<td><input type=\"radio\" checked=\"checked\" name=\"password_format\" value=\"E\"/> Encript Password</td>
\t\t\t\t</tr>
\t\t\t</table>
\t\t\t<b>Note:</b> ";
        // line 24
        echo ($context["help_password"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import Button -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#affiliatesimportform').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t
\t</div>
\t
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/affilatesimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  73 => 24,  61 => 17,  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/affilatesimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/affilatesimport.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/excelport/tab_settings.twig */
class __TwigTemplate_e8cb4899bde1e2573a0254f18781fc9943de3244fe4c1b212d65fa4a5d78e633 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<div class=\"form-horizontal\">
    <div class=\"form-group\">
        <label class=\"control-label col-sm-2\">
            ";
        // line 4
        echo ($context["text_export_product_description_html"] ?? null);
        echo "
        </label>
        <div class=\"col-sm-10\">
            <select name=\"ExcelPort[Settings][DescriptionEncoding]\" class=\"form-control\">
                <option value=\"encoded_html\" ";
        // line 8
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 8), "Settings", [], "any", false, false, false, 8), "DescriptionEncoding", [], "any", false, false, false, 8) == "encoded_html")) ? ("selected") : (""));
        echo ">";
        echo ($context["option_encoded_html"] ?? null);
        echo "</option>
                <option value=\"standard_html\" ";
        // line 9
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 9), "Settings", [], "any", false, false, false, 9), "DescriptionEncoding", [], "any", false, false, false, 9) == "standard_html")) ? ("selected") : (""));
        echo ">";
        echo ($context["option_standard_html"] ?? null);
        echo "</option>
                <option value=\"no_html\" ";
        // line 10
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 10), "Settings", [], "any", false, false, false, 10), "DescriptionEncoding", [], "any", false, false, false, 10) == "no_html")) ? ("selected") : (""));
        echo ">";
        echo ($context["option_no_html"] ?? null);
        echo "</option>
            </select>
        </div> 
    </div>
    <div class=\"form-group\">
        <label class=\"control-label col-sm-2\">
            ";
        // line 16
        echo ($context["text_export_entries_number"] ?? null);
        echo "
        </label>
        <div class=\"col-sm-10\">
            <input type=\"number\" min=\"50\" max=\"800\" name=\"ExcelPort[Settings][ExportLimit]\" value=\"";
        // line 19
        echo ((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 19), "Settings", [], "any", false, false, false, 19), "ExportLimit", [], "any", false, false, false, 19)) ? (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 19), "Settings", [], "any", false, false, false, 19), "ExportLimit", [], "any", false, false, false, 19)) : ("500"));
        echo "\" class=\"form-control\" />
        </div> 
    </div>
    <div class=\"form-group\">
        <label class=\"control-label col-sm-2\">
        ";
        // line 24
        echo ($context["text_import_limit"] ?? null);
        echo "
        </label>
        <div class=\"col-sm-10\">
            <input type=\"number\" min=\"10\" max=\"800\" name=\"ExcelPort[Settings][ImportLimit]\" value=\"";
        // line 27
        echo ((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 27), "Settings", [], "any", false, false, false, 27), "ImportLimit", [], "any", false, false, false, 27)) ? (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 27), "Settings", [], "any", false, false, false, 27), "ImportLimit", [], "any", false, false, false, 27)) : ("100"));
        echo "\" class=\"form-control\" />
        </div> 
    </div>
    <div class=\"form-group\">
        <label class=\"control-label col-sm-2\">
            ";
        // line 32
        echo ($context["text_export_non_store_products"] ?? null);
        echo "
        </label>
        <div class=\"col-sm-10\">
            <select name=\"ExcelPort[Settings][ExportNonStoreProducts]\" class=\"form-control\">
                <option value=\"0\" ";
        // line 36
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 36), "Settings", [], "any", false, false, false, 36), "ExportNonStoreProducts", [], "any", false, false, false, 36) == "0")) ? ("selected") : (""));
        echo ">";
        echo ($context["text_no"] ?? null);
        echo "</option>
                <option value=\"1\" ";
        // line 37
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 37), "Settings", [], "any", false, false, false, 37), "ExportNonStoreProducts", [], "any", false, false, false, 37) == "1")) ? ("selected") : (""));
        echo ">";
        echo ($context["text_yes"] ?? null);
        echo "</option>
            </select>
        </div> 
    </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/module/excelport/tab_settings.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  113 => 37,  107 => 36,  100 => 32,  92 => 27,  86 => 24,  78 => 19,  72 => 16,  61 => 10,  55 => 9,  49 => 8,  42 => 4,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/excelport/tab_settings.twig", "");
    }
}

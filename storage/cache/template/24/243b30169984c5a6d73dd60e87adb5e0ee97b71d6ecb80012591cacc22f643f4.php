<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/manufactureexport.twig */
class __TwigTemplate_a837424b7339c33dc572fa49a49ffd26f5353f28ada6c5870b9df8aacc1a559d extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div class=\"row\">
<div class=\"col-sm-6\">
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-name\">";
        // line 5
        echo ($context["entry_store"] ?? null);
        echo "</label>
\t<select class=\"form-control\" name=\"filter_store\">
\t <option value=\"0\">";
        // line 7
        echo ($context["text_default"] ?? null);
        echo "</option>
\t  ";
        // line 8
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 9
            echo "\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 9);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "name", [], "any", false, false, false, 9);
            echo "</option>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 11
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Export in batches</label>
\t<input type=\"text\" name=\"filter_limit\" value=\"\" placeholder=\"export data in 2 or more files\" id=\"input-limit\" class=\"form-control\" />
\t<i><b>Note:</b> If you have large data then use this feature and export data in batches.</i>
  </div>
</div>
<div class=\"col-sm-6\">
 <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-status\">";
        // line 21
        echo ($context["entry_status"] ?? null);
        echo "</label>
\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t  <option value=\"*\"></option>
\t  ";
        // line 24
        if (($context["filter_status"] ?? null)) {
            // line 25
            echo "\t  <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t  ";
        } else {
            // line 27
            echo "\t  <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t ";
        }
        // line 29
        echo "\t  ";
        if (( !($context["filter_status"] ?? null) &&  !(null === ($context["filter_status"] ?? null)))) {
            // line 30
            echo "\t  <option value=\"0\" selected=\"selected\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t  ";
        } else {
            // line 32
            echo "\t  <option value=\"0\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t ";
        }
        // line 34
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-language\">";
        // line 37
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t\t<select class=\"form-control\" name=\"filter_language_id\">
\t\t ";
        // line 39
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 40
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 40);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 40);
            echo "</option>
\t\t ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 42
        echo "\t\t</select>
\t  </div>
</div>
<div class=\"col-sm-6\">
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-status\"></label>
\t<button type=\"button\" id=\"button_filter_manufacture\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 48
        echo ($context["button_export"] ?? null);
        echo "</button>
  </div>
</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/manufactureexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  147 => 48,  139 => 42,  128 => 40,  124 => 39,  119 => 37,  114 => 34,  108 => 32,  102 => 30,  99 => 29,  93 => 27,  87 => 25,  85 => 24,  79 => 21,  67 => 11,  56 => 9,  52 => 8,  48 => 7,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/manufactureexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/manufactureexport.twig");
    }
}

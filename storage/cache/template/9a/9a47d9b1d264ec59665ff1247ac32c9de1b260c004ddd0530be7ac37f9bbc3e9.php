<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/couponimport.twig */
class __TwigTemplate_2ca3ef7ee96ad3b610cb38607cd6efe772733c1d0c0a0f7b86ba7f5abc501c73 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["counponaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form_couponimport\" class=\"form-horizontal\">

\t<div class=\"row\">
\t\t
\t\t<!-- Coupon Group File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t ";
        // line 11
        echo ($context["entry_couponimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import Button -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#form_couponimport').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t
\t\t
\t</div>

</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/couponimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/couponimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/couponimport.twig");
    }
}

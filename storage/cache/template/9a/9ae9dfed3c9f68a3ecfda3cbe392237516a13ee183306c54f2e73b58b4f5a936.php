<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/excel_import.twig */
class __TwigTemplate_ebad414f3e5f1949b65c90e62ee2f1fe8629c993d689c36670c0b5681a6223b6 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <h1>Excel Import Point</h1>
      <ul class=\"breadcrumb\">
\t  ";
        // line 7
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 8
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 8);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 8);
            echo "</a></li>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 10
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
  ";
        // line 14
        if (($context["error_warning"] ?? null)) {
            // line 15
            echo "    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 19
        echo "\t";
        if (($context["success"] ?? null)) {
            // line 20
            echo "    <div class=\"alert alert-success\"><i class=\"fa fa-check-circle\"></i> ";
            echo ($context["success"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 24
        echo "    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i>Excel Import Point</h3>
      </div>
      <div class=\"panel-body\">
\t\t
\t\t<style>
\t\t.nav-tabs{
\t\t\tmargin-bottom:0px;
\t\t}
\t\t.tab-content{
\t\t\tmargin-top: 0px;
\t\t\tborder-left: 1px solid #ddd;
\t\t\tborder-right: 1px solid #ddd;
\t\t\tborder-bottom: 1px solid #ddd;
\t\t\tpadding:20px;
\t\t}
\t\t.btn-success a{
\t\t\tcolor:#fff;
\t\t\tfont-size:14px;
\t\t\ttext-transform:uppercase;
\t\t}
\t\t.tbpadding{
\t\t\tpadding:10px;
\t\t}
\t\t.ourbtn{
\t\t\tbackground-color:#921e6f;
\t\t\tborder:none;
\t\t\ttext-transform:uppercase;
\t\t}
\t\t.ourbtn:hover{
\t\t\tbackground-color:#921e6f;
\t\t\tborder:none;
\t\t}
\t\t.ourbtn i{
\t\t\tmargin-right:5px;
\t\t}
\t\t</style>
\t\t
\t\t<div class=\"tab-content\">
            <div class=\"tab-pane active\" id=\"tab-import\">
\t\t\t\t<div class=\"row\">
\t\t\t\t\t<div class=\"col-sm-12\">
\t\t\t\t\t  <ul class=\"nav nav-tabs\">
\t\t\t\t\t\t<li class=\"active\"><a href=\"#productimport\" data-toggle=\"tab\">Products</a></li>
\t\t\t\t\t\t<li><a href=\"#categoriesimport\" data-toggle=\"tab\">Categories</a></li>
\t\t\t\t\t\t<li><a href=\"#manufacturersimport\" data-toggle=\"tab\">Manufacturers</a></li>
\t\t\t\t\t\t<li><a href=\"#options\" data-toggle=\"tab\">Options</a></li>
\t\t\t\t\t\t<li><a href=\"#customerimport\" data-toggle=\"tab\">Customer</a></li>
\t\t\t\t\t\t<li><a href=\"#customergroupimport\" data-toggle=\"tab\">Customer Group</a></li>
\t\t\t\t\t\t<li><a href=\"#orderimport\" data-toggle=\"tab\">Order Import</a></li>
\t\t\t\t\t\t<li><a href=\"#affiliatesimport\" data-toggle=\"tab\">Affiliates</a></li>
\t\t\t\t\t\t<li class=\"dropdown\" role=\"presentation\">
\t\t\t\t\t\t\t<a aria-expanded=\"false\" aria-haspopup=\"true\" role=\"button\" href=\"#\" data-toggle=\"dropdown\" class=\"dropdown-toggle\">
\t\t\t\t\t\t\t  More <span class=\"caret\"></span>
\t\t\t\t\t\t\t</a>
\t\t\t\t\t\t\t<ul class=\"dropdown-menu\">
\t\t\t\t\t\t\t\t<li><a href=\"#couponimport\" data-toggle=\"tab\">Coupons</a></li>
\t\t\t\t\t\t\t\t<li><a href=\"#userimport\" data-toggle=\"tab\">Users</a></li>
\t\t\t\t\t\t\t\t<li><a href=\"#productreviewimport\" data-toggle=\"tab\">Product Review</a></li>
\t\t\t\t\t\t\t</ul>
\t\t\t\t\t\t</li>
\t\t\t\t\t\t
\t\t\t\t\t\t
\t\t\t\t\t\t
\t\t\t\t\t  </ul>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"col-sm-12\">
\t\t\t\t\t  <div class=\"tab-content\">
\t\t\t\t\t\t <div class=\"tab-pane active\" id=\"productimport\">
\t\t\t\t\t\t\t<h3>Product Import</h3>
\t\t\t\t\t\t\t";
        // line 95
        $this->loadTemplate("extension/import/productimport.twig", "extension/excel_import.twig", 95)->display($context);
        // line 96
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"categoriesimport\">
\t\t\t\t\t\t\t<h3>Category Import</h3>
\t\t\t\t\t\t\t";
        // line 99
        $this->loadTemplate("extension/import/categoriesimport.twig", "extension/excel_import.twig", 99)->display($context);
        // line 100
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"manufacturersimport\">
\t\t\t\t\t\t\t<h3>Manufacturer Import</h3>
\t\t\t\t\t\t\t";
        // line 103
        $this->loadTemplate("extension/import/manufacturersimport.twig", "extension/excel_import.twig", 103)->display($context);
        // line 104
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"options\">
\t\t\t\t\t\t\t<h3>Options</h3>
\t\t\t\t\t\t\t";
        // line 107
        $this->loadTemplate("extension/import/options.twig", "extension/excel_import.twig", 107)->display($context);
        // line 108
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"customerimport\">
\t\t\t\t\t\t\t<h3>Customer Import</h3>
\t\t\t\t\t\t\t";
        // line 111
        $this->loadTemplate("extension/import/customerimport.twig", "extension/excel_import.twig", 111)->display($context);
        // line 112
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"customergroupimport\">
\t\t\t\t\t\t\t<h3>Customer Group Import</h3>
\t\t\t\t\t\t\t";
        // line 115
        $this->loadTemplate("extension/import/customergroupimport.twig", "extension/excel_import.twig", 115)->display($context);
        // line 116
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"orderimport\">
\t\t\t\t\t\t\t<h3>Order Import</h3>
\t\t\t\t\t\t\t";
        // line 119
        $this->loadTemplate("extension/import/orderimport.twig", "extension/excel_import.twig", 119)->display($context);
        // line 120
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"affiliatesimport\">
\t\t\t\t\t\t\t<h3>Affiliates Import</h3>
\t\t\t\t\t\t\t";
        // line 123
        $this->loadTemplate("extension/import/affilatesimport.twig", "extension/excel_import.twig", 123)->display($context);
        // line 124
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"couponimport\">
\t\t\t\t\t\t\t<h3>Coupon Import</h3>
\t\t\t\t\t\t\t";
        // line 127
        $this->loadTemplate("extension/import/couponimport.twig", "extension/excel_import.twig", 127)->display($context);
        // line 128
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"userimport\">
\t\t\t\t\t\t\t<h3>User Import</h3>
\t\t\t\t\t\t\t";
        // line 131
        $this->loadTemplate("extension/import/userimport.twig", "extension/excel_import.twig", 131)->display($context);
        // line 132
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t\t <div class=\"tab-pane\" id=\"productreviewimport\">
\t\t\t\t\t\t\t<h3>Product Review</h3>
\t\t\t\t\t\t\t";
        // line 135
        $this->loadTemplate("extension/import/productreviewimport.twig", "extension/excel_import.twig", 135)->display($context);
        // line 136
        echo "\t\t\t\t\t\t </div>
\t\t\t\t\t  </div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t\t
\t\t
\t  </div>
    </div>
 </div>
</div>
";
        // line 148
        echo ($context["footer"] ?? null);
    }

    public function getTemplateName()
    {
        return "extension/excel_import.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  248 => 148,  234 => 136,  232 => 135,  227 => 132,  225 => 131,  220 => 128,  218 => 127,  213 => 124,  211 => 123,  206 => 120,  204 => 119,  199 => 116,  197 => 115,  192 => 112,  190 => 111,  185 => 108,  183 => 107,  178 => 104,  176 => 103,  171 => 100,  169 => 99,  164 => 96,  162 => 95,  89 => 24,  81 => 20,  78 => 19,  70 => 15,  68 => 14,  62 => 10,  51 => 8,  47 => 7,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/excel_import.twig", "");
    }
}

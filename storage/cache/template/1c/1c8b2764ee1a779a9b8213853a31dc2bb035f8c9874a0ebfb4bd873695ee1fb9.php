<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/osapi.twig */
class __TwigTemplate_a390816ad5e200b7548981e8d7a98c7021cd18ad2fe3d777671ea037c15bd7ac extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <h1>";
        // line 5
        echo (((($context["heading_title"] ?? null) . " (v.") . ($context["os_version"] ?? null)) . ")");
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 7
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 8
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 8);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 8);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 10
        echo "      </ul>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 13
        if (($context["error_warning"] ?? null)) {
            // line 14
            echo "    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 18
        echo "    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i> ";
        // line 20
        echo ($context["text_edit"] ?? null);
        echo "</h3>
      </div>  
      <div class=\"panel-body\">
        <form action=\"";
        // line 23
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form-osapi\" class=\"form-horizontal\">
\t\t<div class=\"form-group\">
\t\t\t<label class=\"col-sm-2 control-label\" for=\"input-info\">";
        // line 25
        echo ($context["entry_info"] ?? null);
        echo "</label>
\t\t\t<div class=\"col-sm-10\">\t\t
\t\t\t<p>Please copy the following Configuration Key into <a href=\"http://www.onesaas.com\" title=\"OneSaas\">OneSaas</a> configuration to get connected</p>
\t\t\t</div>
\t\t</div>
\t\t<div class=\"form-group\">\t
\t\t<label class=\"col-sm-2 control-label\" for=\"input-key\">";
        // line 31
        echo ($context["entry_key"] ?? null);
        echo "</label>\t\t
\t\t\t<div class=\"col-sm-10\">\t
\t\t\t<textarea cols=\"100\" rows=\"4\" onclick=\"this.focus();this.select()\" readonly>";
        // line 33
        echo ($context["configkey"] ?? null);
        echo "</textarea>
\t\t\t</div>
\t\t</div>\t
\t\t</div>
\t    </form>\t
\t  </div>
    </div>
  </div>
</div>
";
        // line 42
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/module/osapi.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  121 => 42,  109 => 33,  104 => 31,  95 => 25,  90 => 23,  84 => 20,  80 => 18,  72 => 14,  70 => 13,  65 => 10,  54 => 8,  50 => 7,  45 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/osapi.twig", "");
    }
}

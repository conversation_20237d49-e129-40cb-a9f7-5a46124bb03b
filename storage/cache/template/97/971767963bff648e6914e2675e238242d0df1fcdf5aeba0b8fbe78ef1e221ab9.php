<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/upsmodule/country.twig */
class __TwigTemplate_81bb34ec2fa0c3b8ac6dd0133a8a41a5f67e931232bc99d5b53d9a0ea0e1d0b3 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo " ";
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
    <div class=\"page-header\">
        <div class=\"container-fluid\">
            <h1 class=\"logo\">";
        // line 5
        echo ($context["text_UPS_Shipping_Module"] ?? null);
        echo "</h1>
            <ul class=\"breadcrumb\">
                <li><a href=\"";
        // line 7
        echo ($context["home"] ?? null);
        echo "\">";
        echo ($context["text_home"] ?? null);
        echo "</a></li>
                <li><a href=\"";
        // line 8
        echo ($context["country"] ?? null);
        echo "\">";
        echo ($context["text_form"] ?? null);
        echo "</a></li>
            </ul>
        </div>
        <div class=\"container-fluid\">
            <div class=\"alert alert-danger\" style=\"display:none\"><i class=\"fa fa-exclamation-circle\"></i>";
        // line 12
        echo ($context["error_warning"] ?? null);
        echo "
                <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
            </div>
            <div class=\"panel panel-default\">
                <div class=\"panel-heading\">
                    <h3 class=\"panel-title\">";
        // line 17
        echo ($context["text_form"] ?? null);
        echo "</h3>
                </div>
                <div class=\"panel-body\">
                    <form action=\"";
        // line 20
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" id=\"form-article\" class=\"form-horizontal\">
                        <div class=\"tab-content\">
                            <div class=\"form-group\">
                                <h5 class=\"col-sm-3 control-label\">";
        // line 23
        echo ($context["text_select_country"] ?? null);
        echo "</h5>
                                <div class=\"col-sm-7\">
                                    <select class=\"form-control\" name=\"Country\">
                                        ";
        // line 26
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["totals"] ?? null));
        foreach ($context['_seq'] as $context["key"] => $context["value"]) {
            // line 27
            echo "                                            <option ";
            if (($context["key"] == ($context["isCountry"] ?? null))) {
                echo "selected";
            }
            echo " value=\"";
            echo $context["key"];
            echo "\" >";
            echo $context["value"];
            echo "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['key'], $context['value'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        echo "                                    </select>
                                </div>
                            </div>
                            <div class=\"pull-right\">
                                <button type=\"submit\" form=\"form-article\" class=\"btn btn-primary\">";
        // line 33
        echo ($context["button_continue"] ?? null);
        echo "</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <link rel=\"stylesheet\" type=\"text/css\" href=\"view/stylesheet/upsmodule/common.css\" />
    <script type=\"text/javascript\" src=\"view/javascript/summernote/summernote.js\"></script>
    <link href=\"view/javascript/summernote/summernote.css\" rel=\"stylesheet\" />
    <!--  Disabled by Power Image Manager. Moved in the footer; <script type=\"text/javascript\" src=\"view/javascript/summernote/opencart.js\"></script> -->
    <script type=\"text/javascript\" src=\"view/javascript/upsmodule/common.js\"></script>
";
        // line 47
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/upsmodule/country.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  134 => 47,  117 => 33,  111 => 29,  96 => 27,  92 => 26,  86 => 23,  80 => 20,  74 => 17,  66 => 12,  57 => 8,  51 => 7,  46 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/upsmodule/country.twig", "");
    }
}

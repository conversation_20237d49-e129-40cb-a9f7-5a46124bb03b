<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/opc_qb_user_guide.twig */
class __TwigTemplate_85f0f39a0ab4b5270ff2c499cc094c80019923e129381938e172a680b4e888b5 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <div class=\"pull-right\">
        <a href=\"";
        // line 6
        echo ($context["cancel"] ?? null);
        echo "\" data-toggle=\"tooltip\" title=\"Back To Module\" class=\"btn btn-default\"><i class=\"fa fa-reply\"></i></a>
      </div>
      <h1>QuickBook Online Integration User Guide</h1>
    </div>
  </div>
  <div class=\"container-fluid\">
    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\">QuickBook Online Integration User Guide</h3>
      </div>
      <div class=\"panel-body\">
        ";
        // line 17
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(range(0, 13));
        foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
            // line 18
            echo "          <div class=\"form-group\">
            <h1>Step ";
            // line 19
            echo $context["i"];
            echo ": </h1><img class=\"img-thumbnail\" src=\"image/opc_qb_step";
            echo $context["i"];
            echo ".png\" alt=\"\" />
          </div>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['i'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        echo "      </div>
    </div>

    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\">How To Generate Keys:</h3>
      </div>
      <div class=\"panel-body\">
        ";
        // line 30
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(range(0, 13));
        foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
            // line 31
            echo "          <div class=\"form-group\">
            <h1>Step ";
            // line 32
            echo $context["i"];
            echo ": </h1><img class=\"img-thumbnail\" src=\"image/opc_qbkeys_step";
            echo $context["i"];
            echo ".png\" alt=\"\" />
          </div>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['i'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 35
        echo "      </div>
    </div>
  </div>
</div>
";
        // line 39
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/module/opc_qb_user_guide.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  112 => 39,  106 => 35,  95 => 32,  92 => 31,  88 => 30,  78 => 22,  67 => 19,  64 => 18,  60 => 17,  46 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/opc_qb_user_guide.twig", "");
    }
}

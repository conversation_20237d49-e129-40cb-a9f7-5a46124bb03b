<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/affiliateexport.twig */
class __TwigTemplate_8c5b7847f26cefbd4d54d849aa1a20b61935d08ba9420b16252804e03d714e33 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div>
  <div class=\"row\">
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\">";
        // line 6
        echo ($context["entry_status"] ?? null);
        echo "</label>
\t\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  <option value=\"1\">";
        // line 9
        echo ($context["text_enabled"] ?? null);
        echo "</option>
\t\t  <option value=\"0\">";
        // line 10
        echo ($context["text_disabled"] ?? null);
        echo "</option>
\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-approved\">";
        // line 14
        echo ($context["entry_approved"] ?? null);
        echo "</label>
\t\t<select name=\"filter_approved\" id=\"input-approved\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  <option value=\"1\">";
        // line 17
        echo ($context["text_yes"] ?? null);
        echo "</option>
\t\t  <option value=\"0\">";
        // line 18
        echo ($context["text_no"] ?? null);
        echo "</option>
\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Limit (Note:Export Data limit)</label>
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_start\" value=\"0\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\"/> -
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_limit\" value=\"";
        // line 26
        echo ($context["filter_limit"] ?? null);
        echo "\" placeholder=\"Limit\" id=\"input-limit\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label> </label>
\t\t<button type=\"button\" id=\"buttonaffiliateexport\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> Export </button>
\t  </div>
\t</div>
  </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/affiliateexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  82 => 26,  71 => 18,  67 => 17,  61 => 14,  54 => 10,  50 => 9,  44 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/affiliateexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/affiliateexport.twig");
    }
}

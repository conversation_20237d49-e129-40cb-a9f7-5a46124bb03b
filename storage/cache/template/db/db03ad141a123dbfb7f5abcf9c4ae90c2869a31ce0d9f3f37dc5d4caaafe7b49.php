<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/fmpim.twig */
class __TwigTemplate_ab85c9dc48843da4907c7b43444b91fef7775101c501e76fb11a059cefa25a9b extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
";
        // line 2
        if ((($context["cke"] ?? null) &&  !($context["header"] ?? null))) {
            // line 3
            echo "  <!DOCTYPE html>
  <html dir=\"";
            // line 4
            echo ((($context["direction"] ?? null)) ? (($context["direction"] ?? null)) : ("ltr"));
            echo " \" lang=\"";
            echo ((($context["lang"] ?? null)) ? (($context["lang"] ?? null)) : (""));
            echo " \">
  <head>
  <meta charset=\"UTF-8\" />
  <title>";
            // line 7
            echo ($context["heading_title"] ?? null);
            echo " </title>
  <base href=\"";
            // line 8
            echo ($context["base"] ?? null);
            echo " \" />

  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0\" />
  <script type=\"text/javascript\" src=\"view/javascript/jquery/jquery-2.1.1.min.js\"></script>

  
  <!-- Power Image Manager -->
  <link rel=\"stylesheet\" href=\"view/javascript/jquery/jquery-ui-1.11.4.custom/jquery-ui.css\" />
  <script src=\"view/javascript/jquery/jquery-ui-1.11.4.custom/jquery-ui.min.js\"></script>
  <script type=\"text/javascript\" src=\"view/javascript/pim/pim.min.js\"></script>          
  <link rel=\"stylesheet\" type=\"text/css\" media=\"screen\" href=\"view/stylesheet/pim/pim.min.css\">
  <link rel=\"stylesheet\" type=\"text/css\" media=\"screen\" href=\"view/stylesheet/pim/theme.css\">
    ";
            // line 20
            if (($context["lang"] ?? null)) {
                echo " 
     <script type=\"text/javascript\" src=\"view/javascript/pim/i18n/";
                // line 21
                echo ($context["lang"] ?? null);
                echo ".js\"></script>  
    ";
            }
            // line 22
            echo "         \t
  <!-- Power Image Manager -->        

  <script type=\"text/javascript\" src=\"view/javascript/bootstrap/js/bootstrap.min.js\"></script>
  <link href=\"view/javascript/bootstrap/opencart/opencart.css\" type=\"text/css\" rel=\"stylesheet\" />
  <link href=\"view/javascript/font-awesome/css/font-awesome.min.css\" type=\"text/css\" rel=\"stylesheet\" />
  <link href=\"view/javascript/summernote/summernote.css\" rel=\"stylesheet\" />
  <script type=\"text/javascript\" src=\"view/javascript/summernote/summernote.js\"></script>
  <script src=\"view/javascript/jquery/datetimepicker/moment.js\" type=\"text/javascript\"></script>
  <script src=\"view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.js\" type=\"text/javascript\"></script>
  <link href=\"view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.css\" type=\"text/css\" rel=\"stylesheet\" media=\"screen\" />
  <link type=\"text/css\" href=\"view/stylesheet/stylesheet.css\" rel=\"stylesheet\" media=\"screen\" />  
  </head>
  <body>
";
        }
        // line 37
        echo "<div id=\"content\">
  <div class=\"container-fluid\">
    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-list\"></i> ";
        // line 41
        echo ($context["heading_title"] ?? null);
        echo "</h3>
        <div class=\"btn-group pull-right\">
            <button aria-hidden=\"true\" data-dismiss=\"modal\" class=\"close\" id=\"pimClose\" type=\"button\">×</button>
        </div>
      </div>
      <div class=\"panel-body\">
        <div id=\"pim\"></div>      
      </div>
    </div>
  </div>
</div>
<script type=\"text/javascript\"><!--
function getUrlParam(paramName) {
    var reParam = new RegExp('(?:[\\?&]|&amp;)' + paramName + '=([^&]+)', 'i') ;
    var match = window.location.search.match(reParam) ;

    return (match && match.length > 1) ? match[1] : '' ;
}  
\$().ready(function() {
  var funcNum = '';

  ";
        // line 62
        if ((($context["cke"] ?? null) && (($context["cke"] ?? null) != ""))) {
            // line 63
            echo "    funcNum = getUrlParam('CKEditorFuncNum');
  ";
        }
        // line 64
        echo "  
  ";
        // line 65
        if ((($context["CKEditorFuncNum"] ?? null) && (($context["CKEditorFuncNum"] ?? null) != ""))) {
            // line 66
            echo "    funcNum = ";
            echo ($context["CKEditorFuncNum"] ?? null);
            echo "
  ";
        }
        // line 67
        echo "    
    
\t\tvar elf = \$('#pim').elfinder({
\t\t\turl : 'index.php?route=common/filemanager/connector&user_token=";
        // line 70
        echo ($context["user_token"] ?? null);
        echo "',  // connector URL (REQUIRED)
\t\t\tlang : '";
        // line 71
        echo ($context["lang"] ?? null);
        echo "', /* Setup your language here! */
\t\t\tdirimage: '";
        // line 72
        echo ($context["dirimage"] ?? null);
        echo "', 
\t\t\theight: '";
        // line 73
        echo ($context["height"] ?? null);
        echo "',
      useBrowserHistory: false,
      uiOptions : {toolbar : [['home', 'back', 'forward'],['reload'],['mkdir', 'upload'],['open', 'download', 'getfile'],['info'],['quicklook'],['copy', 'cut', 'paste'],['rm'],['duplicate', 'rename', 'edit', 'resize'],['extract', 'archive', 'sort'],['search'],['view'],['help']]},\t\t
      contextmenu: {navbar: [\"open\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"info\"],cwd: [\"reload\", \"back\", \"|\", \"upload\", \"mkdir\", \"mkfile\", \"paste\", \"|\", \"sort\", \"|\", \"info\"],files: [\"getfile\", \"|\", \"open\", \"quicklook\", \"|\", \"download\", \"|\", \"copy\", \"cut\", \"paste\", \"duplicate\", \"|\", \"rm\", \"|\", \"edit\", \"rename\", \"resize\", \"|\", \"archive\",\"extract\", \"|\", \"info\"]},
      ";
        // line 77
        if ((((($context["target"] ?? null) && ($context["thumb"] ?? null)) && ($context["target"] ?? null)) && ($context["thumb"] ?? null))) {
            // line 78
            echo "        getFileCallback : function(files, fm) {
          a = files.url;
\t\t\t\t\tb = a.replace('";
            // line 80
            echo ($context["https_catalog"] ?? null);
            echo "','');\t
\t\t\t\t\tb = b.replace('";
            // line 81
            echo ($context["dirimage"] ?? null);
            echo "','');\t
          var img = \$('#";
            // line 82
            echo ($context["thumb"] ?? null);
            echo "').find('img');
          ";
            // line 83
            if (($context["target"] ?? null)) {
                // line 84
                echo "\t\t\t\t\t\t\$('#";
                echo ($context["target"] ?? null);
                echo "').val(decodeURIComponent(b));
\t\t\t\t\t";
            }
            // line 86
            echo "\t\t\t\t\t
          
          var jorunal_is_unbelivable_piece_of_SHIT = img.attr('data-ng-src');
          if (typeof Journal2Config !== typeof undefined && Journal2Config !== false && angular !== typeof undefined && typeof jorunal_is_unbelivable_piece_of_SHIT !== typeof undefined && jorunal_is_unbelivable_piece_of_SHIT !== false) {
            img.attr('data-ng-src',a);
            img.attr('src',a);
            var currentElement = \$('#";
            // line 92
            echo ($context["target"] ?? null);
            echo "');
            var scope = angular.element(currentElement).scope();
            scope.image = b;
            scope.\$apply();
            \$('#pimClose').click();
          } else {
            img.attr('src', files.tmb);
          }
          \$('#modal-image').remove();
\t\t\t\t\t\$('.modal-backdrop').remove();
        }, 
      ";
        }
        // line 103
        echo " 
      ";
        // line 104
        if ((($context["cke"] ?? null) && (($context["cke"] ?? null) != ""))) {
            echo " 
        getFileCallback : function(file) {
          window.opener.CKEDITOR.tools.callFunction(funcNum, file.url)
          self.close();\t
        },
        ";
        }
        // line 109
        echo " 
      ";
        // line 110
        if ((($context["CKEditorFuncNum"] ?? null) && (($context["CKEditorFuncNum"] ?? null) != ""))) {
            echo " 
        getFileCallback : function(file) {
          window.CKEDITOR.tools.callFunction(funcNum,file.url);
          self.close();\t
      \t\tdelete CKEditorFuncNum;
      \t\t\$('#modal-image').modal('hide');
      \t\t\$('#modal-image').remove();          
        },
        ";
        }
        // line 118
        echo "         
        
        
\t\t\t\t";
        // line 121
        if ((($context["productmanager"] ?? null) && (($context["productmanager"] ?? null) != ""))) {
            echo " 
\t\t\t\t\t\tgetFileCallback : function(file) {
\t\t\t\t\t\t\tvar pr_id = \$('body').attr('data-current-product-id');
\t\t\t\t\t\t\ta = file.url;
\t\t\t\t\t\t\tb = a.replace('";
            // line 125
            echo ($context["dirimage"] ?? null);
            echo "','');\t
\t\t\t\t\t\t\tb = b.replace('";
            // line 126
            echo ($context["https_catalog"] ?? null);
            echo "','');\t\t\t\t\t\t\t\t
\t\t\t\t\t\t\tdoSave(pr_id, 'image',b );
\t\t\t\t\t\t\t\$('#modal-image').modal('hide');\t\t\t\t\t\t\t\t
\t\t\t\t\t\t},
\t\t\t\t";
        }
        // line 130
        echo " 
\t\t\t\t
        commandsOptions : {
          getfile : {
            oncomplete : 'close',
          }
        }              
      
\t\t}).elfinder('instance');


  });
  
//--></script>
";
        // line 144
        echo ($context["footer"] ?? null);
        echo "
";
        // line 145
        if (((($context["cke"] ?? null) && (($context["cke"] ?? null) != "")) &&  !($context["footer"] ?? null))) {
            // line 146
            echo "</body>
</html>
";
        }
        // line 148
        echo " ";
    }

    public function getTemplateName()
    {
        return "extension/module/fmpim.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  298 => 148,  293 => 146,  291 => 145,  287 => 144,  271 => 130,  263 => 126,  259 => 125,  252 => 121,  247 => 118,  235 => 110,  232 => 109,  223 => 104,  220 => 103,  205 => 92,  197 => 86,  191 => 84,  189 => 83,  185 => 82,  181 => 81,  177 => 80,  173 => 78,  171 => 77,  164 => 73,  160 => 72,  156 => 71,  152 => 70,  147 => 67,  141 => 66,  139 => 65,  136 => 64,  132 => 63,  130 => 62,  106 => 41,  100 => 37,  83 => 22,  78 => 21,  74 => 20,  59 => 8,  55 => 7,  47 => 4,  44 => 3,  42 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/fmpim.twig", "");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/customergroupimport.twig */
class __TwigTemplate_c0c6b506b8300d19024002f3238bd12fbf6ded176b0dc4a04d7b64d228316de3 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["customergroupaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form_customergroup\" class=\"form-horizontal\">

\t<div class=\"row\">
\t\t
\t\t<!-- Customer Group File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["entry_customergroupimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import Button -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#form_customergroup').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t
\t\t
\t</div>
\t
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/customergroupimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/customergroupimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/customergroupimport.twig");
    }
}

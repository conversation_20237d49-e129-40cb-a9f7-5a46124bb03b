<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/productimport.twig */
class __TwigTemplate_1517fcf67acb6a6b67b14557321c921e9b44800e53b7bdc3bd43384ddd7d8187 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["productaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form-importproduct\" class=\"form-horizontal\">
\t<div class=\"row\">

\t\t<!-- Product File Import Input -->
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\" />
\t\t</div>
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 12
        echo ($context["entry_prodimportimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import type and Store -->
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label>";
        // line 19
        echo ($context["text_importtype"] ?? null);
        echo "</label>
\t\t\t<select class=\"form-control\" name=\"importtype\">
\t\t\t  <option value=\"1\">";
        // line 21
        echo ($context["text_productid"] ?? null);
        echo "</option>
\t\t\t  <option value=\"2\">";
        // line 22
        echo ($context["text_model"] ?? null);
        echo "</option>
\t\t\t</select>
\t\t\t<b>Note: </b><i>Import your products according to Product ID Or Model (Model must be unique).</i>
\t\t</div>
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label>";
        // line 28
        echo ($context["entry_store"] ?? null);
        echo "</label>
\t\t\t<select class=\"form-control\" name=\"store_id\">
\t\t\t <option value=\"0\">";
        // line 30
        echo ($context["text_default"] ?? null);
        echo "</option>
\t\t\t\t ";
        // line 31
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 32
            echo "\t\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 32);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "name", [], "any", false, false, false, 32);
            echo "</option>
\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 34
        echo "\t\t\t</select>
\t\t\t<i>Import your products according to Store.</i>
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Language and Submit -->
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label>";
        // line 42
        echo ($context["entry_language"] ?? null);
        echo "</label>\t\t
\t\t\t<select class=\"form-control\" name=\"language_id\">
\t\t\t";
        // line 44
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 45
            echo "\t\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 45);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 45);
            echo "</option>
\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 47
        echo "\t\t\t</select>
\t\t\t<i>Import your products according to Language.</i>
\t\t</div>
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label> </label>
\t\t\t<button onclick=\"\$('#form-importproduct').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t</div>
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/productimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  135 => 47,  124 => 45,  120 => 44,  115 => 42,  105 => 34,  94 => 32,  90 => 31,  86 => 30,  81 => 28,  72 => 22,  68 => 21,  63 => 19,  53 => 12,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/productimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/productimport.twig");
    }
}

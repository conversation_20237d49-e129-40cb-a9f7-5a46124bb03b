<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/excel_point.twig */
class __TwigTemplate_3f2ce12a2c29136b3f09086f540472b4c85d6a089c7272742faf1f95b6988ed4 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <h1>Excel Export Point</h1>
      <ul class=\"breadcrumb\">
         ";
        // line 7
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 8
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 8);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 8);
            echo "</a></li>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 10
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 14
        if (($context["error_warning"] ?? null)) {
            // line 15
            echo "    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 19
        echo "\t";
        if (($context["success"] ?? null)) {
            // line 20
            echo "    <div class=\"alert alert-success\"><i class=\"fa fa-check-circle\"></i> ";
            echo ($context["success"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 24
        echo "\t<div class=\"ajaxloader\">
\t<div class=\"loader\"></div>
    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i> Excel Export Point</h3>
      </div>
      <div class=\"panel-body\">
\t  <style>
\t\t.nav-tabs{
\t\t\tmargin-bottom:0px;
\t\t}
\t\t.tab-content{
\t\t\tmargin-top: 0px;
\t\t\tborder-left: 1px solid #ddd;
\t\t\tborder-right: 1px solid #ddd;
\t\t\tborder-bottom: 1px solid #ddd;
\t\t\tpadding:20px;
\t\t}
\t\t.btn-success a{
\t\t\tcolor:#fff;
\t\t\tfont-size:14px;
\t\t\ttext-transform:uppercase;
\t\t}
\t\t.tbpadding{
\t\t\tpadding:10px;
\t\t}
\t\t.ourbtn{
\t\t\tbackground-color:#921e6f;
\t\t\tborder:none;
\t\t\ttext-transform:uppercase;
\t\t}
\t\t.ourbtn:hover{
\t\t\tbackground-color:#921e6f;
\t\t\tborder:none;
\t\t}
\t\t.ourbtn i{
\t\t\tmargin-right:5px;
\t\t}
\t\t
\t\t.tab-content input {
\t\t\tbackground-color: #fafafa;
\t\t\tborder-radius: 0;
\t\t\theight: 40px;\t
\t\t}
\t\t.tab-content select{
\t\t\tbackground-color: #fafafa;
\t\t\tborder-radius: 0;
\t\t\theight: 40px;\t
\t\t}
\t\t.tab-content input {
\t\t\tbackground-color: #fafafa;
\t\t\tborder-radius: 0;
\t\t\theight: 40px;\t
\t\t}
\t\t.tab-content select{
\t\t\tbackground-color: #fafafa;
\t\t\tborder-radius: 0;
\t\t\theight: 40px;\t
\t\t}
\t\t.ajaxloader{
\t\t\tposition:relative;
\t\t}
\t\t.ajaxloader .loader{
\t\t\tposition:absolute;
\t\t\twidth:100%;
\t\t\theight:100%;
\t\t\tbackground-color:rgba(255, 255, 255, 0.8);
\t\t\tz-index:999;
\t\t\tdisplay:none;
\t\t}
\t\t.loader-img{
\t\t\tposition:absolute;
\t\t\tleft:0px;
\t\t\tright:0px;
\t\t\ttop:0px;
\t\t\tbottom:0px;
\t\t\tmargin:auto;
\t\t}
\t</style>
\t      <div class=\"tab-content\">
            <div class=\"tab-pane active in\" id=\"tab-export\">
\t\t\t\t<div class=\"row\">
\t\t\t\t\t<div class=\"col-sm-12\">
\t\t\t\t\t  <ul class=\"nav nav-tabs\">
\t\t\t\t\t\t<li class=\"active\"><a href=\"#product\" data-toggle=\"tab\">Products</a></li>
\t\t\t\t\t\t<li><a href=\"#categories\" data-toggle=\"tab\">Categories</a></li>
\t\t\t\t\t\t<li><a href=\"#manufacture\" data-toggle=\"tab\">Manufacture</a></li>
\t\t\t\t\t\t<li><a href=\"#options\" data-toggle=\"tab\">Options</a></li>
\t\t\t\t\t\t<li><a href=\"#customer_export\" data-toggle=\"tab\">Customers</a></li>
\t\t\t\t\t\t<li><a href=\"#order_export\" data-toggle=\"tab\">Orders</a></li>
\t\t\t\t\t\t<li><a href=\"#customer_group\" data-toggle=\"tab\">Customer Group</a></li>
\t\t\t\t\t\t<li><a href=\"#product_review\" data-toggle=\"tab\">Product Review</a></li>
\t\t\t\t\t\t<li role=\"presentation\" class=\"dropdown\">
\t\t\t\t\t\t<a class=\"dropdown-toggle\" data-toggle=\"dropdown\" href=\"#\" role=\"button\" aria-haspopup=\"true\" aria-expanded=\"false\">
\t\t\t\t\t\t  More <span class=\"caret\"></span>
\t\t\t\t\t\t</a>
\t\t\t\t\t\t<ul class=\"dropdown-menu\">
\t\t\t\t\t\t\t<li><a href=\"#coupon_export\" data-toggle=\"tab\">Coupons Export</a></li>
\t\t\t\t\t\t\t<li><a href=\"#user_export\" data-toggle=\"tab\">User Export</a></li>
\t\t\t\t\t\t\t<li><a href=\"#affiliate_export\" data-toggle=\"tab\">Affiliates Export</a></li>
\t\t\t\t\t\t</ul>
\t\t\t\t\t\t</li>
\t\t\t\t\t  </ul>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"col-sm-12\">
\t\t\t\t\t\t<div class=\"tab-content\">
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"categories\">
\t\t\t\t\t\t\t\t<h3>Category Export</h3>
\t\t\t\t\t\t\t\t";
        // line 132
        $this->loadTemplate("extension/export/categoriesexport.twig", "extension/excel_point.twig", 132)->display($context);
        // line 133
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane active\" id=\"product\">
\t\t\t\t\t\t\t\t<h3>Products Export</h3>
\t\t\t\t\t\t\t\t";
        // line 136
        $this->loadTemplate("extension/export/pexport.twig", "extension/excel_point.twig", 136)->display($context);
        // line 137
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"manufacture\">
\t\t\t\t\t\t\t\t<h3>Manufacturer Export</h3>
\t\t\t\t\t\t\t\t";
        // line 140
        $this->loadTemplate("extension/export/manufactureexport.twig", "extension/excel_point.twig", 140)->display($context);
        // line 141
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"customer_export\">
\t\t\t\t\t\t\t\t<h3>Customer Export</h3>
\t\t\t\t\t\t\t\t";
        // line 144
        $this->loadTemplate("extension/export/customerexport.twig", "extension/excel_point.twig", 144)->display($context);
        // line 145
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"options\">
\t\t\t\t\t\t\t\t<h3>Options</h3>
\t\t\t\t\t\t\t\t";
        // line 148
        $this->loadTemplate("extension/export/options.twig", "extension/excel_point.twig", 148)->display($context);
        // line 149
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"customer_group\">
\t\t\t\t\t\t\t\t<h3>Customer Group Export</h3>
\t\t\t\t\t\t\t\t";
        // line 152
        $this->loadTemplate("extension/export/customerGroupexport.twig", "extension/excel_point.twig", 152)->display($context);
        // line 153
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"order_export\">
\t\t\t\t\t\t\t\t<h3>Order Export</h3>
\t\t\t\t\t\t\t\t";
        // line 156
        $this->loadTemplate("extension/export/orderexport.twig", "extension/excel_point.twig", 156)->display($context);
        // line 157
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"affiliate_export\">
\t\t\t\t\t\t\t\t<h3>Affiliates Export</h3>
\t\t\t\t\t\t\t\t";
        // line 160
        $this->loadTemplate("extension/export/affiliateexport.twig", "extension/excel_point.twig", 160)->display($context);
        // line 161
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"coupon_export\">
\t\t\t\t\t\t\t\t<h3>Coupon Export</h3>
\t\t\t\t\t\t\t\t";
        // line 164
        $this->loadTemplate("extension/export/couponexport.twig", "extension/excel_point.twig", 164)->display($context);
        // line 165
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"user_export\">
\t\t\t\t\t\t\t\t<h3>User Export</h3>
\t\t\t\t\t\t\t\t";
        // line 168
        $this->loadTemplate("extension/export/userexport.twig", "extension/excel_point.twig", 168)->display($context);
        // line 169
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"tab-pane\" id=\"product_review\">
\t\t\t\t\t\t\t\t<h3>Product Review</h3>
\t\t\t\t\t\t\t\t";
        // line 172
        $this->loadTemplate("extension/export/productreview.twig", "extension/excel_point.twig", 172)->display($context);
        // line 173
        echo "\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t  </div>
    </div>
\t</div>
 </div>
</div>
<script type=\"text/javascript\"><!--
\$('#buttonusers').on('click', function(){
\turl = 'index.php?route=extension/excel_export/userexport&user_user_token=";
        // line 186
        echo ($context["user_user_token"] ?? null);
        echo "';
\tvar filter_limit = \$('#coupon_export input[name=\\'filter_limit\\']').val();
\t
\tif(filter_limit){
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t\t
\tvar filter_start = \$('#coupon_export select[name=\\'filter_start\\']').val();
\t
\tif(filter_start != '*'){
\t\turl += '&filter_start=' + encodeURIComponent(filter_start); 
\t}
\t
\tlocation = url;
});

\$('#buttonproductreview').on('click', function(){
\turl = 'index.php?route=extension/excel_export/exportproductreview&user_token=";
        // line 203
        echo ($context["user_token"] ?? null);
        echo "';
\tvar filter_start = \$('#product_review input[name=\\'filter_start\\']').val();
\tif(filter_start){
\t\turl += '&filter_start=' + encodeURIComponent(filter_start);
\t}
\t
\tvar filter_limit = \$('#product_review input[name=\\'filter_limit\\']').val();
\tif(filter_limit){
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t\t
\tvar filter_status = \$('#product_review select[name=\\'filter_status\\']').val();
\tif(filter_status != '*'){
\t\turl += '&filter_status=' + encodeURIComponent(filter_status); 
\t}
\t
\tlocation = url;
});

\$('#buttonoptions').on('click', function(){
\turl = 'index.php?route=extension/excel_export/exportproductsoptions&user_token=";
        // line 223
        echo ($context["user_token"] ?? null);
        echo "';
\tvar filter_language_id = \$('#options select[name=\\'filter_language_id\\']').val();

\tif(filter_language_id != '*'){
\t\turl += '&filter_language_id=' + encodeURIComponent(filter_language_id);
\t}
\t
\tvar filter_limit = \$('#options input[name=\\'filter_limit\\']').val();
\t
\tif(filter_limit){
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t\t
\tvar filter_start = \$('#options select[name=\\'filter_start\\']').val();
\t
\tif(filter_start != '*'){
\t\turl += '&filter_start=' + encodeURIComponent(filter_start); 
\t}
\t
\tlocation = url;
});

\$('#buttoncoupons').on('click', function(){
\turl = 'index.php?route=extension/excel_export/couponsexport&user_token=";
        // line 246
        echo ($context["user_token"] ?? null);
        echo "';
\tvar filter_limit = \$('#coupon_export input[name=\\'filter_limit\\']').val();
\t
\tif (filter_limit) {
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t\t
\tvar filter_start = \$('#coupon_export select[name=\\'filter_start\\']').val();
\t
\tif (filter_start != '*') {
\t\turl += '&filter_start=' + encodeURIComponent(filter_start); 
\t}
\t
\tlocation = url;
});

\$('#buttonaffiliateexport').on('click', function() {
\turl = 'index.php?route=extension/excel_export/affilatesexport&user_token=";
        // line 263
        echo ($context["user_token"] ?? null);
        echo "';
\t
\tvar filter_start = \$('#affiliate_export input[name=\\'filter_start\\']').val();
\t
\tif (filter_start) {
\t\turl += '&filter_start=' + encodeURIComponent(filter_start);
\t}
\t
\tvar filter_limit = \$('#affiliate_export input[name=\\'filter_limit\\']').val();
\t
\tif (filter_limit) {
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t\t
\tvar filter_status = \$('#affiliate_export select[name=\\'filter_status\\']').val();
\t
\tif (filter_status != '*') {
\t\turl += '&filter_status=' + encodeURIComponent(filter_status); 
\t}\t
\t
\tvar filter_approved = \$('#affiliate_export select[name=\\'filter_approved\\']').val();
\t
\tif (filter_approved != '*') {
\t\turl += '&filter_approved=' + encodeURIComponent(filter_approved);
\t}\t
\t
\tlocation = url;
});

//Start Orders
\$('#button-order_export').on('click', function(){
\turl = 'index.php?route=extension/excel_export/exportOrder&user_token=";
        // line 294
        echo ($context["user_token"] ?? null);
        echo "';
\t
\t/* var filter_to_order_id = \$('#order_export input[name=\\'filter_to_order_id\\']').val();

\tif(filter_to_order_id != ''){
\t\turl += '&filter_to_order_id=' + encodeURIComponent(filter_to_order_id);
\t}
\t
\tvar filter_from_order_id = \$('#order_export input[name=\\'filter_from_order_id\\']').val();

\tif(filter_from_order_id != ''){
\t\turl += '&filter_from_order_id=' + encodeURIComponent(filter_from_order_id);
\t}
\t
\tvar filter_order_status = \$('#order_export select[name=\\'filter_order_status\\']').val();

\tif(filter_order_status != '*'){
\t\turl += '&filter_order_status=' + encodeURIComponent(filter_order_status);
\t}
\t
\tvar filter_total = \$('#order_export input[name=\\'filter_total\\']').val();

\tif(filter_total != ''){
\t\turl += '&filter_total=' + encodeURIComponent(filter_total);
\t}
\t
\tvar filter_to_date_added = \$('#order_export input[name=\\'filter_to_date_added\\']').val();

\tif(filter_to_date_added != ''){
\t\turl += '&filter_to_date_added=' + encodeURIComponent(filter_to_date_added);
\t}
\t
\tvar filter_from_date_added = \$('#order_export input[name=\\'filter_from_date_added\\']').val();

\tif(filter_from_date_added != ''){
\t\turl += '&filter_from_date_added=' + encodeURIComponent(filter_from_date_added);
\t}
\t
\tvar filter_to_date_modified = \$('#order_export input[name=\\'filter_to_date_modified\\']').val();

\tif(filter_to_date_modified != ''){
\t\turl += '&filter_to_date_modified=' + encodeURIComponent(filter_to_date_modified);
\t}
\t
\tvar filter_form_date_modified = \$('#order_export input[name=\\'filter_form_date_modified\\']').val();

\tif(filter_form_date_modified != ''){
\t\turl += '&filter_form_date_modified=' + encodeURIComponent(filter_form_date_modified);
\t}
\t
\tvar filter_start = \$('#order_export input[name=\\'filter_start\\']').val();

\tif(filter_start != ''){
\t\turl += '&filter_start=' + encodeURIComponent(filter_start);
\t}

\tvar filter_limit = \$('#order_export input[name=\\'filter_limit\\']').val();

\tif(filter_limit != ''){
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t
\tvar filter_name = \$('#order_export input[name=\\'customer_name\\']').val();
\t
\tif (filter_name) {
\t\turl += '&filter_name=' + encodeURIComponent(filter_name);
\t}
\t
\tvar filter_customer_group_id = \$('#order_export select[name=\\'filter_customer_group_id\\']').val();
\t
\tif (filter_customer_group_id != '*') {
\t\turl += '&filter_customer_group_id=' + encodeURIComponent(filter_customer_group_id);
\t}\t
\t
\tvar filter_eformat = \$('#order_export select[name=\\'filter_eformat\\']').val();
\t
\tif(filter_eformat){
\t\turl += '&filter_eformat=' + encodeURIComponent(filter_eformat);
\t} */
\t
\texportOrders(url);
});


function exportOrders(url){
\t\$.ajax({
\t\turl: url,
\t\ttype: 'post',
\t\tdata: \$('#order_export input[type=\\'text\\'], #order_export input[type=\\'hidden\\'], #order_export input[type=\\'radio\\']:checked, #order_export input[type=\\'checkbox\\']:checked, #order_export select, #order_export textarea'),
\t\tdataType: 'json',
\t\tbeforeSend: function(){
\t\t\t\$('.ajaxloader .loader').html('<img class=\"loader-img\" src=\"view/image/excelpoint/pie_loader.gif\" />');
\t\t\t\$('.ajaxloader .loader').css('display','block');
\t\t\t\$('.button_filter_categories').button('loading');
\t\t},
\t\tcomplete: function(){
\t\t\t\$('.button_filter_categories').button('reset');
\t\t},
\t\tsuccess: function(json){
\t\t\t\$('.alert, .text-danger').remove();
\t\t\t\$('.form-group').removeClass('has-error');
\t\t\t
\t\t\tif(json['download']){
\t\t\t\t\$('.ajaxloader .loader').html('');
\t\t\t\t\$('.ajaxloader .loader').css('display','none');
\t\t\t\twindow.location=json['download'];
\t\t\t}
\t\t\t
\t\t\tsetTimeout(function(){
\t\t\t\tif(json['next']){
\t\t\t\t  exportOrders(json['next']);
\t\t\t\t}
\t\t\t}, 1000);
\t\t},
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
        }
\t});
}
//END Orders

\$('#button_filter_customerGroup').on('click', function(){
\turl = 'index.php?route=extension/excel_export/exportCustomerGroup&user_token=";
        // line 416
        echo ($context["user_token"] ?? null);
        echo "';
\t
\tvar filter_language_id = \$('#customer_group select[name=\\'filter_language_id\\']').val();

\tif(filter_language_id != '*'){
\t\turl += '&filter_language=' + encodeURIComponent(filter_language_id);
\t}
\t
\tvar filter_start = \$('#customer_group input[name=\\'filter_start\\']').val();

\tif(filter_start != '*'){
\t\turl += '&filter_start=' + encodeURIComponent(filter_start);
\t}

\tvar filter_limit = \$('#customer_group input[name=\\'filter_limit\\']').val();

\tif(filter_limit != '*'){
\t\turl += '&filter_limit=' + encodeURIComponent(filter_limit);
\t}
\t
\tlocation = url;
});

//Customer Start
\$('#button-customer_export').on('click', function(){
\turl = 'index.php?route=extension/excel_export/exportCustomer&user_token=";
        // line 441
        echo ($context["user_token"] ?? null);
        echo "';
\texportcustomers(url);
});


function exportcustomers(url){
\t\$.ajax({
\t\turl: url,
\t\ttype: 'post',
\t\tdata: \$('#customer_export input[type=\\'text\\'], #customer_export input[type=\\'hidden\\'], #customer_export input[type=\\'radio\\']:checked, #customer_export input[type=\\'checkbox\\']:checked, #customer_export select, #customer_export textarea'),
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('.ajaxloader .loader').html('<img class=\"loader-img\" src=\"view/image/excelpoint/pie_loader.gif\" />');
\t\t\t\$('.ajaxloader .loader').css('display','block');
\t\t\t\$('.button_filter_categories').button('loading');
\t\t},
\t\tcomplete: function(){
\t\t\t\$('.button_filter_categories').button('reset');
\t\t},
\t\tsuccess: function(json){
\t\t\t\$('.alert, .text-danger').remove();
\t\t\t\$('.form-group').removeClass('has-error');
\t\t\t
\t\t\tif(json['download']){
\t\t\t\t\$('.ajaxloader .loader').html('');
\t\t\t\t\$('.ajaxloader .loader').css('display','none');
\t\t\t\twindow.location=json['download'];
\t\t\t}
\t\t\t
\t\t\tsetTimeout(function(){
\t\t\t\tif(json['next']){
\t\t\t\t  exportcustomers(json['next']);
\t\t\t\t}
\t\t\t}, 1000);
\t\t},
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
        }
\t});
}
//Customer END

//Start Manufacturer
\$('#button_filter_manufacture').on('click', function(){
\tvar url = 'index.php?route=extension/excel_export/exportManufacture&user_token=";
        // line 485
        echo ($context["user_token"] ?? null);
        echo "';
\texportmanufacturer(url);
});

function exportmanufacturer(url){
\t\$.ajax({
\t\turl: url,
\t\ttype: 'post',
\t\tdata: \$('#manufacture input[type=\\'text\\'], #manufacture input[type=\\'hidden\\'], #manufacture input[type=\\'radio\\']:checked, #manufacture input[type=\\'checkbox\\']:checked, #manufacture select, #manufacture textarea'),
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('.ajaxloader .loader').html('<img class=\"loader-img\" src=\"view/image/excelpoint/pie_loader.gif\" />');
\t\t\t\$('.ajaxloader .loader').css('display','block');
\t\t\t\$('.button_filter_categories').button('loading');
\t\t},
\t\tcomplete: function(){
\t\t\t\$('.button_filter_categories').button('reset');
\t\t},
\t\tsuccess: function(json){
\t\t\t\$('.alert, .text-danger').remove();
\t\t\t\$('.form-group').removeClass('has-error');
\t\t\t
\t\t\tif(json['download']){
\t\t\t\t\$('.ajaxloader .loader').html('');
\t\t\t\t\$('.ajaxloader .loader').css('display','none');
\t\t\t\twindow.location=json['download'];
\t\t\t}
\t\t\t
\t\t\tsetTimeout(function(){
\t\t\t\tif(json['next']){
\t\t\t\t  exportmanufacturer(json['next']);
\t\t\t\t}
\t\t\t}, 1000);
\t\t},
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
        }
\t});
}
//Manufacture END


///Categories Start
\$('#button_filter_categories').on('click', function(){
\texportcategory('index.php?route=extension/excel_export/exportCategories&user_token=";
        // line 529
        echo ($context["user_token"] ?? null);
        echo "');
});

function exportcategory(url){
\t\$.ajax({
\t\turl: url,
\t\ttype: 'post',
\t\tdata: \$('#categories input[type=\\'text\\'], #categories input[type=\\'hidden\\'], #categories input[type=\\'radio\\']:checked, #categories input[type=\\'checkbox\\']:checked, #categories select, #categories textarea'),
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('.ajaxloader .loader').html('<img class=\"loader-img\" src=\"view/image/excelpoint/pie_loader.gif\" />');
\t\t\t\$('.ajaxloader .loader').css('display','block');
\t\t\t\$('.button_filter_categories').button('loading');
\t\t},
\t\tcomplete: function(){
\t\t\t\$('.button_filter_categories').button('reset');
\t\t},
\t\tsuccess: function(json){
\t\t\t\$('.alert, .text-danger').remove();
\t\t\t\$('.form-group').removeClass('has-error');
\t\t\t
\t\t\tif(json['download']){
\t\t\t\t\$('.ajaxloader .loader').html('');
\t\t\t\t\$('.ajaxloader .loader').css('display','none');
\t\t\t\twindow.location=json['download'];
\t\t\t}
\t\t\t
\t\t\tsetTimeout(function(){
\t\t\t\tif(json['next']){
\t\t\t\t  exportcategory(json['next']);
\t\t\t\t}
\t\t\t}, 1000);
\t\t},
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
        }
\t});
}
///Categories End

///Products Start
\$('#button-filter_product').on('click', function(){
\tvar url = 'index.php?route=extension/excel_export/exportproducts&user_token=";
        // line 571
        echo ($context["user_token"] ?? null);
        echo "';
\texportproducts(url);
});

function exportproducts(url){
\t\$.ajax({
\t\turl: url,
\t\ttype: 'post',
\t\tdata: \$('#product input[type=\\'text\\'], #product input[type=\\'hidden\\'], #product input[type=\\'radio\\']:checked, #product input[type=\\'checkbox\\']:checked, #product select, #product textarea'),
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('.ajaxloader .loader').html('<img class=\"loader-img\" src=\"view/image/excelpoint/pie_loader.gif\" />');
\t\t\t\$('.ajaxloader .loader').css('display','block');
\t\t\t\$('.button_filter_categories').button('loading');
\t\t},
\t\tcomplete: function(){
\t\t\t\$('.button_filter_categories').button('reset');
\t\t},
\t\tsuccess: function(json){
\t\t\t\$('.alert, .text-danger').remove();
\t\t\t\$('.form-group').removeClass('has-error');
\t\t\t
\t\t\tif(json['download']){
\t\t\t\t\$('.ajaxloader .loader').html('');
\t\t\t\t\$('.ajaxloader .loader').css('display','none');
\t\t\t\twindow.location=json['download'];
\t\t\t}
\t\t\t
\t\t\tsetTimeout(function(){
\t\t\t\tif(json['next']){
\t\t\t\t  exportproducts(json['next']);
\t\t\t\t}
\t\t\t}, 1000);
\t\t},
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
        }
\t});
}
///Products End
//--></script>
<script type=\"text/javascript\"><!--
\$('input[name=\\'customer_name\\']').autocomplete({
\t'source': function(request, response) {
\t\t\$.ajax({
\t\t\turl: 'index.php?route=sale/customer/autocomplete&user_token=";
        // line 616
        echo ($context["user_token"] ?? null);
        echo "&filter_name=' +  encodeURIComponent(request),
\t\t\tdataType: 'json',\t\t\t
\t\t\tsuccess: function(json) {
\t\t\t\tresponse(\$.map(json, function(item) {
\t\t\t\t\treturn {
\t\t\t\t\t\tlabel: item['name'],
\t\t\t\t\t\tvalue: item['customer_id']
\t\t\t\t\t}
\t\t\t\t}));
\t\t\t}
\t\t});
\t},
\t'select': function(item) {
\t\t\$('input[name=\\'customer_name\\']').val(item['label']);
\t}\t
});


\$('input[name=\\'filter_email\\']').autocomplete({
\t'source': function(request, response) {
\t\t\$.ajax({
\t\t\turl: 'index.php?route=sale/customer/autocomplete&user_token=";
        // line 637
        echo ($context["user_token"] ?? null);
        echo "&filter_email=' +  encodeURIComponent(request),
\t\t\tdataType: 'json',\t\t\t
\t\t\tsuccess: function(json) {
\t\t\t\tresponse(\$.map(json, function(item) {
\t\t\t\t\treturn {
\t\t\t\t\t\tlabel: item['email'],
\t\t\t\t\t\tvalue: item['customer_id']
\t\t\t\t\t}
\t\t\t\t}));
\t\t\t}
\t\t});
\t},
\t'select': function(item) {
\t\t\$('input[name=\\'filter_email\\']').val(item['label']);
\t}\t
});

\$('input[name=\\'filter_name\\']').autocomplete({
\t'source': function(request, response) {
\t\t\$.ajax({
\t\t\turl: 'index.php?route=catalog/product/autocomplete&user_token=";
        // line 657
        echo ($context["user_token"] ?? null);
        echo "&filter_name=' +  encodeURIComponent(request),
\t\t\tdataType: 'json',
\t\t\tsuccess: function(json) {
\t\t\t\tresponse(\$.map(json, function(item) {
\t\t\t\t\treturn {
\t\t\t\t\t\tlabel: item['name'],
\t\t\t\t\t\tvalue: item['product_id']
\t\t\t\t\t}
\t\t\t\t}));
\t\t\t}
\t\t});
\t},
\t'select': function(item) {
\t\t\$('input[name=\\'filter_name\\']').val(item['label']);
\t}
});

\$('input[name=\\'filter_model\\']').autocomplete({
\t'source': function(request, response) {
\t\t\$.ajax({
\t\t\turl: 'index.php?route=catalog/product/autocomplete&user_token=";
        // line 677
        echo ($context["user_token"] ?? null);
        echo "&filter_model=' +  encodeURIComponent(request),
\t\t\tdataType: 'json',
\t\t\tsuccess: function(json) {
\t\t\t\tresponse(\$.map(json, function(item) {
\t\t\t\t\treturn {
\t\t\t\t\t\tlabel: item['model'],
\t\t\t\t\t\tvalue: item['product_id']
\t\t\t\t\t}
\t\t\t\t}));
\t\t\t}
\t\t});
\t},
\t'select': function(item) {
\t\t\$('input[name=\\'filter_model\\']').val(item['label']);
\t}
});
//--></script>
<script type=\"text/javascript\"><!--
\$('.date').datetimepicker({
\tpickTime: false
});
//--></script>
";
        // line 699
        echo ($context["footer"] ?? null);
    }

    public function getTemplateName()
    {
        return "extension/excel_point.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  844 => 699,  819 => 677,  796 => 657,  773 => 637,  749 => 616,  701 => 571,  656 => 529,  609 => 485,  562 => 441,  534 => 416,  409 => 294,  375 => 263,  355 => 246,  329 => 223,  306 => 203,  286 => 186,  271 => 173,  269 => 172,  264 => 169,  262 => 168,  257 => 165,  255 => 164,  250 => 161,  248 => 160,  243 => 157,  241 => 156,  236 => 153,  234 => 152,  229 => 149,  227 => 148,  222 => 145,  220 => 144,  215 => 141,  213 => 140,  208 => 137,  206 => 136,  201 => 133,  199 => 132,  89 => 24,  81 => 20,  78 => 19,  70 => 15,  68 => 14,  62 => 10,  51 => 8,  47 => 7,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/excel_point.twig", "");
    }
}

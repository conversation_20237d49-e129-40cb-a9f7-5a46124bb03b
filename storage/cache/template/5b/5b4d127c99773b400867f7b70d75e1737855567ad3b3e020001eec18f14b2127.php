<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import_export.twig */
class __TwigTemplate_f2d056049115f9020bae9e6a459151612a0d86137af59a370d068cd6c89d5c2a extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo "
<link href=\"view/stylesheet/excelpoint/style.css\" rel=\"stylesheet\" />
<link href=\"view/stylesheet/excelpoint/bootstrap-select.min.css\" rel=\"stylesheet\" />
<link href=\"view/stylesheet/excelpoint/bootstrap-switch.css\" rel=\"stylesheet\">
<link href=\"view/stylesheet/excelpoint/dropify.min.css\" rel=\"stylesheet\">
";
        // line 6
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
\t<div class=\"pull-right\">
        <a href=\"";
        // line 11
        echo ($context["addexport"] ?? null);
        echo "\" class=\"btn btn-primary\"><i class=\"fa fa-download\"></i> ";
        echo ($context["text_addexport"] ?? null);
        echo "</a>
        <a href=\"";
        // line 12
        echo ($context["addimport"] ?? null);
        echo "\" class=\"btn btn-warning\"><i class=\"fa fa-upload\"></i> ";
        echo ($context["text_addimport"] ?? null);
        echo "</a></div>
      <h1>";
        // line 13
        echo ($context["heading_title"] ?? null);
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 15
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 16
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 16);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 16);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 18
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 22
        if (($context["error_warning"] ?? null)) {
            // line 23
            echo "    <div class=\"alert alert-danger alert-dismissible\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 27
        echo "\t
\t<div class=\"alert alert-warning alert-dismissible\">
\t\t<h4><i class=\"fa fa-exclamation-circle\"></i> Backup your database</h4>
\t\tBefore using extension recommended to take database backup. As after using this action cannot be undo.
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
\t
\t<div class=\"row\">
\t\t<div class=\"col-sm-4\">
\t\t\t<div class=\"cardinfo info\">
\t\t\t";
        // line 37
        echo ($context["entry_total_cases"] ?? null);
        echo "
\t\t\t\t<p>";
        // line 38
        echo ($context["totalcases"] ?? null);
        echo "</p>
\t\t\t\t<i class=\"fa fa-bar-chart-o\"></i>
\t\t\t</div>
\t\t</div>
\t\t<div class=\"col-sm-4\">
\t\t\t<div class=\"cardinfo warning\">
\t\t\t\t";
        // line 44
        echo ($context["entry_enabled_cases"] ?? null);
        echo "
\t\t\t\t<p>";
        // line 45
        echo ($context["totalenablecases"] ?? null);
        echo "</p>
\t\t\t\t<i class=\"fa fa-envelope-open\"></i>
\t\t\t</div>
\t\t</div>
\t\t<div class=\"col-sm-4\">
\t\t\t<div class=\"cardinfo success\">
\t\t\t\t";
        // line 51
        echo ($context["entry_disabled_cases"] ?? null);
        echo "
\t\t\t\t<p>";
        // line 52
        echo ($context["totaldisablecases"] ?? null);
        echo "</p>
\t\t\t\t<i class=\"fa fa-envelope\"></i>
\t\t\t</div>
\t\t</div>
\t</div>
\t
    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i> ";
        // line 60
        echo ($context["text_edit"] ?? null);
        echo "</h3>
      </div>
      <div class=\"panel-body\">
\t\t<div class=\"loader hide\"><img src=\"view/image/loading.gif\" class=\"img-responsive\" /></div>
\t\t <ul class=\"nav nav-tabs\">
            <li class=\"active\"><a href=\"#tab-general\" data-toggle=\"tab\">";
        // line 65
        echo ($context["text_list"] ?? null);
        echo "</a></li>
            <li><a href=\"#tab-data\" data-toggle=\"tab\">";
        // line 66
        echo ($context["text_migration"] ?? null);
        echo "</a></li>
          </ul>
\t\t<div class=\"form-horizontal\">
\t\t<div class=\"tab-content\">
\t\t\t<div class=\"tab-pane active\" id=\"tab-general\">
\t\t\t\t<!-- Case List Start -->
\t\t\t\t<div class=\"table-responsive\">
\t\t\t\t\t<table class=\"table table-bordered caselist\">
\t\t\t\t\t\t<thead>
\t\t\t\t\t\t\t<tr>
\t\t\t\t\t\t\t\t<td></td>
\t\t\t\t\t\t\t\t<td>
\t\t\t\t\t\t\t\t\t<div class=\"small_select\">
\t\t\t\t\t\t\t\t\t\t<div class=\"input-group input-group-sm\">
\t\t\t\t\t\t\t\t\t\t  <input type=\"text\" class=\"form-control\" name=\"search\" placeholder=\"Search for...\" />
\t\t\t\t\t\t\t\t\t\t  <span class=\"input-group-btn\">
\t\t\t\t\t\t\t\t\t\t\t<button class=\"btn btn-search\" type=\"button\"><i class=\"fa fa-search\"></i></button>
\t\t\t\t\t\t\t\t\t\t  </span>
\t\t\t\t\t\t\t\t\t\t </div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t<div class=\"small_select\">
\t\t\t\t\t\t\t\t\t\t<div class=\"input-group input-group-sm\">
\t\t\t\t\t\t\t\t\t\t\t<span class=\"input-group-addon\"><i class=\"fa fa-plus\"></i> ";
        // line 88
        echo ($context["text_user"] ?? null);
        echo "</span>
\t\t\t\t\t\t\t\t\t\t\t<select class=\"input-sm\" name=\"select_user\">
\t\t\t\t\t\t\t\t\t\t\t<option value=\"\"> ";
        // line 90
        echo ($context["text_select"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t\t\t";
        // line 91
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["users"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["user"]) {
            // line 92
            echo "\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["user"], "user_id", [], "any", false, false, false, 92);
            echo "\"> ";
            echo twig_get_attribute($this->env, $this->source, $context["user"], "username", [], "any", false, false, false, 92);
            echo "</option>
\t\t\t\t\t\t\t\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['user'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 93
        echo "\t
\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t<div class=\"small_select\">
\t\t\t\t\t\t\t\t\t\t<div class=\"input-group input-group-sm\">
\t\t\t\t\t\t\t\t\t\t\t<span class=\"input-group-addon\"> ";
        // line 99
        echo ($context["entry_status"] ?? null);
        echo "</span>
\t\t\t\t\t\t\t\t\t\t\t<select class=\"input-sm\" name=\"select_status\">
\t\t\t\t\t\t\t\t\t\t\t<option value=\"\"> ";
        // line 101
        echo ($context["text_select"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t\t\t<option value=\"1\"> ";
        // line 102
        echo ($context["text_enabled"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t\t\t<option value=\"0\"> ";
        // line 103
        echo ($context["text_disabled"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t<div class=\"small_select\">
\t\t\t\t\t\t\t\t\t\t<div class=\"input-group input-group-sm\">
\t\t\t\t\t\t\t\t\t\t\t<span class=\"input-group-addon\"><i class=\"fa fa-list\"></i> ";
        // line 109
        echo ($context["text_list"] ?? null);
        echo "</span>
\t\t\t\t\t\t\t\t\t\t\t<select class=\"input-sm\" name=\"filter_table\">
\t\t\t\t\t\t\t\t\t\t\t<option value=\"\"> ";
        // line 111
        echo ($context["text_select"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t\t\t";
        // line 112
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["cases"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["case"]) {
            // line 113
            echo "\t\t\t\t\t\t\t\t\t\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["case"], "tablename", [], "any", false, false, false, 113);
            echo "\"> ";
            echo twig_get_attribute($this->env, $this->source, $context["case"], "table", [], "any", false, false, false, 113);
            echo "</option>
\t\t\t\t\t\t\t\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['case'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 114
        echo "\t
\t\t\t\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t\t<div class=\"small_select\">
\t\t\t\t\t\t\t\t\t\t<a class=\"btn btn-danger btn-sm deletecases\"><i class=\"fa fa-trash-o\"></i> </a>
\t\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t\t</td>
\t\t\t\t\t\t\t</tr>
\t\t\t\t\t\t</thead>
\t\t\t\t\t\t<tbody id=\"loadcases\"></tbody>
\t\t\t\t\t</table>
\t\t\t\t</div>
\t\t\t\t<!-- Case List End -->
\t\t\t</div>
\t\t\t<div class=\"tab-pane\" id=\"tab-data\">
\t\t\t\t<div class=\"form-group\">
\t\t\t\t\t<label class=\"col-sm-2 control-label\">";
        // line 131
        echo ($context["text_export"] ?? null);
        echo "</label>
\t\t\t\t\t<div class=\"col-sm-10 tables\">
\t\t\t\t\t\t<div class=\"form-group\">
\t\t\t\t\t\t\t<label class=\"col-sm-6\">";
        // line 134
        echo ($context["text_file_format"] ?? null);
        echo "</label>
\t\t\t\t\t\t\t<label class=\"col-sm-6\">";
        // line 135
        echo ($context["text_fetch_image"] ?? null);
        echo "</label>
\t\t\t\t\t\t\t<div class=\"col-sm-6\">
\t\t\t\t\t\t\t\t<select name=\"file_format\" id=\"file_format\" class=\"selectpicker form-control\">
\t\t\t\t\t\t\t\t\t<option value=\"xls\">";
        // line 138
        echo ($context["text_xls"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t<option value=\"xlsx\">";
        // line 139
        echo ($context["text_xlsx"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t<option value=\"xml\">";
        // line 140
        echo ($context["text_xml"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"col-sm-6\">
\t\t\t\t\t\t\t\t<select name=\"fetch_image\" id=\"fetch_image\" class=\"selectpicker form-control\">
\t\t\t\t\t\t\t\t\t<option value=\"yes\">";
        // line 145
        echo ($context["text_yes"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t\t<option value=\"no\">";
        // line 146
        echo ($context["text_no"] ?? null);
        echo "</option>
\t\t\t\t\t\t\t\t</select>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t</div>
\t\t\t\t\t\t<strong>";
        // line 150
        echo ($context["text_default_table"] ?? null);
        echo "</strong>
\t\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t\t";
        // line 152
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["tables"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["table"]) {
            // line 153
            echo "\t\t\t\t\t\t\t<div class=\"col-sm-4\" style=\"padding-right:0;\">
\t\t\t\t\t\t\t\t<div class=\"checkbox\">
\t\t\t\t\t\t\t\t\t<input type=\"checkbox\" name=\"db_table[]\" id=\"";
            // line 155
            echo twig_get_attribute($this->env, $this->source, $context["table"], "value", [], "any", false, false, false, 155);
            echo "\" class=\"switcher\" value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["table"], "value", [], "any", false, false, false, 155);
            echo "\" data-size=\"mini\">
\t\t\t\t\t\t\t\t\t<label for=\"";
            // line 156
            echo twig_get_attribute($this->env, $this->source, $context["table"], "value", [], "any", false, false, false, 156);
            echo "\"> ";
            echo twig_get_attribute($this->env, $this->source, $context["table"], "name", [], "any", false, false, false, 156);
            echo "</label>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['table'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 160
        echo "\t\t\t\t\t\t</div>
\t\t\t\t\t\t";
        // line 161
        if (($context["customtables"] ?? null)) {
            // line 162
            echo "\t\t\t\t\t\t<br/>
\t\t\t\t\t\t<strong>";
            // line 163
            echo ($context["text_custom_table"] ?? null);
            echo "</strong>
\t\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t\t";
            // line 165
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["customtables"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["customtable"]) {
                // line 166
                echo "\t\t\t\t\t\t\t<div class=\"col-sm-4\" style=\"padding-right:0;\">
\t\t\t\t\t\t\t\t<div class=\"checkbox\">
\t\t\t\t\t\t\t\t\t<input type=\"checkbox\" name=\"db_table[]\" id=\"";
                // line 168
                echo twig_get_attribute($this->env, $this->source, $context["customtable"], "value", [], "any", false, false, false, 168);
                echo "\" class=\"switcher\" value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["customtable"], "value", [], "any", false, false, false, 168);
                echo "\" data-size=\"mini\">
\t\t\t\t\t\t\t\t\t<label for=\"";
                // line 169
                echo twig_get_attribute($this->env, $this->source, $context["customtable"], "value", [], "any", false, false, false, 169);
                echo "\"> ";
                echo twig_get_attribute($this->env, $this->source, $context["customtable"], "name", [], "any", false, false, false, 169);
                echo "</label>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['customtable'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 173
            echo "\t\t\t\t\t\t</div>
\t\t\t\t\t\t";
        }
        // line 175
        echo "\t\t\t\t\t\t<br/>
\t\t\t\t\t\t<div class=\"row\">
\t\t\t\t\t\t\t<div class=\"col-sm-4\">
\t\t\t\t\t\t\t\t<div class=\"btn-group\" data-toggle=\"buttons\">
\t\t\t\t\t\t\t\t\t<a class=\"btn btn-primary select_all\">";
        // line 179
        echo ($context["text_select_all"] ?? null);
        echo "</a>
\t\t\t\t\t\t\t\t\t<a class=\"btn btn-danger unselectall\">";
        // line 180
        echo ($context["text_unselect_all"] ?? null);
        echo "</a>
\t\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t\t<div class=\"col-sm-8\">
\t\t\t\t\t\t\t\t<a class=\"btn btn-primary exportdb pull-right\"><i class=\"fa fa-download\"></i> ";
        // line 184
        echo ($context["text_export"] ?? null);
        echo "</a>
\t\t\t\t\t\t\t</div>
\t\t\t\t\t\t</div>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t<div class=\"form-group\">
\t\t\t\t\t<label class=\"col-sm-2 control-label\">";
        // line 190
        echo ($context["text_upload"] ?? null);
        echo "</label>
\t\t\t\t\t<div class=\"col-sm-10\">
\t\t\t\t\t\t<form method=\"post\" enctype=\"multipart/form-data\" id=\"form-importdb\">
\t\t\t\t\t\t\t<input type=\"file\" name=\"import\" id=\"input-file-now\" class=\"dropify\" data-allowed-file-extensions=\"xls xlsx xml\" />
\t\t\t\t\t\t\t<button type=\"submit\" class=\"btn btn-primary importdb pull-right\"><i class=\"fa fa-upload\"></i> ";
        // line 194
        echo ($context["text_import"] ?? null);
        echo "</button>
\t\t\t\t\t\t</form>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t</div>
\t\t</div>
\t </div>
    </div>
  </div>
</div>
<div class=\"modal fade\" id=\"export\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"export-title\">
  <div class=\"modal-dialog\" role=\"document\">
\t<div class=\"modal-content\">
\t  <div class=\"modal-header\">
\t\t<button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>
\t\t<h4 class=\"modal-title\" id=\"export-title\">";
        // line 210
        echo ($context["text_export_processing"] ?? null);
        echo "</h4>
\t  </div>
\t  <div class=\"modal-body\">
\t\t\t<div class=\"processing\">
\t\t\t\t<div class=\"progress\">
\t\t\t\t  <div id=\"dynamic\" class=\"progress-bar progress-bar-success progress-bar-striped progress-bar-animated active\" role=\"progressbar\" aria-valuenow=\"0\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width: 100%\">
\t\t\t\t\t<span id=\"current-progress\">";
        // line 216
        echo ($context["text_processing"] ?? null);
        echo "</span>
\t\t\t\t  </div>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t<div class=\"error-export hide\">
\t\t\t\t<div class=\"message error-message\">
\t\t\t\t\t<div class=\"check\">✗</div>
\t\t\t\t\t<p>";
        // line 223
        echo ($context["text_incomplete"] ?? null);
        echo "</p>
\t\t\t\t\t<small class=\"error-msg\"></small>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t<div class=\"success-export hide\">
\t\t\t\t<div class=\"message\">
\t\t\t\t<div class=\"check\">✔</div>
\t\t\t\t<p>";
        // line 230
        echo ($context["text_complete"] ?? null);
        echo "</p>
\t\t\t\t<div class=\"row filesize\">
\t\t\t\t\t<div class=\"col-sm-6\">
\t\t\t\t\t\t<p>";
        // line 233
        echo ($context["text_filename"] ?? null);
        echo " <strong class=\"filename\"></strong></p>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"col-sm-6\">
\t\t\t\t\t\t<p>";
        // line 236
        echo ($context["text_filesize"] ?? null);
        echo " <strong class=\"file-size\"></strong></p>
\t\t\t\t\t</div>
\t\t\t\t\t<div class=\"col-sm-12 import_item hide\">
\t\t\t\t\t\t<p>";
        // line 239
        echo ($context["text_create"] ?? null);
        echo " <strong class=\"totalnewitem\"></strong> ";
        echo ($context["text_update"] ?? null);
        echo " <strong class=\"totalupdateitem\"></strong> ";
        echo ($context["text_delete"] ?? null);
        echo " <strong class=\"totaldeleteitem\"></strong></p>
\t\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t<div class=\"updatehtml hide\">
\t\t\t\t<h4>";
        // line 245
        echo ($context["text_log"] ?? null);
        echo "</h4>
\t\t\t\t<div class=\"logs\"></div>
\t\t\t</div>
\t  </div>
\t</div>
  </div>
</div>
<script src=\"view/stylesheet/excelpoint/bootstrap-switch.js\"></script>
<script src=\"view/stylesheet/excelpoint/bootstrap-select.min.js\"></script>
<script src=\"view/javascript/jquery/jquery-ui/jquery-ui.js\"></script>
<script src=\"view/stylesheet/excelpoint/dropify.min.js\"></script>
<script type=\"text/javascript\">
\$(\".switcher\").bootstrapSwitch();
\$(document).ready(function(){
\t\$('.dropify').dropify();
});
</script>
<script type=\"text/javascript\">  
  \$('#loadcases').delegate('.pagination a', 'click', function(e) {
\t\te.preventDefault();
\t\t\$('#loadcases').fadeOut('slow');
\t\t\$('#loadcases').load(this.href);
\t\t\$('#loadcases').fadeIn('slow');
\t});

\tvar url = 'index.php?route=extension/importexport/caselist&user_token=";
        // line 270
        echo ($context["user_token"] ?? null);
        echo "';
\tloadcases(url);
\t
\tfunction loadcases(url) {
\t\$.ajax({
\t\turl: url,
\t\tdataType: 'html',
\t\tbeforeSend: function() {
\t\t\t\$('.loader').removeClass('hide');
\t\t},
\t\tcomplete: function() {
\t\t\t\$('.loader').addClass('hide');
\t\t},
\t\tsuccess: function(html) {
\t\t\t\$('#loadcases').html(html);
\t\t}
\t});
} 

\$('.deletecases').on('click', function() {
\talert('";
        // line 290
        echo ($context["text_confirm"] ?? null);
        echo "');
\t\$.ajax({
\t\turl: 'index.php?route=extension/importexport/deletecase&user_token=";
        // line 292
        echo ($context["user_token"] ?? null);
        echo "',
\t\ttype: 'post',
\t\tdataType: 'json',
\t\tdata: \$('input[name=\\'selected[]\\']:checked'),
\t\tbeforeSend: function() {
\t\t\t\$('.loader').removeClass('hide');
\t\t},
\t\tcomplete: function() {
\t\t\t\$('.loader').addClass('hide');
\t\t},
\t\tsuccess: function(json) {
\t\t\tif(json['error']){
\t\t\t\talert(json['error']);
\t\t\t}else{
\t\t\t\t\$('#loadcases').load('index.php?route=extension/importexport/caselist&user_token=";
        // line 306
        echo ($context["user_token"] ?? null);
        echo "');
\t\t\t}
\t\t}
\t});
\t
});

\$('select[name=\"select_user\"],select[name=\"select_status\"],select[name=\"filter_table\"]').on('change', function() {
\tvar url = '';

\tvar filter_user = \$('select[name=\"select_user\"]').val();

\tif (filter_user) {
\t\turl += '&filter_user=' + encodeURIComponent(filter_user);
\t}

\tvar filter_status = \$('select[name=\\'select_status\\']').val();

\tif (filter_status) {
\t\turl += '&filter_status=' + encodeURIComponent(filter_status);
\t}
\t
\tvar filter_name = \$('input[name=\"search\"]').val();

\tif (filter_name) {
\t\turl += '&filter_name=' + encodeURIComponent(filter_name);
\t}
\t
\tvar filter_table = \$('select[name=\"filter_table\"]').val();

\tif (filter_table) {
\t\turl += '&filter_table=' + encodeURIComponent(filter_table);
\t}
\t
\tloadcases('index.php?route=extension/importexport/caselist&user_token=";
        // line 340
        echo ($context["user_token"] ?? null);
        echo "' + url);
});

\$('.filtertable a,.btn-search').on('click', function() {
\tvar table = \$(this).attr('rel');
\t
\tvar url = '';

\tvar filter_user = \$('select[name=\"select_user\"]').val();

\tif (filter_user) {
\t\turl += '&filter_user=' + encodeURIComponent(filter_user);
\t}

\tvar filter_status = \$('select[name=\\'select_status\\']').val();

\tif (filter_status) {
\t\turl += '&filter_status=' + encodeURIComponent(filter_status);
\t}
\t
\tvar filter_name = \$('input[name=\"search\"]').val();

\tif (filter_name) {
\t\turl += '&filter_name=' + encodeURIComponent(filter_name);
\t}
\t
\tvar filter_table = \$('select[name=\"filter_table\"]').val();

\tif (filter_table) {
\t\turl += '&filter_table=' + encodeURIComponent(filter_table);
\t}
\t
\tloadcases('index.php?route=extension/importexport/caselist&user_token=";
        // line 372
        echo ($context["user_token"] ?? null);
        echo "' + url);
});
</script>
<script type=\"text/javascript\">  
  \$('a.select_all').click(function(event) {
\t\$('.switcher').bootstrapSwitch('state',true);
\t\$('.tables :checkbox').each(function() {
\t\t
\t\tthis.checked = true;                        
\t});
});
\$('a.unselectall').click(function(event) {
\t\$('.switcher').bootstrapSwitch('state',false);
\t\$('.tables :checkbox').each(function() {
\t\tthis.checked = false;                        
\t});
});

\$('.exportdb').on('click', function() {
\t\$('#export-title').text('";
        // line 391
        echo ($context["text_export_processing"] ?? null);
        echo "');
\t\$('.success-export,.error-export,.import_item,.updatehtml').addClass('hide');
\t\$(\"#current-progress\").text('";
        // line 393
        echo ($context["text_processing"] ?? null);
        echo "');
\t\$('.processing .progress-bar').addClass('active');
\t\$('.processing .progress-bar').removeClass('progress-bar-danger');
\t\$('.processing .progress-bar').addClass('progress-bar-success');
\t\$.ajax({
\t\turl: 'index.php?route=extension/importexport/exportdb&user_token=";
        // line 398
        echo ($context["user_token"] ?? null);
        echo "',
\t\ttype: 'post',
\t\tdataType: 'json',
\t\tdata: \$('select[name=\\'file_format\\'],select[name=\\'fetch_image\\'],input[name*=\\'db_table\\']:checked'),
\t\tbeforeSend: function() {
\t\t\t\$('#export').modal('show');
\t\t},
\t\tcomplete: function() {
\t\t\t
\t\t},
\t\tsuccess: function(json) {
\t\t\tif(json['error']){
\t\t        \$('.processing .progress-bar').removeClass('active');
    \t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
    \t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
    \t\t\t\$('.error-export').removeClass('hide');
    \t\t\t\$(\"#current-progress\").text('Process Stop');
    \t\t\t\$('.error-msg').html(json['error']);
\t\t    }
\t\t\t
\t\t\tif(!json['error']){
\t\t        if(json['download']){
    \t\t\t\twindow.location=json['download'];
    \t\t\t}
    \t\t\t
    \t\t\tif(json['success']){
    \t\t\t    \$(\"#dynamic\").css(\"width\",'100%');
    \t\t\t\t\$(\"#current-progress\").text('100%');
    \t\t\t\t\$('.processing .progress-bar').removeClass('active');
    \t\t\t\t\$('.success-export').removeClass('hide');
    \t\t\t\tif(json['filename']){
    \t\t\t\t\t\$('.filename').html(json['filename']);
    \t\t\t\t\t\$('.file-size').html(json['filesize']);
    \t\t\t\t}
    \t\t\t}
\t\t    }
\t\t},
\t\terror: function(xhr, ajaxOptions, thrownError) {
\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
\t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
\t\t\t\$('.error-export').removeClass('hide');
\t\t\t\$(\"#current-progress\").text('";
        // line 440
        echo ($context["text_stop_process"] ?? null);
        echo "');
\t\t\t\$('.error-msg').html(xhr.responseText);
\t\t}
\t});
});

var ajaxLoading = false;
\$(\"form#form-importdb\").submit(function(event){
\tevent.preventDefault();
\t\$('#export').modal('show');
\t\$('#export-title').text('";
        // line 450
        echo ($context["text_import_processing"] ?? null);
        echo "');
\t\$('.success-export,.error-export,.import_item,.updatehtml').addClass('hide');
\t\$(\"#current-progress\").text('";
        // line 452
        echo ($context["text_processing"] ?? null);
        echo "');
\t\$('.processing .progress-bar').addClass('active');
\t\$('.processing .progress-bar').removeClass('progress-bar-danger');
\t\$('.processing .progress-bar').addClass('progress-bar-success');
\tvar formData = new FormData(\$(this)[0]);
\tif(!ajaxLoading) {
        ajaxLoading = true;
\t\t\$.ajax({
\t\t\turl: 'index.php?route=extension/importexport/importdb&user_token=";
        // line 460
        echo ($context["user_token"] ?? null);
        echo "',
\t\t\ttype: 'post',
\t\t\tdata: formData,
\t\t\tdataType: 'json',
\t\t\tasync: false,
\t\t\tcache: false,
\t\t\tcontentType: false,
\t\t\tprocessData: false,
\t\t\tbeforeSend: function() {
\t\t\t\t
\t\t\t},
\t\t\tcomplete: function() {
\t\t\t\t
\t\t\t},
\t\t\tsuccess: function(json){
\t\t\t\tsetTimeout(function () {
\t\t\t\tif(json['error']){
\t\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
\t\t\t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
\t\t\t\t\t\$('.error-export').removeClass('hide');
\t\t\t\t\t\$(\"#current-progress\").text('";
        // line 481
        echo ($context["text_stop_process"] ?? null);
        echo "');
\t\t\t\t\t\$('.error-msg').html(json['error']);
\t\t\t\t\tajaxLoading = false;
\t\t\t\t}
\t\t\t\t
\t\t\t\tif(json['success'] && !json['error']){
\t\t\t\t\tdocument.getElementById('form-importdb').reset();
\t\t\t\t\t\$('.dropify-clear').click();
\t\t\t\t\t\$(\"#dynamic\").css(\"width\",'100%');
\t\t\t\t\t\$(\"#current-progress\").text('100%');
\t\t\t\t\t\$('.success-export').removeClass('hide');
\t\t\t\t\t\$('.success-export .filesize').removeClass('hide');
\t\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\t\tif(json['filename']){
    \t\t\t\t\t\$('.filename').html(json['filename']);
    \t\t\t\t\t\$('.file-size').html(json['filesize']);
    \t\t\t\t}
\t\t\t\t\tajaxLoading = false;
\t\t\t\t}
\t\t\t\t}, 100);
\t\t\t},
\t\t\terror: function(xhr, ajaxOptions, thrownError) {
\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
\t\t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
\t\t\t\t\$('.error-export').removeClass('hide');
\t\t\t\t\$(\"#current-progress\").text('";
        // line 507
        echo ($context["text_stop_process"] ?? null);
        echo "');
\t\t\t\t\$('.error-msg').html(xhr.responseText);
\t\t\t}
\t\t});
\t}
\treturn false;
});
</script>
";
        // line 515
        echo ($context["footer"] ?? null);
    }

    public function getTemplateName()
    {
        return "extension/import_export.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  835 => 515,  824 => 507,  795 => 481,  771 => 460,  760 => 452,  755 => 450,  742 => 440,  697 => 398,  689 => 393,  684 => 391,  662 => 372,  627 => 340,  590 => 306,  573 => 292,  568 => 290,  545 => 270,  517 => 245,  504 => 239,  498 => 236,  492 => 233,  486 => 230,  476 => 223,  466 => 216,  457 => 210,  438 => 194,  431 => 190,  422 => 184,  415 => 180,  411 => 179,  405 => 175,  401 => 173,  389 => 169,  383 => 168,  379 => 166,  375 => 165,  370 => 163,  367 => 162,  365 => 161,  362 => 160,  350 => 156,  344 => 155,  340 => 153,  336 => 152,  331 => 150,  324 => 146,  320 => 145,  312 => 140,  308 => 139,  304 => 138,  298 => 135,  294 => 134,  288 => 131,  269 => 114,  258 => 113,  254 => 112,  250 => 111,  245 => 109,  236 => 103,  232 => 102,  228 => 101,  223 => 99,  215 => 93,  204 => 92,  200 => 91,  196 => 90,  191 => 88,  166 => 66,  162 => 65,  154 => 60,  143 => 52,  139 => 51,  130 => 45,  126 => 44,  117 => 38,  113 => 37,  101 => 27,  93 => 23,  91 => 22,  85 => 18,  74 => 16,  70 => 15,  65 => 13,  59 => 12,  53 => 11,  45 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import_export.twig", "");
    }
}

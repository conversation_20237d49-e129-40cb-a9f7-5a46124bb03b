<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* qb/customer.twig */
class __TwigTemplate_d3c380d1dd51f8afeb5506f2158ccfd7a85e26a63adc412e4a6c3e92962ed1a1 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <div class=\"pull-right\">
        <button type=\"button\" data-toggle=\"tooltip\" title=\"";
        // line 6
        echo ($context["button_filter"] ?? null);
        echo "\" onclick=\"\$('#filter-customer').toggleClass('hidden-sm hidden-xs');\" class=\"btn btn-default hidden-md hidden-lg\"><i class=\"fa fa-filter\"></i></button>
        <button type=\"button\" id=\"btn-import\" data-loading-text = \"";
        // line 7
        echo ($context["text_importing"] ?? null);
        echo "\" class=\"btn btn-default\"><i class=\"fa fa-download\"></i> ";
        echo ($context["text_import"] ?? null);
        echo "</button>
        <button type=\"button\" id=\"btn-export\" data-loading-text = \"";
        // line 8
        echo ($context["text_exporting"] ?? null);
        echo "\" class=\"btn btn-default\"><i class=\"fa fa-upload\"></i> ";
        echo ($context["text_export"] ?? null);
        echo "</button>
      </div>
      <h1>";
        // line 10
        echo ($context["heading_title"] ?? null);
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 12
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 13
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 13);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 13);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 15
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid div_export\">";
        // line 18
        if (($context["error_warning"] ?? null)) {
            // line 19
            echo "    <div class=\"alert alert-danger alert-dismissible\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 23
        echo "    ";
        if (($context["success"] ?? null)) {
            // line 24
            echo "    <div class=\"alert alert-success alert-dismissible\"><i class=\"fa fa-check-circle\"></i> ";
            echo ($context["success"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 28
        echo "    <div class=\"row\">
      <div id=\"filter-customer\" class=\"col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs\">
        <div class=\"panel panel-default\">
          <div class=\"panel-heading\">
            <h3 class=\"panel-title\"><i class=\"fa fa-filter\"></i> ";
        // line 32
        echo ($context["text_filter"] ?? null);
        echo "</h3>
          </div>
          <div class=\"panel-body\">
            <div class=\"form-group\">
              <label class=\"control-label\" for=\"input-customer_id\">";
        // line 36
        echo ($context["entry_customer_id"] ?? null);
        echo "</label>
              <input type=\"text\" name=\"filter_customer_id\" value=\"";
        // line 37
        echo ($context["filter_customer_id"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_customer_id"] ?? null);
        echo "\" id=\"input-customer_id\" class=\"form-control\" />
            </div>
            <div class=\"form-group\">
              <label class=\"control-label\" for=\"input-qb_customer_id\">";
        // line 40
        echo ($context["entry_qb_customer_id"] ?? null);
        echo "</label>
              <input type=\"text\" name=\"filter_qb_customer_id\" value=\"";
        // line 41
        echo ($context["filter_qb_customer_id"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_qb_customer_id"] ?? null);
        echo "\" id=\"input-qb_customer_id\" class=\"form-control\" />
            </div>
            <div class=\"form-group\">
              <label class=\"control-label\" for=\"input-name\">";
        // line 44
        echo ($context["entry_name"] ?? null);
        echo "</label>
              <input type=\"text\" name=\"filter_name\" value=\"";
        // line 45
        echo ($context["filter_name"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_name"] ?? null);
        echo "\" id=\"input-name\" class=\"form-control\" />
            </div>
            <div class=\"form-group\">
              <label class=\"control-label\" for=\"input-email\">";
        // line 48
        echo ($context["entry_email"] ?? null);
        echo "</label>
              <input type=\"text\" name=\"filter_email\" value=\"";
        // line 49
        echo ($context["filter_email"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_email"] ?? null);
        echo "\" id=\"input-email\" class=\"form-control\" />
            </div>
            <div class=\"form-group\">
              <label class=\"control-label\" for=\"input-status\">";
        // line 52
        echo ($context["entry_status"] ?? null);
        echo "</label>
              <select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
                <option value=\"\"></option>
                ";
        // line 55
        if ((($context["filter_status"] ?? null) == "1")) {
            // line 56
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 58
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                ";
        }
        // line 60
        echo "                ";
        if ((($context["filter_status"] ?? null) == "0")) {
            // line 61
            echo "                <option value=\"0\" selected=\"selected\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 63
            echo "                <option value=\"0\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 65
        echo "              </select>
            </div>
            <div class=\"form-group text-right\">
              <button type=\"button\" id=\"button-filter\" class=\"btn btn-default\"><i class=\"fa fa-filter\"></i> ";
        // line 68
        echo ($context["button_filter"] ?? null);
        echo "</button>
            </div>
          </div>
        </div>
      </div>
      <div class=\"col-md-9 col-md-pull-3 col-sm-12\">
        <div class=\"panel panel-default\">
          <div class=\"panel-heading\">
            <h3 class=\"panel-title\"><i class=\"fa fa-list\"></i> ";
        // line 76
        echo ($context["text_list"] ?? null);
        echo "</h3>
          </div>
          <div class=\"panel-body\">
            <div class=\"table-responsive\">
              <form action=\"\" method=\"post\" enctype=\"multipart/form-data\" id=\"form-customer\">
                <table class=\"table table-bordered table-hover\">
                  <thead>
                    <tr>
                      <td style=\"width: 1px;\" class=\"text-center\"><input type=\"checkbox\" onclick=\"\$('input[name*=\\'selected\\']').prop('checked', this.checked);\" /></td>
                      <td class=\"text-left\">";
        // line 85
        echo ($context["column_customer_id"] ?? null);
        echo "</td>
                      <td class=\"text-left\">";
        // line 86
        echo ($context["column_qb_customer_id"] ?? null);
        echo "</td>
                      <td class=\"text-left\">";
        // line 87
        if ((($context["sort"] ?? null) == "name")) {
            echo "<a href=\"";
            echo ($context["sort_name"] ?? null);
            echo "\" class=\"";
            echo ($context["order"] ?? null);
            echo "\">";
            echo ($context["column_name"] ?? null);
            echo "</a>";
        } else {
            echo "<a href=\"";
            echo ($context["sort_name"] ?? null);
            echo "\">";
            echo ($context["column_name"] ?? null);
            echo "</a>";
        }
        echo "</td>
                      <td class=\"text-left\">";
        // line 88
        if ((($context["sort"] ?? null) == "c.email")) {
            echo "<a href=\"";
            echo ($context["sort_email"] ?? null);
            echo "\" class=\"";
            echo ($context["order"] ?? null);
            echo "\">";
            echo ($context["column_email"] ?? null);
            echo "</a>";
        } else {
            echo "<a href=\"";
            echo ($context["sort_email"] ?? null);
            echo "\">";
            echo ($context["column_email"] ?? null);
            echo "</a>";
        }
        echo "</td>
                      <td class=\"text-left\">";
        // line 89
        if ((($context["sort"] ?? null) == "c.status")) {
            echo "<a href=\"";
            echo ($context["sort_status"] ?? null);
            echo "\" class=\"";
            echo ($context["order"] ?? null);
            echo "\">";
            echo ($context["column_status"] ?? null);
            echo "</a>";
        } else {
            echo "<a href=\"";
            echo ($context["sort_status"] ?? null);
            echo "\">";
            echo ($context["column_status"] ?? null);
            echo "</a>";
        }
        echo "</td>
                    </tr>
                  </thead>
                  <tbody>
                  ";
        // line 93
        if (($context["customers"] ?? null)) {
            // line 94
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["customers"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["customer"]) {
                // line 95
                echo "                  <tr>
                    <td class=\"text-center\">";
                // line 96
                if (twig_in_filter(twig_get_attribute($this->env, $this->source, $context["customer"], "customer_id", [], "any", false, false, false, 96), ($context["selected"] ?? null))) {
                    // line 97
                    echo "                      <input type=\"checkbox\" name=\"selected[]\" value=\"";
                    echo twig_get_attribute($this->env, $this->source, $context["customer"], "customer_id", [], "any", false, false, false, 97);
                    echo "\" checked=\"checked\" />
                      ";
                } else {
                    // line 99
                    echo "                      <input type=\"checkbox\" name=\"selected[]\" value=\"";
                    echo twig_get_attribute($this->env, $this->source, $context["customer"], "customer_id", [], "any", false, false, false, 99);
                    echo "\" />
                      ";
                }
                // line 101
                echo "                    </td>
                    <td class=\"text-left\">";
                // line 102
                echo twig_get_attribute($this->env, $this->source, $context["customer"], "customer_id", [], "any", false, false, false, 102);
                echo "</td>
                    <td class=\"text-left\">";
                // line 103
                echo twig_get_attribute($this->env, $this->source, $context["customer"], "qb_customer_id", [], "any", false, false, false, 103);
                echo "</td>
                    <td class=\"text-left\">";
                // line 104
                echo twig_get_attribute($this->env, $this->source, $context["customer"], "name", [], "any", false, false, false, 104);
                echo "</td>
                    <td class=\"text-left\">";
                // line 105
                echo twig_get_attribute($this->env, $this->source, $context["customer"], "email", [], "any", false, false, false, 105);
                echo "</td>
                    <td class=\"text-left\">";
                // line 106
                echo twig_get_attribute($this->env, $this->source, $context["customer"], "status", [], "any", false, false, false, 106);
                echo "</td>
                  </tr>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['customer'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 109
            echo "                  ";
        } else {
            // line 110
            echo "                  <tr>
                    <td class=\"text-center\" colspan=\"6\">";
            // line 111
            echo ($context["text_no_results"] ?? null);
            echo "</td>
                  </tr>
                  ";
        }
        // line 114
        echo "                    </tbody>
                </table>
              </div>
            </form>
            <div class=\"row\">
              <div class=\"col-sm-6 text-left\">";
        // line 119
        echo ($context["pagination"] ?? null);
        echo "</div>
              <div class=\"col-sm-6 text-right\">";
        // line 120
        echo ($context["results"] ?? null);
        echo "</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type=\"text/javascript\"><!--
  \$('.table-responsive').on('shown.bs.dropdown', function (e) {
    var t = \$(this),
      m = \$(e.target).find('.dropdown-menu'),
      tb = t.offset().top + t.height(),
      mb = m.offset().top + m.outerHeight(true),
      d = 20;
    if (t[0].scrollWidth > t.innerWidth()) {
      if (mb + d > tb) {
        t.css('padding-bottom', ((mb + d) - tb));
      }
    } else {
      t.css('overflow', 'visible');
    }
  }).on('hidden.bs.dropdown', function () {
    \$(this).css({'padding-bottom': '', 'overflow': ''});
  });
  //--></script>
  <script type=\"text/javascript\"><!--
  \$('#button-filter').on('click', function() {
    url = 'index.php?route=qb/customer&user_token=";
        // line 147
        echo ($context["user_token"] ?? null);
        echo "';
    var filter_customer_id = \$('input[name=\\'filter_customer_id\\']').val();
    if (filter_customer_id) {
      url += '&filter_customer_id=' + encodeURIComponent(filter_customer_id);
    }
    var filter_qb_customer_id = \$('input[name=\\'filter_qb_customer_id\\']').val();
    if (filter_qb_customer_id) {
      url += '&filter_qb_customer_id=' + encodeURIComponent(filter_qb_customer_id);
    }
    var filter_name = \$('input[name=\\'filter_name\\']').val();
    if (filter_name) {
      url += '&filter_name=' + encodeURIComponent(filter_name);
    }
    var filter_email = \$('input[name=\\'filter_email\\']').val();
    if (filter_email) {
      url += '&filter_email=' + encodeURIComponent(filter_email);
    }
    var filter_status = \$('select[name=\\'filter_status\\']').val();
    if (filter_status !== '') {
      url += '&filter_status=' + encodeURIComponent(filter_status);
    }
    location = url;
  });
  //--></script>
  <script type=\"text/javascript\"><!--
  \$('input[name=\\'filter_name\\']').autocomplete({
    'source': function(request, response) {
      \$.ajax({
        url: 'index.php?route=qb/customer/autocomplete&user_token=";
        // line 175
        echo ($context["user_token"] ?? null);
        echo "&filter_name=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response(\$.map(json, function(item) {
            return {
              label: item['name'],
              value: item['customer_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      \$('input[name=\\'filter_name\\']').val(item['label']);
    }
  });
  \$('input[name=\\'filter_email\\']').autocomplete({
    'source': function(request, response) {
      \$.ajax({
        url: 'index.php?route=qb/customer/autocomplete&user_token=";
        // line 194
        echo ($context["user_token"] ?? null);
        echo "&filter_email=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response(\$.map(json, function(item) {
            return {
              label: item['email'],
              value: item['customer_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      \$('input[name=\\'filter_email\\']').val(item['label']);
    }
  });
  //--></script>
  <script type=\"text/javascript\"><!--
  \$('#btn-export').on('click', function() {
    \$.ajax({
      url: 'index.php?route=qb/customer/export&user_token=";
        // line 214
        echo ($context["user_token"] ?? null);
        echo "',
      type: 'post',
      dataType: 'json',
      beforeSend: function() {
        \$('#btn-export').button('loading');
        \$(\"button\").attr(\"disabled\", \"disabled\");
      },
      complete: function() {
        \$('#btn-export').button('reset');
        \$(\"button\").attr(\"disabled\", false);
      },
      success: function(json) {
        \$('.alert-dismissible').remove();

        if (json['success']) {
          \$('.div_export').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa fa-check-circle\"></i> ' + json['success'] + ' <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button></div>');
        }

        setTimeout(function(){
           window.location.reload(1);
        }, 5000);
      }
    });
  });

  \$('#btn-import').on('click', function() {
    \$.ajax({
      url: 'index.php?route=qb/customer/import&user_token=";
        // line 241
        echo ($context["user_token"] ?? null);
        echo "',
      type: 'post',
      dataType: 'json',
      beforeSend: function() {
        \$('#btn-import').button('loading');
        \$(\"button\").attr(\"disabled\", \"disabled\");
      },
      complete: function() {
        \$('#btn-import').button('reset');
        \$(\"button\").attr(\"disabled\", false);
      },
      success: function(json) {
        \$('.alert-dismissible').remove();

        if (json['success']) {
          \$('.div_export').prepend('<div class=\"alert alert-success alert-dismissible\"><i class=\"fa fa-check-circle\"></i> ' + json['success'] + ' <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button></div>');
        }

        setTimeout(function(){
           window.location.reload(1);
        }, 5000);
      }
    });
  });
  //--></script>
</div>
";
        // line 267
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "qb/customer.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  538 => 267,  509 => 241,  479 => 214,  456 => 194,  434 => 175,  403 => 147,  373 => 120,  369 => 119,  362 => 114,  356 => 111,  353 => 110,  350 => 109,  341 => 106,  337 => 105,  333 => 104,  329 => 103,  325 => 102,  322 => 101,  316 => 99,  310 => 97,  308 => 96,  305 => 95,  300 => 94,  298 => 93,  277 => 89,  259 => 88,  241 => 87,  237 => 86,  233 => 85,  221 => 76,  210 => 68,  205 => 65,  199 => 63,  193 => 61,  190 => 60,  184 => 58,  178 => 56,  176 => 55,  170 => 52,  162 => 49,  158 => 48,  150 => 45,  146 => 44,  138 => 41,  134 => 40,  126 => 37,  122 => 36,  115 => 32,  109 => 28,  101 => 24,  98 => 23,  90 => 19,  88 => 18,  83 => 15,  72 => 13,  68 => 12,  63 => 10,  56 => 8,  50 => 7,  46 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "qb/customer.twig", "");
    }
}

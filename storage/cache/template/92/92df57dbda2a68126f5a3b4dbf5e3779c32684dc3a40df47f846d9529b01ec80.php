<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* journal3/template/journal3/module/form_email.twig */
class __TwigTemplate_1410d7ab74ac4b468b665d45f2cc482e37325f5fba56ea307140da3f15f8a9cb extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01//EN\" \"http://www.w3.org/TR/1999/REC-html401-19991224/strict.dtd\">
<html>
<head>
  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">
  <title>";
        // line 5
        echo ($context["title"] ?? null);
        echo "</title>
</head>
<body style=\"font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #000000;\">
<div style=\"width: 680px;\">
  ";
        // line 9
        if (($context["logo"] ?? null)) {
            // line 10
            echo "  <a href=\"";
            echo ($context["store_url"] ?? null);
            echo "\" title=\"";
            echo ($context["store_name"] ?? null);
            echo "\"><img src=\"";
            echo ($context["logo"] ?? null);
            echo "\" alt=\"";
            echo ($context["store_name"] ?? null);
            echo "\" style=\"margin-bottom: 20px; border: none;\"/></a>
  ";
        }
        // line 12
        echo "  <p style=\"margin-top: 0px; margin-bottom: 20px;\">";
        echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailTitle", [], "any", false, false, false, 12);
        echo "</p>
  <table style=\"border-collapse: collapse; width: 100%; border-top: 1px solid #DDDDDD; border-left: 1px solid #DDDDDD; margin-bottom: 20px;\">
    <thead>
    <tr>
      <td style=\"font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;\">";
        // line 16
        echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailField", [], "any", false, false, false, 16);
        echo "</td>
      <td style=\"font-size: 12px; border-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; background-color: #EFEFEF; font-weight: bold; text-align: left; padding: 7px; color: #222222;\">";
        // line 17
        echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailValue", [], "any", false, false, false, 17);
        echo "</td>
    </tr>
    </thead>
    <tbody>
    ";
        // line 21
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "items", [], "any", false, false, false, 21));
        foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
            // line 22
            echo "      <tr>
        <td style=\"font-size: 12px;\tborder-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: right; padding: 7px;\"><b>";
            // line 23
            echo twig_get_attribute($this->env, $this->source, $context["item"], "label", [], "any", false, false, false, 23);
            echo ":</b></td>
        <td style=\"font-size: 12px;\tborder-right: 1px solid #DDDDDD; border-bottom: 1px solid #DDDDDD; text-align: left; padding: 7px;\">
          ";
            // line 25
            if ((twig_get_attribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 25) == "file")) {
                // line 26
                echo "            <a href=\"";
                echo twig_get_attribute($this->env, $this->source, $context["item"], "url", [], "any", false, false, false, 26);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["item"], "value", [], "any", false, false, false, 26);
                echo "</a>
          ";
            } else {
                // line 28
                echo "            ";
                echo ((twig_test_iterable(twig_get_attribute($this->env, $this->source, $context["item"], "value", [], "any", false, false, false, 28))) ? (twig_join_filter(twig_get_attribute($this->env, $this->source, $context["item"], "value", [], "any", false, false, false, 28), ", ")) : (twig_get_attribute($this->env, $this->source, $context["item"], "value", [], "any", false, false, false, 28)));
                echo "
          ";
            }
            // line 30
            echo "        </td>
      </tr>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['item'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 33
        echo "    </tbody>
  </table>
  ";
        // line 35
        if (twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "title", [], "any", false, false, false, 35)) {
            // line 36
            echo "  <p style=\"margin-top: 10px; margin-bottom: 20px;\">";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailUsingModule", [], "any", false, false, false, 36);
            echo " <b>";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "title", [], "any", false, false, false, 36);
            echo "</b></p>
  ";
        }
        // line 38
        echo "  ";
        if (twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "url", [], "any", false, false, false, 38)) {
            // line 39
            echo "  <p style=\"margin-top: 10px; margin-bottom: 20px;\">";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailFrom", [], "any", false, false, false, 39);
            echo " <a href=\"";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "url", [], "any", false, false, false, 39);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "url", [], "any", false, false, false, 39);
            echo "</a></p>
  ";
        }
        // line 41
        echo "  ";
        if (twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ip", [], "any", false, false, false, 41)) {
            // line 42
            echo "    <p style=\"margin-top: 10px; margin-bottom: 20px;\">";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "sentEmailIPAddress", [], "any", false, false, false, 42);
            echo " <b>";
            echo twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ip", [], "any", false, false, false, 42);
            echo "</b></p>
  ";
        }
        // line 44
        echo "</div>
</body>
</html>
";
    }

    public function getTemplateName()
    {
        return "journal3/template/journal3/module/form_email.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  157 => 44,  149 => 42,  146 => 41,  136 => 39,  133 => 38,  125 => 36,  123 => 35,  119 => 33,  111 => 30,  105 => 28,  97 => 26,  95 => 25,  90 => 23,  87 => 22,  83 => 21,  76 => 17,  72 => 16,  64 => 12,  52 => 10,  50 => 9,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "journal3/template/journal3/module/form_email.twig", "");
    }
}

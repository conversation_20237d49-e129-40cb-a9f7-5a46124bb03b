<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/excelport/tab_export.twig */
class __TwigTemplate_74fe499db23b33b13efb4a79e18850adc573f0216f46fc5fabc847f877925639 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if (($context["openstock_installed"] ?? null)) {
            // line 2
            echo "<div class=\"alert alert-success\">";
            echo ($context["openstock_installed"] ?? null);
            echo "</div>
";
        }
        // line 4
        echo "<table class=\"form\">
    <tr>
        <td>
            <div class=\"question\">";
        // line 7
        echo ($context["text_question_data"] ?? null);
        echo " <a id=\"filter_popover\" rel=\"popover\" data-toggle=\"popover\" data-content=\"";
        echo ($context["text_toggle_filter"] ?? null);
        echo "\" class=\"btn btn-default btn ";
        echo ((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 7), "Export", [], "any", false, false, false, 7), "Filter", [], "any", false, false, false, 7)) ? ("active") : (""));
        echo "\"><i class=\"fa fa-filter\"></i></a></div>
            <input type=\"hidden\" id=\"toggle_filter\" name=\"ExcelPort[Export][Filter]\" value=\"";
        // line 8
        echo ((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 8), "Export", [], "any", false, false, false, 8), "Filter", [], "any", false, false, false, 8)) ? ("1") : ("0"));
        echo "\">
            <div class=\"option\">
            \t<input id=\"DataTypeProductsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Products\" ";
        // line 10
        echo ((( !twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 10), "Export", [], "any", false, false, false, 10), "DataType", [], "any", false, false, false, 10) || (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 10), "Export", [], "any", false, false, false, 10), "DataType", [], "any", false, false, false, 10) == "Products"))) ? ("checked") : (""));
        echo " /><label for=\"DataTypeProductsOption\">";
        echo ($context["text_datatype_option_products"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Products\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 17
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Products][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 19
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 19), "Export", [], "any", false, false, false, 19), "Filters", [], "any", false, false, false, 19), "Products", [], "any", false, false, false, 19), "Conjunction", [], "any", false, false, false, 19) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 20
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 30
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeCategoriesOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Categories\" ";
        // line 36
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 36), "Export", [], "any", false, false, false, 36), "DataType", [], "any", false, false, false, 36) == "Categories")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeCategoriesOption\">";
        echo ($context["text_datatype_option_categories"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Categories\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 43
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Categories][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 45
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 45), "Export", [], "any", false, false, false, 45), "Filters", [], "any", false, false, false, 45), "Categories", [], "any", false, false, false, 45), "Conjunction", [], "any", false, false, false, 45) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 46
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 56
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeOptionsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Options\" ";
        // line 62
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 62), "Export", [], "any", false, false, false, 62), "DataType", [], "any", false, false, false, 62) == "Options")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeOptionsOption\">";
        echo ($context["text_datatype_option_options"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Options\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 69
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Options][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 71
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 71), "Export", [], "any", false, false, false, 71), "Filters", [], "any", false, false, false, 71), "Options", [], "any", false, false, false, 71), "Conjunction", [], "any", false, false, false, 71) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 72
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 82
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeAttributesOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Attributes\" ";
        // line 88
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 88), "Export", [], "any", false, false, false, 88), "DataType", [], "any", false, false, false, 88) == "Attributes")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeAttributesOption\">";
        echo ($context["text_datatype_option_attributes"] ?? null);
        echo "</label>
            </div>
        \t<div class=\"dataTypeFilter\" data-type=\"Attributes\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 95
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Attributes][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 97
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 97), "Export", [], "any", false, false, false, 97), "Filters", [], "any", false, false, false, 97), "Attributes", [], "any", false, false, false, 97), "Conjunction", [], "any", false, false, false, 97) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 98
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 108
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
                <input id=\"DataTypeFiltersOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Filters\" ";
        // line 114
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 114), "Export", [], "any", false, false, false, 114), "DataType", [], "any", false, false, false, 114) == "Filters")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeFiltersOption\">";
        echo ($context["text_datatype_option_filters"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Filters\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 121
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Filters][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 123
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 123), "Export", [], "any", false, false, false, 123), "Filters", [], "any", false, false, false, 123), "Filters", [], "any", false, false, false, 123), "Conjunction", [], "any", false, false, false, 123) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 124
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 134
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeCustomersOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Customers\" ";
        // line 140
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 140), "Export", [], "any", false, false, false, 140), "DataType", [], "any", false, false, false, 140) == "Customers")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeCustomersOption\">";
        echo ($context["text_datatype_option_customers"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Customers\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 147
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Customers][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 149
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 149), "Export", [], "any", false, false, false, 149), "Filters", [], "any", false, false, false, 149), "Customers", [], "any", false, false, false, 149), "Conjunction", [], "any", false, false, false, 149) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 150
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 160
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeCustomerGroupsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"CustomerGroups\" ";
        // line 166
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 166), "Export", [], "any", false, false, false, 166), "DataType", [], "any", false, false, false, 166) == "CustomerGroups")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeCustomerGroupsOption\">";
        echo ($context["text_datatype_option_customer_groups"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"CustomerGroups\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 173
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][CustomerGroups][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 175
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 175), "Export", [], "any", false, false, false, 175), "Filters", [], "any", false, false, false, 175), "CustomerGroups", [], "any", false, false, false, 175), "Conjunction", [], "any", false, false, false, 175) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 176
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 186
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
            \t<input id=\"DataTypeOrdersOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Orders\" ";
        // line 192
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 192), "Export", [], "any", false, false, false, 192), "DataType", [], "any", false, false, false, 192) == "Orders")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeOrdersOption\">";
        echo ($context["text_datatype_option_orders"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Orders\">
            \t<table>
                \t<thead>
                    \t<tr>
                        \t<td colspan=\"2\">
                            \t";
        // line 199
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Orders][Conjunction]\" class=\"conjunctionSelect\">
                                \t<option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 201
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 201), "Export", [], "any", false, false, false, 201), "Filters", [], "any", false, false, false, 201), "Orders", [], "any", false, false, false, 201), "Conjunction", [], "any", false, false, false, 201) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                            \t</select> <a data-toggle=\"tooltip\" title=\"";
        // line 202
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                \t<tbody>
                    \t
                    </tbody>
                    <tfoot>
                    \t<tr>
                        \t<td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 212
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
                <input id=\"DataTypeOrderStatusesOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"OrderStatuses\" ";
        // line 218
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 218), "Export", [], "any", false, false, false, 218), "DataType", [], "any", false, false, false, 218) == "OrderStatuses")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeOrderStatusesOption\">";
        echo ($context["text_datatype_option_order_statuses"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"OrderStatuses\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 225
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][OrderStatuses][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 227
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 227), "Export", [], "any", false, false, false, 227), "Filters", [], "any", false, false, false, 227), "OrderStatuses", [], "any", false, false, false, 227), "Conjunction", [], "any", false, false, false, 227) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 228
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 238
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class=\"option\">
                <input id=\"DataTypeManufacturersOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Manufacturers\" ";
        // line 244
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 244), "Export", [], "any", false, false, false, 244), "DataType", [], "any", false, false, false, 244) == "Manufacturers")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeManufacturersOption\">";
        echo ($context["text_datatype_option_manufacturers"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Manufacturers\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 251
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Manufacturers][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 253
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 253), "Export", [], "any", false, false, false, 253), "Filters", [], "any", false, false, false, 253), "Manufacturers", [], "any", false, false, false, 253), "Conjunction", [], "any", false, false, false, 253) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 254
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 264
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class=\"option\">
                <input id=\"DataTypeCouponsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Coupons\" ";
        // line 271
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 271), "Export", [], "any", false, false, false, 271), "DataType", [], "any", false, false, false, 271) == "Coupons")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeCouponsOption\">";
        echo ($context["text_datatype_option_coupons"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Coupons\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 278
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Coupons][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 280
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 280), "Export", [], "any", false, false, false, 280), "Filters", [], "any", false, false, false, 280), "Coupons", [], "any", false, false, false, 280), "Conjunction", [], "any", false, false, false, 280) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 281
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 291
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class=\"option\">
                <input id=\"DataTypeVouchersOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Vouchers\" ";
        // line 298
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 298), "Export", [], "any", false, false, false, 298), "DataType", [], "any", false, false, false, 298) == "Vouchers")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeVouchersOption\">";
        echo ($context["text_datatype_option_vouchers"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Vouchers\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 305
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Vouchers][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 307
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 307), "Export", [], "any", false, false, false, 307), "Filters", [], "any", false, false, false, 307), "Vouchers", [], "any", false, false, false, 307), "Conjunction", [], "any", false, false, false, 307) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 308
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 318
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class=\"option\">
                <input id=\"DataTypeDownloadsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Downloads\" ";
        // line 325
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 325), "Export", [], "any", false, false, false, 325), "DataType", [], "any", false, false, false, 325) == "Downloads")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeDownloadsOption\">";
        echo ($context["text_datatype_option_downloads"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Downloads\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 332
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Downloads][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 334
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 334), "Export", [], "any", false, false, false, 334), "Filters", [], "any", false, false, false, 334), "Downloads", [], "any", false, false, false, 334), "Conjunction", [], "any", false, false, false, 334) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 335
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 345
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class=\"option\">
                <input id=\"DataTypeReviewsOption\" type=\"radio\" name=\"ExcelPort[Export][DataType]\" value=\"Reviews\" ";
        // line 352
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 352), "Export", [], "any", false, false, false, 352), "DataType", [], "any", false, false, false, 352) == "Reviews")) ? ("checked") : (""));
        echo " /><label for=\"DataTypeReviewsOption\">";
        echo ($context["text_datatype_option_reviews"] ?? null);
        echo "</label>
            </div>
            <div class=\"dataTypeFilter\" data-type=\"Reviews\">
                <table>
                    <thead>
                        <tr>
                            <td colspan=\"2\">
                                ";
        // line 359
        echo ($context["text_conjunction"] ?? null);
        echo " <select name=\"ExcelPort[Export][Filters][Reviews][Conjunction]\" class=\"conjunctionSelect\">
                                    <option value=\"OR\">OR</option>
                                    <option value=\"AND\" ";
        // line 361
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 361), "Export", [], "any", false, false, false, 361), "Filters", [], "any", false, false, false, 361), "Reviews", [], "any", false, false, false, 361), "Conjunction", [], "any", false, false, false, 361) == "AND")) ? ("selected") : (""));
        echo ">AND</option>
                                </select> <a data-toggle=\"tooltip\" title=\"";
        // line 362
        echo ($context["help_conjunction"] ?? null);
        echo "\"><i class=\"fa fa-info-circle fw icon-info-sign\"></i></a>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td class=\"right\"><a class=\"btn btn-success addCondition\"><i class=\"icon-plus icon-white\"></i> ";
        // line 372
        echo ($context["button_add_condition"] ?? null);
        echo "</a></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <div data-depends-on=\"#DataTypeProductsOption, #DataTypeCategoriesOption, #DataTypeCustomersOption, #DataTypeManufacturersOption\" class=\"question\">";
        // line 378
        echo ($context["text_question_store"] ?? null);
        echo "</div>
            <div data-depends-on=\"#DataTypeProductsOption, #DataTypeCategoriesOption, #DataTypeCustomersOption, #DataTypeManufacturersOption\" class=\"option\">
            \t<input id=\"Store0Option\" type=\"radio\" name=\"ExcelPort[Export][Store]\" value=\"0\" ";
        // line 380
        echo (( !twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 380), "Export", [], "any", false, false, false, 380), "Store", [], "any", false, false, false, 380)) ? ("checked") : (""));
        echo " /><label for=\"Store0Option\">";
        echo ($context["default_store_name"] ?? null);
        echo "</label>
            </div>
            ";
        // line 382
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 383
            echo "            <div data-depends-on=\"#DataTypeProductsOption, #DataTypeCategoriesOption, #DataTypeCustomersOption, #DataTypeManufacturersOption\" class=\"option\">
            \t<input id=\"Store";
            // line 384
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 384);
            echo "Option\" type=\"radio\" name=\"ExcelPort[Export][Store]\" value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 384);
            echo "\" ";
            echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 384), "Export", [], "any", false, false, false, 384), "Store", [], "any", false, false, false, 384) == twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 384))) ? ("checked") : (""));
            echo "/><label for=\"Store";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 384);
            echo "Option\">";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "name", [], "any", false, false, false, 384);
            echo "</label>
            </div>
            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 387
        echo "            
            <div data-depends-on=\"#DataTypeProductsOption, #DataTypeCategoriesOption, #DataTypeOptionsOption, #DataTypeAttributesOption, #DataTypeFiltersOption, #DataTypeCustomersOption, #DataTypeCustomerGroupsOption, #DataTypeManufacturersOption, #DataTypeDownloadsOption, #DataTypeOrderStatusesOption\" class=\"question\">";
        // line 388
        echo ($context["text_question_language"] ?? null);
        echo "</div>

            ";
        // line 390
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        $context['loop'] = [
          'parent' => $context['_parent'],
          'index0' => 0,
          'index'  => 1,
          'first'  => true,
        ];
        if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
            $length = count($context['_seq']);
            $context['loop']['revindex0'] = $length - 1;
            $context['loop']['revindex'] = $length;
            $context['loop']['length'] = $length;
            $context['loop']['last'] = 1 === $length;
        }
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 391
            echo "            <div data-depends-on=\"#DataTypeProductsOption, #DataTypeCategoriesOption, #DataTypeOptionsOption, #DataTypeAttributesOption, #DataTypeFiltersOption, #DataTypeCustomersOption, #DataTypeCustomerGroupsOption, #DataTypeManufacturersOption, #DataTypeDownloadsOption, #DataTypeOrderStatusesOption\" class=\"option\">
            \t<input id=\"Language";
            // line 392
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 392);
            echo "Option\" type=\"radio\" name=\"ExcelPort[Export][Language]\" value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 392);
            echo "\" ";
            echo (((( !twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 392), "Export", [], "any", false, false, false, 392), "Language", [], "any", false, false, false, 392) && (twig_get_attribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 392) == 0)) || (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 392), "Export", [], "any", false, false, false, 392), "Language", [], "any", false, false, false, 392) == twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 392)))) ? ("checked") : (""));
            echo " /><label for=\"Language";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 392);
            echo "Option\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 392);
            echo "</label>
            </div>
            ";
            ++$context['loop']['index0'];
            ++$context['loop']['index'];
            $context['loop']['first'] = false;
            if (isset($context['loop']['length'])) {
                --$context['loop']['revindex0'];
                --$context['loop']['revindex'];
                $context['loop']['last'] = 0 === $context['loop']['revindex0'];
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 395
        echo "            
            <div class=\"question\" data-depends-on=\"#DataTypeProductsOption\">
                <p>";
        // line 397
        echo ($context["text_question_product_type"] ?? null);
        echo "</p>
            </div>
            <div class=\"question\" data-depends-on=\"#DataTypeProductsOption\">
                <input type=\"radio\" name=\"ExcelPort[Export][ProductExportMode]\" ";
        // line 400
        echo ((( !twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 400), "Export", [], "any", false, false, false, 400), "ProductExportMode", [], "any", false, false, false, 400) || (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 400), "Export", [], "any", false, false, false, 400), "ProductExportMode", [], "any", false, false, false, 400) == "0"))) ? ("checked") : (""));
        echo " value=\"0\" id=\"radioBulkExport\" /><label for=\"radioBulkExport\">";
        echo ($context["text_question_product_type_bulk"] ?? null);
        echo "</label>
            </div>
            <div class=\"question\" data-depends-on=\"#DataTypeProductsOption\">
                <input type=\"radio\" name=\"ExcelPort[Export][ProductExportMode]\" ";
        // line 403
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 403), "Export", [], "any", false, false, false, 403), "ProductExportMode", [], "any", false, false, false, 403) == "2")) ? ("checked") : (""));
        echo " value=\"2\" id=\"radioFullExport\" /><label for=\"radioFullExport\">";
        echo ($context["text_question_product_type_full"] ?? null);
        echo "</label>
            </div>
            <div class=\"question\" data-depends-on=\"#DataTypeProductsOption\">
                <input type=\"radio\" name=\"ExcelPort[Export][ProductExportMode]\" ";
        // line 406
        echo (((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 406), "Export", [], "any", false, false, false, 406), "ProductExportMode", [], "any", false, false, false, 406) == "1")) ? ("checked") : (""));
        echo " value=\"1\" id=\"radioQuickExport\" /><label for=\"radioQuickExport\">";
        echo ($context["text_question_product_type_quick"] ?? null);
        echo "</label>
            </div>
        </td>  
    </tr>
    <tr>
        <td>
        \t<div>
        \t\t<a data-action=\"export\" class=\"btn btn-success continueAction ExcelPortSubmitButton\">";
        // line 413
        echo ($context["button_export"] ?? null);
        echo "</a>
            </div>
\t\t\t<div class=\"help\"><strong>";
        // line 415
        echo ($context["text_note"] ?? null);
        echo "</strong> ";
        echo ($context["text_supported_in_oc1541"] ?? null);
        echo " <a class='needMoreSize' href=\"javascript:void(0)\"><i class=\"fa fa-external-link\"></i> ";
        echo ($context["text_learn_to_increase"] ?? null);
        echo "</a></div>
        </td>  
    </tr>
</table>
";
    }

    public function getTemplateName()
    {
        return "extension/module/excelport/tab_export.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  801 => 415,  796 => 413,  784 => 406,  776 => 403,  768 => 400,  762 => 397,  758 => 395,  733 => 392,  730 => 391,  713 => 390,  708 => 388,  705 => 387,  688 => 384,  685 => 383,  681 => 382,  674 => 380,  669 => 378,  660 => 372,  647 => 362,  643 => 361,  638 => 359,  626 => 352,  616 => 345,  603 => 335,  599 => 334,  594 => 332,  582 => 325,  572 => 318,  559 => 308,  555 => 307,  550 => 305,  538 => 298,  528 => 291,  515 => 281,  511 => 280,  506 => 278,  494 => 271,  484 => 264,  471 => 254,  467 => 253,  462 => 251,  450 => 244,  441 => 238,  428 => 228,  424 => 227,  419 => 225,  407 => 218,  398 => 212,  385 => 202,  381 => 201,  376 => 199,  364 => 192,  355 => 186,  342 => 176,  338 => 175,  333 => 173,  321 => 166,  312 => 160,  299 => 150,  295 => 149,  290 => 147,  278 => 140,  269 => 134,  256 => 124,  252 => 123,  247 => 121,  235 => 114,  226 => 108,  213 => 98,  209 => 97,  204 => 95,  192 => 88,  183 => 82,  170 => 72,  166 => 71,  161 => 69,  149 => 62,  140 => 56,  127 => 46,  123 => 45,  118 => 43,  106 => 36,  97 => 30,  84 => 20,  80 => 19,  75 => 17,  63 => 10,  58 => 8,  50 => 7,  45 => 4,  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/excelport/tab_export.twig", "");
    }
}

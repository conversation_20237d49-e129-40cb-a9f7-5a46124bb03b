<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/importexport/caselist.twig */
class __TwigTemplate_55182ec110576aeae804cd0e041b2fcfa53fcd89e1a460b49c602838d3afab13 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        if (($context["cases"] ?? null)) {
            // line 2
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["cases"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["case"]) {
                // line 3
                echo "\t<tr>
\t\t<td class=\"text-center\" width=\"1px\">
\t\t\t<input type=\"checkbox\" class=\"selectedcheckbox\" name=\"selected[]\" value=\"";
                // line 5
                echo twig_get_attribute($this->env, $this->source, $context["case"], "excel_id", [], "any", false, false, false, 5);
                echo "\" />
\t\t</td>
\t\t<td class=\"text-left\">
\t\t\t<div class=\"col-sm-6\">
\t\t\t\t<div class=\"caseint\">";
                // line 9
                echo twig_get_attribute($this->env, $this->source, $context["case"], "casename", [], "any", false, false, false, 9);
                echo "</div>
\t\t\t\t<div class=\"casename\">
\t\t\t\t\t";
                // line 11
                echo twig_get_attribute($this->env, $this->source, $context["case"], "name", [], "any", false, false, false, 11);
                echo "<br />
\t\t\t\t\t<span>";
                // line 12
                echo twig_get_attribute($this->env, $this->source, $context["case"], "date", [], "any", false, false, false, 12);
                echo "</span>
\t\t\t\t</div>
\t\t\t</div>
\t\t\t<div class=\"col-sm-6 text-right\">
\t\t\t\t<div class=\"btn-group\" role=\"group\" aria-label=\"...\">
\t\t\t\t\t<button type=\"button\" class=\"btn ";
                // line 17
                if ((twig_get_attribute($this->env, $this->source, $context["case"], "type", [], "any", false, false, false, 17) == "import")) {
                    echo "btn-info btn-caseimport";
                } else {
                    echo "btn-primary btn-caseexport";
                }
                echo "\" rel=\"";
                echo twig_get_attribute($this->env, $this->source, $context["case"], "excel_id", [], "any", false, false, false, 17);
                echo "\"><i class=\"fa ";
                if ((twig_get_attribute($this->env, $this->source, $context["case"], "type", [], "any", false, false, false, 17) == "import")) {
                    echo "fa-upload";
                } else {
                    echo "fa-download";
                }
                echo "\"></i> ";
                echo ($context["text_processed"] ?? null);
                echo "</button>
\t\t\t\t\t<a href=\"";
                // line 18
                echo twig_get_attribute($this->env, $this->source, $context["case"], "edit", [], "any", false, false, false, 18);
                echo "\" class=\"btn btn-warning\"><i class=\"fa fa-edit\"></i> ";
                echo ($context["text_edit1"] ?? null);
                echo "</a>
\t\t\t\t</div>
\t\t\t</div>
\t\t</td>
\t</tr>
\t";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['case'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 24
            echo "\t";
        } else {
            // line 25
            echo "\t\t<tr><td class=\"text-center\" colspan=\"2\">";
            echo ($context["text_no_results"] ?? null);
            echo "</td></tr>
\t";
        }
        // line 27
        echo "\t<div class=\"row\">
\t  <div class=\"col-sm-6 text-left\">";
        // line 28
        echo ($context["pagination"] ?? null);
        echo "</div>
\t  <div class=\"col-sm-6 text-right\">";
        // line 29
        echo ($context["results"] ?? null);
        echo "</div>
\t</div>
\t<script>
 \$('.btn-caseexport').on('click', function() {
\t\$('#export-title').text('Export Processing');
\t\$('.success-export,.error-export,.import_item,.updatehtml').addClass('hide');
\t\$(\"#current-progress\").text('";
        // line 35
        echo ($context["text_processing"] ?? null);
        echo "');
\t\$('.processing .progress-bar').addClass('active');
\t\$('.processing .progress-bar').removeClass('progress-bar-danger');
\t\$('.processing .progress-bar').addClass('progress-bar-success');
\t\$('.updatehtml .logs').html(''); 
\tvar excel_import_export_id = \$(this).attr('rel');
\t\$.ajax({
\t\turl: 'index.php?route=extension/importexport/exportcase&user_token=";
        // line 42
        echo ($context["user_token"] ?? null);
        echo "&excel_import_export_id='+excel_import_export_id,
\t\ttype: 'post',
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('#export').modal('show');
\t\t},
\t\tcomplete: function() {
\t\t\t
\t\t},
\t\tsuccess: function(json) {
\t\t\tif(json['error']){
\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\t\$('.error-export').removeClass('hide');
\t\t\t\t\$('.error-msg').html(json['error']);
\t\t\t\t\$(\"#current-progress\").text('";
        // line 56
        echo ($context["text_stop_process"] ?? null);
        echo "');
\t\t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
\t\t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
\t\t\t}
\t\t\tif(json['success']){
\t\t\t\tif(json['download']){
\t\t\t\t\twindow.location=json['download'];
\t\t\t\t}
\t\t\t\t
\t\t\t\t\$(\"#dynamic\").css(\"width\",'100%');
\t\t\t\t\$(\"#current-progress\").text('100%');
\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\t\$('.success-export').removeClass('hide');
\t\t\t\tif(json['filename']){
\t\t\t\t\t\$('.filename').html(json['filename']);
\t\t\t\t\t\$('.file-size').html(json['filesize']);
\t\t\t\t}
\t\t\t}
\t\t\t
\t\t},
\t\t error: function(xhr, ajaxOptions, thrownError) {
\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t\$('.processing .progress-bar').removeClass('progress-bar-success');
\t\t\t\$('.processing .progress-bar').addClass('progress-bar-danger');
\t\t\t\$('.error-export').removeClass('hide');
\t\t\t\$(\"#current-progress\").text('";
        // line 81
        echo ($context["text_stop_process"] ?? null);
        echo "');
\t\t\t\$('.error-msg').html(xhr.responseText);
\t\t}
\t});
});

\$('.btn-caseimport').on('click', function() {
\t\$('#export-title').text('";
        // line 88
        echo ($context["text_import_processing"] ?? null);
        echo "');
\t\$('.success-export').addClass('hide');
\t\$('.totalnewitem').text(0);
\t\$('.totalupdateitem').text(0);
\t\$('.totaldeleteitem').text(0);
\t\$('.updatehtml .logs').html(''); 
\t\$(\"#dynamic\").css(\"width\",'0%');
\t\$(\"#current-progress\").text('0%');
\tvar repeater;
\tvar excel_import_export_id = \$(this).attr('rel');
\t\$.ajax({
\t\turl: 'index.php?route=extension/importexport/importcase&user_token=";
        // line 99
        echo ($context["user_token"] ?? null);
        echo "&excel_import_export_id='+excel_import_export_id,
\t\ttype: 'post',
\t\tdataType: 'json',
\t\tbeforeSend: function() {
\t\t\t\$('#export').modal('show');
\t\t},
\t\tcomplete: function() {
\t\t\tclearInterval(repeater);
\t\t},
\t\tsuccess: function(json) {
\t\t\t
\t\t\tif(json['filename']){
\t\t\t\t\$('.filename').html(json['filename']);
\t\t\t\t\$('.file-size').html(json['filesize']);
\t\t\t}
\t\t\t
\t\t\tif(json['newitem']){
\t\t\t\tfor(i in json['newitem']){
\t\t\t\t\tif(json['newitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['newitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.totalnewitem').text(json['totalnewitem']);
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totalnewitem']){
\t\t\t\t\$('.totalnewitem').text(json['totalnewitem']);
\t\t\t}
\t\t\tif(json['updateitem']){
\t\t\t\tfor(i in json['updateitem']){
\t\t\t\t\tif(json['updateitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['updateitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totalupdateitem']){
\t\t\t\t\$('.totalupdateitem').text(json['totalupdateitem']);
\t\t\t}
\t\t\tif(json['deleteitem']){
\t\t\t\tfor(i in json['deleteitem']){
\t\t\t\t\tif(json['deleteitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['deleteitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.totaldeleteitem').text(json['totaldeleteitem']);
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totaldeleteitem']){
\t\t\t\t\$('.totaldeleteitem').text(json['totaldeleteitem']);
\t\t\t}
\t\t\t
\t\t\tif(json['completepercentage'] < 100){
\t\t\t\tvar swidth = json['completepercentage'] + '%';
\t\t\t}else{
\t\t\t\tvar swidth = '100%';
\t\t\t}
\t\t\tif (json['completepercentage']) {
\t\t\t\t\$(\"#dynamic\").css(\"width\",swidth);
\t\t\t\t\$(\"#current-progress\").text(swidth);
\t\t\t}
\t\t\t
\t\t\tif (json['success']) {
\t\t\t\t\$('.success-export,.import_item').removeClass('hide');
\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t}
\t\t\t
\t\t\tif (json['next']) {
\t\t\t\tnext(json['next']);
\t\t\t}
\t\t}
\t});
});

function next(url) {
\t\$.ajax({
\t\turl: url,
\t\tdataType: 'json',
\t\tsuccess: function(json) {
\t\t\t\$('.alert-dismissible').remove();
\t\t\t
\t\t\tif (json['success']) {
\t\t\t\t\$('.success-export,.import_item').removeClass('hide');
\t\t\t\t\$('.processing .progress-bar').removeClass('active');
\t\t\t}
\t\t\t
\t\t\tif(json['completepercentage'] < 100){
\t\t\t\tvar swidth = json['completepercentage'] + '%';
\t\t\t}else{
\t\t\t\tvar swidth = '100%';
\t\t\t}
\t\t\t
\t\t\tif (json['completepercentage']) {
\t\t\t\t\$(\"#dynamic\").css(\"width\",swidth);
\t\t\t\t\$(\"#current-progress\").text(swidth);
\t\t\t}
\t\t\t
\t\t\tif(json['newitem']){
\t\t\t\tfor(i in json['newitem']){
\t\t\t\t\tif(json['newitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['newitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.totalnewitem').text(json['totalnewitem']);
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totalnewitem']){
\t\t\t\t\$('.totalnewitem').text(json['totalnewitem']);
\t\t\t}
\t\t\tif(json['updateitem']){
\t\t\t\tfor(i in json['updateitem']){
\t\t\t\t\tif(json['updateitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['updateitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totalupdateitem']){
\t\t\t\t\$('.totalupdateitem').text(json['totalupdateitem']);
\t\t\t}
\t\t\tif(json['deleteitem']){
\t\t\t\tfor(i in json['deleteitem']){
\t\t\t\t\tif(json['deleteitem'][i] != \"\"){
\t\t\t\t\t\t\$('.updatehtml .logs').prepend('<p>'+json['deleteitem'][i]+'</p>');
\t\t\t\t\t}
\t\t\t\t}
\t\t\t\t\$('.totaldeleteitem').text(json['totaldeleteitem']);
\t\t\t\t\$('.updatehtml').removeClass('hide');
\t\t\t}
\t\t\tif(json['totaldeleteitem']){
\t\t\t\t\$('.totaldeleteitem').text(json['totaldeleteitem']);
\t\t\t}
\t\t\t
\t\t\tif (json['next']) {
\t\t\t\tnext(json['next']);
\t\t\t}
\t\t},
\t\terror: function(xhr, ajaxOptions, thrownError) {
\t\t\talert(thrownError + \"\\r\\n\" + xhr.statusText + \"\\r\\n\" + xhr.responseText);
\t\t}
\t});
}

</script>";
    }

    public function getTemplateName()
    {
        return "extension/importexport/caselist.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  207 => 99,  193 => 88,  183 => 81,  155 => 56,  138 => 42,  128 => 35,  119 => 29,  115 => 28,  112 => 27,  106 => 25,  103 => 24,  89 => 18,  71 => 17,  63 => 12,  59 => 11,  54 => 9,  47 => 5,  43 => 3,  39 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/importexport/caselist.twig", "");
    }
}

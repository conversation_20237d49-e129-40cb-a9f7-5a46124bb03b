<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/shipping/usps.twig */
class __TwigTemplate_3206c963da46364084d55f359374143e5be942b3c6b00d2bb3e2b54be7749b3b extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <div class=\"pull-right\">
        <button type=\"submit\" form=\"form-shipping\" data-toggle=\"tooltip\" title=\"";
        // line 6
        echo ($context["button_save"] ?? null);
        echo "\" class=\"btn btn-primary\"><i class=\"fa fa-save\"></i></button>
        <a href=\"";
        // line 7
        echo ($context["cancel"] ?? null);
        echo "\" data-toggle=\"tooltip\" title=\"";
        echo ($context["button_cancel"] ?? null);
        echo "\" class=\"btn btn-default\"><i class=\"fa fa-reply\"></i></a></div>
      <h1>";
        // line 8
        echo ($context["heading_title"] ?? null);
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 10
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 11
            echo "        <li><a href=\"";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "href", [], "any", false, false, false, 11);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["breadcrumb"], "text", [], "any", false, false, false, 11);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 13
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    ";
        // line 17
        if (($context["error_warning"] ?? null)) {
            // line 18
            echo "    <div class=\"alert alert-danger alert-dismissible\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 22
        echo "    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i> ";
        // line 24
        echo ($context["text_edit"] ?? null);
        echo "</h3>
      </div>
      <div class=\"panel-body\">
        <form action=\"";
        // line 27
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form-shipping\" class=\"form-horizontal\">
          <div class=\"form-group required\">
            <label class=\"col-sm-2 control-label\" for=\"input-user-id\">";
        // line 29
        echo ($context["entry_user_id"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <input type=\"text\" name=\"shipping_usps_user_id\" value=\"";
        // line 31
        echo ($context["shipping_usps_user_id"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_user_id"] ?? null);
        echo "\" id=\"input-user-id\" class=\"form-control\" />
              ";
        // line 32
        if (($context["error_user_id"] ?? null)) {
            // line 33
            echo "              <div class=\"text-danger\">";
            echo ($context["error_user_id"] ?? null);
            echo "</div>
              ";
        }
        // line 35
        echo "            </div>
          </div>
          <div class=\"form-group required\">
            <label class=\"col-sm-2 control-label\" for=\"input-postcode\">";
        // line 38
        echo ($context["entry_postcode"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <input type=\"text\" name=\"shipping_usps_postcode\" value=\"";
        // line 40
        echo ($context["shipping_usps_postcode"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_postcode"] ?? null);
        echo "\" id=\"input-postcode\" class=\"form-control\" />
              ";
        // line 41
        if (($context["error_postcode"] ?? null)) {
            // line 42
            echo "              <div class=\"text-danger\">";
            echo ($context["error_postcode"] ?? null);
            echo "</div>
              ";
        }
        // line 44
        echo "            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\">";
        // line 47
        echo ($context["entry_domestic"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <div class=\"well well-sm\" style=\"height: 150px; overflow: auto;\">
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 52
        if (($context["shipping_usps_domestic_00"] ?? null)) {
            // line 53
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_00\" value=\"1\" checked=\"checked\" />
                    ";
            // line 54
            echo ($context["text_domestic_00"] ?? null);
            echo "
                    ";
        } else {
            // line 56
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_00\" value=\"1\" />
                    ";
            // line 57
            echo ($context["text_domestic_00"] ?? null);
            echo "
                    ";
        }
        // line 59
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 63
        if (($context["shipping_usps_domestic_01"] ?? null)) {
            // line 64
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_01\" value=\"1\" checked=\"checked\" />
                    ";
            // line 65
            echo ($context["text_domestic_01"] ?? null);
            echo "
                    ";
        } else {
            // line 67
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_01\" value=\"1\" />
                    ";
            // line 68
            echo ($context["text_domestic_01"] ?? null);
            echo "
                    ";
        }
        // line 70
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 74
        if (($context["shipping_usps_domestic_02"] ?? null)) {
            // line 75
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_02\" value=\"1\" checked=\"checked\" />
                    ";
            // line 76
            echo ($context["text_domestic_02"] ?? null);
            echo "
                    ";
        } else {
            // line 78
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_02\" value=\"1\" />
                    ";
            // line 79
            echo ($context["text_domestic_02"] ?? null);
            echo "
                    ";
        }
        // line 81
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 85
        if (($context["shipping_usps_domestic_03"] ?? null)) {
            // line 86
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_03\" value=\"1\" checked=\"checked\" />
                    ";
            // line 87
            echo ($context["text_domestic_03"] ?? null);
            echo "
                    ";
        } else {
            // line 89
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_03\" value=\"1\" />
                    ";
            // line 90
            echo ($context["text_domestic_03"] ?? null);
            echo "
                    ";
        }
        // line 92
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 96
        if (($context["shipping_usps_domestic_1"] ?? null)) {
            // line 97
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_1\" value=\"1\" checked=\"checked\" />
                    ";
            // line 98
            echo ($context["text_domestic_1"] ?? null);
            echo "
                    ";
        } else {
            // line 100
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_1\" value=\"1\" />
                    ";
            // line 101
            echo ($context["text_domestic_1"] ?? null);
            echo "
                    ";
        }
        // line 103
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 107
        if (($context["shipping_usps_domestic_2"] ?? null)) {
            // line 108
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_2\" value=\"1\" checked=\"checked\" />
                    ";
            // line 109
            echo ($context["text_domestic_2"] ?? null);
            echo "
                    ";
        } else {
            // line 111
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_2\" value=\"1\" />
                    ";
            // line 112
            echo ($context["text_domestic_2"] ?? null);
            echo "
                    ";
        }
        // line 114
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 118
        if (($context["shipping_usps_domestic_3"] ?? null)) {
            // line 119
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_3\" value=\"1\" checked=\"checked\" />
                    ";
            // line 120
            echo ($context["text_domestic_3"] ?? null);
            echo "
                    ";
        } else {
            // line 122
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_3\" value=\"1\" />
                    ";
            // line 123
            echo ($context["text_domestic_3"] ?? null);
            echo "
                    ";
        }
        // line 125
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 129
        if (($context["shipping_usps_domestic_4"] ?? null)) {
            // line 130
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_4\" value=\"1\" checked=\"checked\" />
                    ";
            // line 131
            echo ($context["text_domestic_4"] ?? null);
            echo "
                    ";
        } else {
            // line 133
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_4\" value=\"1\" />
                    ";
            // line 134
            echo ($context["text_domestic_4"] ?? null);
            echo "
                    ";
        }
        // line 136
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 140
        if (($context["shipping_usps_domestic_5"] ?? null)) {
            // line 141
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_5\" value=\"1\" checked=\"checked\" />
                    ";
            // line 142
            echo ($context["text_domestic_5"] ?? null);
            echo "
                    ";
        } else {
            // line 144
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_5\" value=\"1\" />
                    ";
            // line 145
            echo ($context["text_domestic_5"] ?? null);
            echo "
                    ";
        }
        // line 147
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 151
        if (($context["shipping_usps_domestic_6"] ?? null)) {
            // line 152
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_6\" value=\"1\" checked=\"checked\" />
                    ";
            // line 153
            echo ($context["text_domestic_6"] ?? null);
            echo "
                    ";
        } else {
            // line 155
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_6\" value=\"1\" />
                    ";
            // line 156
            echo ($context["text_domestic_6"] ?? null);
            echo "
                    ";
        }
        // line 158
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 162
        if (($context["shipping_usps_domestic_7"] ?? null)) {
            // line 163
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_7\" value=\"1\" checked=\"checked\" />
                    ";
            // line 164
            echo ($context["text_domestic_7"] ?? null);
            echo "
                    ";
        } else {
            // line 166
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_7\" value=\"1\" />
                    ";
            // line 167
            echo ($context["text_domestic_7"] ?? null);
            echo "
                    ";
        }
        // line 169
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 173
        if (($context["shipping_usps_domestic_12"] ?? null)) {
            // line 174
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_12\" value=\"1\" checked=\"checked\" />
                    ";
            // line 175
            echo ($context["text_domestic_12"] ?? null);
            echo "
                    ";
        } else {
            // line 177
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_12\" value=\"1\" />
                    ";
            // line 178
            echo ($context["text_domestic_12"] ?? null);
            echo "
                    ";
        }
        // line 180
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 184
        if (($context["shipping_usps_domestic_13"] ?? null)) {
            // line 185
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_13\" value=\"1\" checked=\"checked\" />
                    ";
            // line 186
            echo ($context["text_domestic_13"] ?? null);
            echo "
                    ";
        } else {
            // line 188
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_13\" value=\"1\" />
                    ";
            // line 189
            echo ($context["text_domestic_13"] ?? null);
            echo "
                    ";
        }
        // line 191
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 195
        if (($context["shipping_usps_domestic_16"] ?? null)) {
            // line 196
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_16\" value=\"1\" checked=\"checked\" />
                    ";
            // line 197
            echo ($context["text_domestic_16"] ?? null);
            echo "
                    ";
        } else {
            // line 199
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_16\" value=\"1\" />
                    ";
            // line 200
            echo ($context["text_domestic_16"] ?? null);
            echo "
                    ";
        }
        // line 202
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 206
        if (($context["shipping_usps_domestic_17"] ?? null)) {
            // line 207
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_17\" value=\"1\" checked=\"checked\" />
                    ";
            // line 208
            echo ($context["text_domestic_17"] ?? null);
            echo "
                    ";
        } else {
            // line 210
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_17\" value=\"1\" />
                    ";
            // line 211
            echo ($context["text_domestic_17"] ?? null);
            echo "
                    ";
        }
        // line 213
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 217
        if (($context["shipping_usps_domestic_18"] ?? null)) {
            // line 218
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_18\" value=\"1\" checked=\"checked\" />
                    ";
            // line 219
            echo ($context["text_domestic_18"] ?? null);
            echo "
                    ";
        } else {
            // line 221
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_18\" value=\"1\" />
                    ";
            // line 222
            echo ($context["text_domestic_18"] ?? null);
            echo "
                    ";
        }
        // line 224
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 228
        if (($context["shipping_usps_domestic_19"] ?? null)) {
            // line 229
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_19\" value=\"1\" checked=\"checked\" />
                    ";
            // line 230
            echo ($context["text_domestic_19"] ?? null);
            echo "
                    ";
        } else {
            // line 232
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_19\" value=\"1\" />
                    ";
            // line 233
            echo ($context["text_domestic_19"] ?? null);
            echo "
                    ";
        }
        // line 235
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 239
        if (($context["shipping_usps_domestic_22"] ?? null)) {
            // line 240
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_22\" value=\"1\" checked=\"checked\" />
                    ";
            // line 241
            echo ($context["text_domestic_22"] ?? null);
            echo "
                    ";
        } else {
            // line 243
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_22\" value=\"1\" />
                    ";
            // line 244
            echo ($context["text_domestic_22"] ?? null);
            echo "
                   ";
        }
        // line 246
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 250
        if (($context["shipping_usps_domestic_23"] ?? null)) {
            // line 251
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_23\" value=\"1\" checked=\"checked\" />
                    ";
            // line 252
            echo ($context["text_domestic_23"] ?? null);
            echo "
                    ";
        } else {
            // line 254
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_23\" value=\"1\" />
                    ";
            // line 255
            echo ($context["text_domestic_23"] ?? null);
            echo "
                    ";
        }
        // line 257
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 261
        if (($context["shipping_usps_domestic_25"] ?? null)) {
            // line 262
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_25\" value=\"1\" checked=\"checked\" />
                    ";
            // line 263
            echo ($context["text_domestic_25"] ?? null);
            echo "
                    ";
        } else {
            // line 265
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_25\" value=\"1\" />
                    ";
            // line 266
            echo ($context["text_domestic_25"] ?? null);
            echo "
                    ";
        }
        // line 268
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 272
        if (($context["shipping_usps_domestic_27"] ?? null)) {
            // line 273
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_27\" value=\"1\" checked=\"checked\" />
                    ";
            // line 274
            echo ($context["text_domestic_27"] ?? null);
            echo "
                    ";
        } else {
            // line 276
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_27\" value=\"1\" />
                    ";
            // line 277
            echo ($context["text_domestic_27"] ?? null);
            echo "
                    ";
        }
        // line 279
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 283
        if (($context["shipping_usps_domestic_28"] ?? null)) {
            // line 284
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_28\" value=\"1\" checked=\"checked\" />
                    ";
            // line 285
            echo ($context["text_domestic_28"] ?? null);
            echo "
                    ";
        } else {
            // line 287
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_domestic_28\" value=\"1\" />
                    ";
            // line 288
            echo ($context["text_domestic_28"] ?? null);
            echo "
                    ";
        }
        // line 290
        echo "                  </label>
                </div>
              </div>
              <button type=\"button\" onclick=\"\$(this).parent().find(':checkbox').prop('checked', true);\" class=\"btn btn-link\">";
        // line 293
        echo ($context["text_select_all"] ?? null);
        echo "</button> / <button type=\"button\" onclick=\"\$(this).parent().find(':checkbox').prop('checked', false);\" class=\"btn btn-link\">";
        echo ($context["text_unselect_all"] ?? null);
        echo "</button></div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\">";
        // line 296
        echo ($context["entry_international"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <div class=\"well well-sm\" style=\"height: 150px; overflow: auto;\">
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 301
        if (($context["shipping_usps_international_1"] ?? null)) {
            // line 302
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_1\" value=\"1\" checked=\"checked\" />
                    ";
            // line 303
            echo ($context["text_international_1"] ?? null);
            echo "
                    ";
        } else {
            // line 305
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_1\" value=\"1\" />
                    ";
            // line 306
            echo ($context["text_international_1"] ?? null);
            echo "
                    ";
        }
        // line 308
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 312
        if (($context["shipping_usps_international_2"] ?? null)) {
            // line 313
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_2\" value=\"1\" checked=\"checked\" />
                    ";
            // line 314
            echo ($context["text_international_2"] ?? null);
            echo "
                    ";
        } else {
            // line 316
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_2\" value=\"1\" />
                    ";
            // line 317
            echo ($context["text_international_2"] ?? null);
            echo "
                    ";
        }
        // line 319
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 323
        if (($context["shipping_usps_international_4"] ?? null)) {
            // line 324
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_4\" value=\"1\" checked=\"checked\" />
                    ";
            // line 325
            echo ($context["text_international_4"] ?? null);
            echo "
                    ";
        } else {
            // line 327
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_4\" value=\"1\" />
                    ";
            // line 328
            echo ($context["text_international_4"] ?? null);
            echo "
                    ";
        }
        // line 330
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 334
        if (($context["shipping_usps_international_5"] ?? null)) {
            // line 335
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_5\" value=\"1\" checked=\"checked\" />
                    ";
            // line 336
            echo ($context["text_international_5"] ?? null);
            echo "
                    ";
        } else {
            // line 338
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_5\" value=\"1\" />
                    ";
            // line 339
            echo ($context["text_international_5"] ?? null);
            echo "
                    ";
        }
        // line 341
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 345
        if (($context["shipping_usps_international_6"] ?? null)) {
            // line 346
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_6\" value=\"1\" checked=\"checked\" />
                    ";
            // line 347
            echo ($context["text_international_6"] ?? null);
            echo "
                    ";
        } else {
            // line 349
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_6\" value=\"1\" />
                    ";
            // line 350
            echo ($context["text_international_6"] ?? null);
            echo "
                    ";
        }
        // line 352
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 356
        if (($context["shipping_usps_international_7"] ?? null)) {
            // line 357
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_7\" value=\"1\" checked=\"checked\" />
                    ";
            // line 358
            echo ($context["text_international_7"] ?? null);
            echo "
                    ";
        } else {
            // line 360
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_7\" value=\"1\" />
                    ";
            // line 361
            echo ($context["text_international_7"] ?? null);
            echo "
                    ";
        }
        // line 363
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 367
        if (($context["shipping_usps_international_8"] ?? null)) {
            // line 368
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_8\" value=\"1\" checked=\"checked\" />
                    ";
            // line 369
            echo ($context["text_international_8"] ?? null);
            echo "
                    ";
        } else {
            // line 371
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_8\" value=\"1\" />
                    ";
            // line 372
            echo ($context["text_international_8"] ?? null);
            echo "
                    ";
        }
        // line 374
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 378
        if (($context["shipping_usps_international_9"] ?? null)) {
            // line 379
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_9\" value=\"1\" checked=\"checked\" />
                    ";
            // line 380
            echo ($context["text_international_9"] ?? null);
            echo "
                    ";
        } else {
            // line 382
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_9\" value=\"1\" />
                    ";
            // line 383
            echo ($context["text_international_9"] ?? null);
            echo "
                    ";
        }
        // line 385
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 389
        if (($context["shipping_usps_international_10"] ?? null)) {
            // line 390
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_10\" value=\"1\" checked=\"checked\" />
                    ";
            // line 391
            echo ($context["text_international_10"] ?? null);
            echo "
                    ";
        } else {
            // line 393
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_10\" value=\"1\" />
                    ";
            // line 394
            echo ($context["text_international_10"] ?? null);
            echo "
                    ";
        }
        // line 396
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 400
        if (($context["shipping_usps_international_11"] ?? null)) {
            // line 401
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_11\" value=\"1\" checked=\"checked\" />
                    ";
            // line 402
            echo ($context["text_international_11"] ?? null);
            echo "
                    ";
        } else {
            // line 404
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_11\" value=\"1\" />
                    ";
            // line 405
            echo ($context["text_international_11"] ?? null);
            echo "
                    ";
        }
        // line 407
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 411
        if (($context["shipping_usps_international_12"] ?? null)) {
            // line 412
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_12\" value=\"1\" checked=\"checked\" />
                    ";
            // line 413
            echo ($context["text_international_12"] ?? null);
            echo "
                    ";
        } else {
            // line 415
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_12\" value=\"1\" />
                    ";
            // line 416
            echo ($context["text_international_12"] ?? null);
            echo "
                    ";
        }
        // line 418
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 422
        if (($context["shipping_usps_international_13"] ?? null)) {
            // line 423
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_13\" value=\"1\" checked=\"checked\" />
                    ";
            // line 424
            echo ($context["text_international_13"] ?? null);
            echo "
                    ";
        } else {
            // line 426
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_13\" value=\"1\" />
                    ";
            // line 427
            echo ($context["text_international_13"] ?? null);
            echo "
                    ";
        }
        // line 429
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 433
        if (($context["shipping_usps_international_14"] ?? null)) {
            // line 434
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_14\" value=\"1\" checked=\"checked\" />
                    ";
            // line 435
            echo ($context["text_international_14"] ?? null);
            echo "
                    ";
        } else {
            // line 437
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_14\" value=\"1\" />
                    ";
            // line 438
            echo ($context["text_international_14"] ?? null);
            echo "
                    ";
        }
        // line 440
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 444
        if (($context["shipping_usps_international_15"] ?? null)) {
            // line 445
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_15\" value=\"1\" checked=\"checked\" />
                    ";
            // line 446
            echo ($context["text_international_15"] ?? null);
            echo "
                    ";
        } else {
            // line 448
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_15\" value=\"1\" />
                    ";
            // line 449
            echo ($context["text_international_15"] ?? null);
            echo "
                    ";
        }
        // line 451
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 455
        if (($context["shipping_usps_international_16"] ?? null)) {
            // line 456
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_16\" value=\"1\" checked=\"checked\" />
                    ";
            // line 457
            echo ($context["text_international_16"] ?? null);
            echo "
                    ";
        } else {
            // line 459
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_16\" value=\"1\" />
                    ";
            // line 460
            echo ($context["text_international_16"] ?? null);
            echo "
                    ";
        }
        // line 462
        echo "                  </label>
                </div>
                <div class=\"checkbox\">
                  <label>
                    ";
        // line 466
        if (($context["shipping_usps_international_21"] ?? null)) {
            // line 467
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_21\" value=\"1\" checked=\"checked\" />
                    ";
            // line 468
            echo ($context["text_international_21"] ?? null);
            echo "
                    ";
        } else {
            // line 470
            echo "                    <input type=\"checkbox\" name=\"shipping_usps_international_21\" value=\"1\" />
                    ";
            // line 471
            echo ($context["text_international_21"] ?? null);
            echo "
                    ";
        }
        // line 473
        echo "                  </label>
                </div>
              </div>
              <button type=\"button\" onclick=\"\$(this).parent().find(':checkbox').prop('checked', true);\" class=\"btn btn-link\">";
        // line 476
        echo ($context["text_select_all"] ?? null);
        echo "</button> / <button type=\"button\" onclick=\"\$(this).parent().find(':checkbox').prop('checked', false);\" class=\"btn btn-link\">";
        echo ($context["text_unselect_all"] ?? null);
        echo "</button></div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-size\">";
        // line 479
        echo ($context["entry_size"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_size\" id=\"input-size\" class=\"form-control\">
                ";
        // line 482
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["sizes"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["size"]) {
            // line 483
            echo "                ";
            if ((twig_get_attribute($this->env, $this->source, $context["size"], "value", [], "any", false, false, false, 483) == ($context["shipping_usps_size"] ?? null))) {
                // line 484
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["size"], "value", [], "any", false, false, false, 484);
                echo "\" selected=\"selected\">";
                echo twig_get_attribute($this->env, $this->source, $context["size"], "text", [], "any", false, false, false, 484);
                echo "</option>
                ";
            } else {
                // line 486
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["size"], "value", [], "any", false, false, false, 486);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["size"], "text", [], "any", false, false, false, 486);
                echo "</option>
               ";
            }
            // line 488
            echo "                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['size'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 489
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-container\">";
        // line 493
        echo ($context["entry_container"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_container\" id=\"input-container\" class=\"form-control\">
                ";
        // line 496
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["containers"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["container"]) {
            // line 497
            echo "                ";
            if ((twig_get_attribute($this->env, $this->source, $context["container"], "value", [], "any", false, false, false, 497) == ($context["shipping_usps_container"] ?? null))) {
                // line 498
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["container"], "value", [], "any", false, false, false, 498);
                echo "\" selected=\"selected\">";
                echo twig_get_attribute($this->env, $this->source, $context["container"], "text", [], "any", false, false, false, 498);
                echo "</option>
                ";
            } else {
                // line 500
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["container"], "value", [], "any", false, false, false, 500);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["container"], "text", [], "any", false, false, false, 500);
                echo "</option>
                ";
            }
            // line 502
            echo "                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['container'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 503
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-machinable\">";
        // line 507
        echo ($context["entry_machinable"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_machinable\" id=\"input-machinable\" class=\"form-control\">
                ";
        // line 510
        if (($context["shipping_usps_machinable"] ?? null)) {
            // line 511
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_yes"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 512
            echo ($context["text_no"] ?? null);
            echo "</option>
                ";
        } else {
            // line 514
            echo "                <option value=\"1\">";
            echo ($context["text_yes"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 515
            echo ($context["text_no"] ?? null);
            echo "</option>
                ";
        }
        // line 517
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group required\">
            <label class=\"col-sm-2 control-label\" for=\"input-length\"><span data-toggle=\"tooltip\" title=\"";
        // line 521
        echo ($context["help_dimension"] ?? null);
        echo "\">";
        echo ($context["entry_dimension"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-10\">
              <div class=\"row\">
                <div class=\"col-sm-4\">
                  <input type=\"text\" name=\"shipping_usps_length\" value=\"";
        // line 525
        echo ($context["shipping_usps_length"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_length"] ?? null);
        echo "\" id=\"input-length\" class=\"form-control\" />
                </div>
                <div class=\"col-sm-4\">
                  <input type=\"text\" name=\"shipping_usps_width\" value=\"";
        // line 528
        echo ($context["shipping_usps_width"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_width"] ?? null);
        echo "\" id=\"input-width\" class=\"form-control\" />
                </div>
                <div class=\"col-sm-4\">
                  <input type=\"text\" name=\"shipping_usps_height\" value=\"";
        // line 531
        echo ($context["shipping_usps_height"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_height"] ?? null);
        echo "\" id=\"input-height\" class=\"form-control\" />
                </div>
              </div>
              ";
        // line 534
        if (($context["error_dimension"] ?? null)) {
            // line 535
            echo "              <div class=\"text-danger\">";
            echo ($context["error_dimension"] ?? null);
            echo "</div>
              ";
        }
        // line 537
        echo "            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\"><span data-toggle=\"tooltip\" title=\"";
        // line 540
        echo ($context["help_display_time"] ?? null);
        echo "\">";
        echo ($context["entry_display_time"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-10\">
              <label class=\"radio-inline\">
                ";
        // line 543
        if (($context["shipping_usps_display_time"] ?? null)) {
            // line 544
            echo "                <input type=\"radio\" name=\"shipping_usps_display_time\" value=\"1\" checked=\"checked\" />
                ";
            // line 545
            echo ($context["text_yes"] ?? null);
            echo "
                ";
        } else {
            // line 547
            echo "                <input type=\"radio\" name=\"shipping_usps_display_time\" value=\"1\" />
                ";
            // line 548
            echo ($context["text_yes"] ?? null);
            echo "
                ";
        }
        // line 550
        echo "              </label>
              <label class=\"radio-inline\">
                ";
        // line 552
        if ( !($context["shipping_usps_display_time"] ?? null)) {
            // line 553
            echo "                <input type=\"radio\" name=\"shipping_usps_display_time\" value=\"0\" checked=\"checked\" />
                ";
            // line 554
            echo ($context["text_no"] ?? null);
            echo "
                ";
        } else {
            // line 556
            echo "                <input type=\"radio\" name=\"shipping_usps_display_time\" value=\"0\" />
                ";
            // line 557
            echo ($context["text_no"] ?? null);
            echo "
                ";
        }
        // line 559
        echo "              </label>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\"><span data-toggle=\"tooltip\" title=\"";
        // line 563
        echo ($context["help_display_weight"] ?? null);
        echo "\">";
        echo ($context["entry_display_weight"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-10\">
              <label class=\"radio-inline\">
                ";
        // line 566
        if (($context["shipping_usps_display_weight"] ?? null)) {
            // line 567
            echo "                <input type=\"radio\" name=\"shipping_usps_display_weight\" value=\"1\" checked=\"checked\" />
                ";
            // line 568
            echo ($context["text_yes"] ?? null);
            echo "
                ";
        } else {
            // line 570
            echo "                <input type=\"radio\" name=\"shipping_usps_display_weight\" value=\"1\" />
                ";
            // line 571
            echo ($context["text_yes"] ?? null);
            echo "
                ";
        }
        // line 573
        echo "              </label>
              <label class=\"radio-inline\">
                ";
        // line 575
        if ( !($context["shipping_usps_display_weight"] ?? null)) {
            // line 576
            echo "                <input type=\"radio\" name=\"shipping_usps_display_weight\" value=\"0\" checked=\"checked\" />
                ";
            // line 577
            echo ($context["text_no"] ?? null);
            echo "
                ";
        } else {
            // line 579
            echo "                <input type=\"radio\" name=\"shipping_usps_display_weight\" value=\"0\" />
                ";
            // line 580
            echo ($context["text_no"] ?? null);
            echo "
                ";
        }
        // line 582
        echo "              </label>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-weight-class\"><span data-toggle=\"tooltip\" title=\"";
        // line 586
        echo ($context["help_weight_class"] ?? null);
        echo "\">";
        echo ($context["entry_weight_class"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_weight_class_id\" id=\"input-weight-class\" class=\"form-control\">
                ";
        // line 589
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["weight_classes"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["weight_class"]) {
            // line 590
            echo "                ";
            if ((twig_get_attribute($this->env, $this->source, $context["weight_class"], "weight_class_id", [], "any", false, false, false, 590) == ($context["shipping_usps_weight_class_id"] ?? null))) {
                // line 591
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["weight_class"], "weight_class_id", [], "any", false, false, false, 591);
                echo "\" selected=\"selected\">";
                echo twig_get_attribute($this->env, $this->source, $context["weight_class"], "title", [], "any", false, false, false, 591);
                echo "</option>
                ";
            } else {
                // line 593
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["weight_class"], "weight_class_id", [], "any", false, false, false, 593);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["weight_class"], "title", [], "any", false, false, false, 593);
                echo "</option>
                ";
            }
            // line 595
            echo "                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['weight_class'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 596
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-tax-class\">";
        // line 600
        echo ($context["entry_tax"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_tax_class_id\" id=\"input-tax-class\" class=\"form-control\">
                <option value=\"0\">";
        // line 603
        echo ($context["text_none"] ?? null);
        echo "</option>
                ";
        // line 604
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["tax_classes"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["tax_class"]) {
            // line 605
            echo "                ";
            if ((twig_get_attribute($this->env, $this->source, $context["tax_class"], "tax_class_id", [], "any", false, false, false, 605) == ($context["shipping_usps_tax_class_id"] ?? null))) {
                // line 606
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["tax_class"], "tax_class_id", [], "any", false, false, false, 606);
                echo "\" selected=\"selected\">";
                echo twig_get_attribute($this->env, $this->source, $context["tax_class"], "title", [], "any", false, false, false, 606);
                echo "</option>
                ";
            } else {
                // line 608
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["tax_class"], "tax_class_id", [], "any", false, false, false, 608);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["tax_class"], "title", [], "any", false, false, false, 608);
                echo "</option>
                ";
            }
            // line 610
            echo "                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['tax_class'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 611
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-geo-zone\">";
        // line 615
        echo ($context["entry_geo_zone"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_geo_zone_id\" id=\"input-geo-zone\" class=\"form-control\">
                <option value=\"0\">";
        // line 618
        echo ($context["text_all_zones"] ?? null);
        echo "</option>
                ";
        // line 619
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["geo_zones"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["geo_zone"]) {
            // line 620
            echo "                ";
            if ((twig_get_attribute($this->env, $this->source, $context["geo_zone"], "geo_zone_id", [], "any", false, false, false, 620) == ($context["shipping_usps_geo_zone_id"] ?? null))) {
                // line 621
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["geo_zone"], "geo_zone_id", [], "any", false, false, false, 621);
                echo "\" selected=\"selected\">";
                echo twig_get_attribute($this->env, $this->source, $context["geo_zone"], "name", [], "any", false, false, false, 621);
                echo "</option>
                ";
            } else {
                // line 623
                echo "                <option value=\"";
                echo twig_get_attribute($this->env, $this->source, $context["geo_zone"], "geo_zone_id", [], "any", false, false, false, 623);
                echo "\">";
                echo twig_get_attribute($this->env, $this->source, $context["geo_zone"], "name", [], "any", false, false, false, 623);
                echo "</option>
                ";
            }
            // line 625
            echo "                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['geo_zone'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 626
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-status\">";
        // line 630
        echo ($context["entry_status"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_status\" id=\"input-status\" class=\"form-control\">
                ";
        // line 633
        if (($context["shipping_usps_status"] ?? null)) {
            // line 634
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 635
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 637
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 638
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 640
        echo "              </select>
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-sort-order\">";
        // line 644
        echo ($context["entry_sort_order"] ?? null);
        echo "</label>
            <div class=\"col-sm-10\">
              <input type=\"text\" name=\"shipping_usps_sort_order\" value=\"";
        // line 646
        echo ($context["shipping_usps_sort_order"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_sort_order"] ?? null);
        echo "\" id=\"input-sort-order\" class=\"form-control\" />
            </div>
          </div>
          <div class=\"form-group\">
            <label class=\"col-sm-2 control-label\" for=\"input-debug\"><span data-toggle=\"tooltip\" title=\"";
        // line 650
        echo ($context["help_debug"] ?? null);
        echo "\">";
        echo ($context["entry_debug"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-10\">
              <select name=\"shipping_usps_debug\" id=\"input-debug\" class=\"form-control\">
                ";
        // line 653
        if (($context["shipping_usps_debug"] ?? null)) {
            // line 654
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 655
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 657
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 658
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 660
        echo "              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
";
        // line 668
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/shipping/usps.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  1588 => 668,  1578 => 660,  1573 => 658,  1568 => 657,  1563 => 655,  1558 => 654,  1556 => 653,  1548 => 650,  1539 => 646,  1534 => 644,  1528 => 640,  1523 => 638,  1518 => 637,  1513 => 635,  1508 => 634,  1506 => 633,  1500 => 630,  1494 => 626,  1488 => 625,  1480 => 623,  1472 => 621,  1469 => 620,  1465 => 619,  1461 => 618,  1455 => 615,  1449 => 611,  1443 => 610,  1435 => 608,  1427 => 606,  1424 => 605,  1420 => 604,  1416 => 603,  1410 => 600,  1404 => 596,  1398 => 595,  1390 => 593,  1382 => 591,  1379 => 590,  1375 => 589,  1367 => 586,  1361 => 582,  1356 => 580,  1353 => 579,  1348 => 577,  1345 => 576,  1343 => 575,  1339 => 573,  1334 => 571,  1331 => 570,  1326 => 568,  1323 => 567,  1321 => 566,  1313 => 563,  1307 => 559,  1302 => 557,  1299 => 556,  1294 => 554,  1291 => 553,  1289 => 552,  1285 => 550,  1280 => 548,  1277 => 547,  1272 => 545,  1269 => 544,  1267 => 543,  1259 => 540,  1254 => 537,  1248 => 535,  1246 => 534,  1238 => 531,  1230 => 528,  1222 => 525,  1213 => 521,  1207 => 517,  1202 => 515,  1197 => 514,  1192 => 512,  1187 => 511,  1185 => 510,  1179 => 507,  1173 => 503,  1167 => 502,  1159 => 500,  1151 => 498,  1148 => 497,  1144 => 496,  1138 => 493,  1132 => 489,  1126 => 488,  1118 => 486,  1110 => 484,  1107 => 483,  1103 => 482,  1097 => 479,  1089 => 476,  1084 => 473,  1079 => 471,  1076 => 470,  1071 => 468,  1068 => 467,  1066 => 466,  1060 => 462,  1055 => 460,  1052 => 459,  1047 => 457,  1044 => 456,  1042 => 455,  1036 => 451,  1031 => 449,  1028 => 448,  1023 => 446,  1020 => 445,  1018 => 444,  1012 => 440,  1007 => 438,  1004 => 437,  999 => 435,  996 => 434,  994 => 433,  988 => 429,  983 => 427,  980 => 426,  975 => 424,  972 => 423,  970 => 422,  964 => 418,  959 => 416,  956 => 415,  951 => 413,  948 => 412,  946 => 411,  940 => 407,  935 => 405,  932 => 404,  927 => 402,  924 => 401,  922 => 400,  916 => 396,  911 => 394,  908 => 393,  903 => 391,  900 => 390,  898 => 389,  892 => 385,  887 => 383,  884 => 382,  879 => 380,  876 => 379,  874 => 378,  868 => 374,  863 => 372,  860 => 371,  855 => 369,  852 => 368,  850 => 367,  844 => 363,  839 => 361,  836 => 360,  831 => 358,  828 => 357,  826 => 356,  820 => 352,  815 => 350,  812 => 349,  807 => 347,  804 => 346,  802 => 345,  796 => 341,  791 => 339,  788 => 338,  783 => 336,  780 => 335,  778 => 334,  772 => 330,  767 => 328,  764 => 327,  759 => 325,  756 => 324,  754 => 323,  748 => 319,  743 => 317,  740 => 316,  735 => 314,  732 => 313,  730 => 312,  724 => 308,  719 => 306,  716 => 305,  711 => 303,  708 => 302,  706 => 301,  698 => 296,  690 => 293,  685 => 290,  680 => 288,  677 => 287,  672 => 285,  669 => 284,  667 => 283,  661 => 279,  656 => 277,  653 => 276,  648 => 274,  645 => 273,  643 => 272,  637 => 268,  632 => 266,  629 => 265,  624 => 263,  621 => 262,  619 => 261,  613 => 257,  608 => 255,  605 => 254,  600 => 252,  597 => 251,  595 => 250,  589 => 246,  584 => 244,  581 => 243,  576 => 241,  573 => 240,  571 => 239,  565 => 235,  560 => 233,  557 => 232,  552 => 230,  549 => 229,  547 => 228,  541 => 224,  536 => 222,  533 => 221,  528 => 219,  525 => 218,  523 => 217,  517 => 213,  512 => 211,  509 => 210,  504 => 208,  501 => 207,  499 => 206,  493 => 202,  488 => 200,  485 => 199,  480 => 197,  477 => 196,  475 => 195,  469 => 191,  464 => 189,  461 => 188,  456 => 186,  453 => 185,  451 => 184,  445 => 180,  440 => 178,  437 => 177,  432 => 175,  429 => 174,  427 => 173,  421 => 169,  416 => 167,  413 => 166,  408 => 164,  405 => 163,  403 => 162,  397 => 158,  392 => 156,  389 => 155,  384 => 153,  381 => 152,  379 => 151,  373 => 147,  368 => 145,  365 => 144,  360 => 142,  357 => 141,  355 => 140,  349 => 136,  344 => 134,  341 => 133,  336 => 131,  333 => 130,  331 => 129,  325 => 125,  320 => 123,  317 => 122,  312 => 120,  309 => 119,  307 => 118,  301 => 114,  296 => 112,  293 => 111,  288 => 109,  285 => 108,  283 => 107,  277 => 103,  272 => 101,  269 => 100,  264 => 98,  261 => 97,  259 => 96,  253 => 92,  248 => 90,  245 => 89,  240 => 87,  237 => 86,  235 => 85,  229 => 81,  224 => 79,  221 => 78,  216 => 76,  213 => 75,  211 => 74,  205 => 70,  200 => 68,  197 => 67,  192 => 65,  189 => 64,  187 => 63,  181 => 59,  176 => 57,  173 => 56,  168 => 54,  165 => 53,  163 => 52,  155 => 47,  150 => 44,  144 => 42,  142 => 41,  136 => 40,  131 => 38,  126 => 35,  120 => 33,  118 => 32,  112 => 31,  107 => 29,  102 => 27,  96 => 24,  92 => 22,  84 => 18,  82 => 17,  76 => 13,  65 => 11,  61 => 10,  56 => 8,  50 => 7,  46 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/shipping/usps.twig", "");
    }
}

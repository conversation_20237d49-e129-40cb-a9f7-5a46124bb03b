<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/opc_qb.twig */
class __TwigTemplate_8efc7be5f9d09f3293219d6750325c2a75d135a0b9ca3c4727a04188fce73ce8 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo ($context["column_left"] ?? null);
        echo "

<div id=\"content\">
  <div class=\"page-header\">
    <div class=\"container-fluid\">
      <div class=\"pull-right\">
        ";
        // line 7
        if (($context["connect_button"] ?? null)) {
            // line 8
            echo "          <script
               type=\"text/javascript\"
               src=\"https://appcenter.intuit.com/Content/IA/intuit.ipp.anywhere-1.3.3.js\">
          </script>
          <script type=\"text/javascript\">
              var redirectUrl = '";
            // line 13
            echo ($context["authorizationRequestUrl"] ?? null);
            echo "';
              intuit.ipp.anywhere.setup({
                      grantUrl:  redirectUrl,
                      datasources: {
                           quickbooks : true,
                           payments : true
                     },
                      paymentOptions:{
                            intuitReferred : true
                     }
              });
          </script>
          <ipp:connectToIntuit></ipp:connectToIntuit>
        ";
        }
        // line 27
        echo "        <a href=\"";
        echo ($context["user_guide"] ?? null);
        echo "\" data-toggle=\"tooltip\" title=\"User Guide\" class=\"btn btn-primary\"><i class=\"fa fa-book\"></i></a>
        <button type=\"submit\" form=\"form-opc_qb\" data-toggle=\"tooltip\" title=\"";
        // line 28
        echo ($context["button_save"] ?? null);
        echo "\" class=\"btn btn-primary\"><i class=\"fa fa-save\"></i></button>
        <a href=\"";
        // line 29
        echo ($context["cancel"] ?? null);
        echo "\" data-toggle=\"tooltip\" title=\"";
        echo ($context["button_cancel"] ?? null);
        echo "\" class=\"btn btn-default\"><i class=\"fa fa-reply\"></i></a></div>
      <h1>";
        // line 30
        echo ($context["heading_title"] ?? null);
        echo "</h1>
      <ul class=\"breadcrumb\">
        ";
        // line 32
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["breadcrumbs"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["breadcrumb"]) {
            // line 33
            echo "        <li><a href=\"";
            echo (($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 = $context["breadcrumb"]) && is_array($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4) || $__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 instanceof ArrayAccess ? ($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4["href"] ?? null) : null);
            echo "\">";
            echo (($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 = $context["breadcrumb"]) && is_array($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144) || $__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 instanceof ArrayAccess ? ($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144["text"] ?? null) : null);
            echo "</a></li>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['breadcrumb'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 35
        echo "      </ul>
    </div>
  </div>
  <div class=\"container-fluid\">
    <div class=\"alert alert-info\"><i class=\"fa fa-exclamation-circle\"></i> ";
        // line 39
        echo ($context["text_create_account"] ?? null);
        echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>

    <div class=\"alert alert-info\"><i class=\"fa fa-exclamation-circle\"></i> ";
        // line 43
        echo ($context["text_create_keys"] ?? null);
        echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        // line 46
        if (($context["error_warning"] ?? null)) {
            // line 47
            echo "    <div class=\"alert alert-danger\"><i class=\"fa fa-exclamation-circle\"></i> ";
            echo ($context["error_warning"] ?? null);
            echo "
      <button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button>
    </div>
    ";
        }
        // line 51
        echo "    <div class=\"panel panel-default\">
      <div class=\"panel-heading\">
        <h3 class=\"panel-title\"><i class=\"fa fa-pencil\"></i> ";
        // line 53
        echo ($context["text_edit"] ?? null);
        echo "</h3>
      </div>
      <div class=\"panel-body\">
        <form action=\"";
        // line 56
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form-opc_qb\" class=\"form-horizontal\">
          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\">";
        // line 58
        echo ($context["entry_redirect_uri"] ?? null);
        echo "</label>
            <div class=\"col-sm-11\">
              <input type=\"text\" class=\"form-control\" value=\"";
        // line 60
        echo ($context["redirect_uri"] ?? null);
        echo "\" readonly>
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-status\">";
        // line 65
        echo ($context["entry_status"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_status\" id=\"input-status\" class=\"form-control\">
                ";
        // line 68
        if (($context["module_opc_qb_status"] ?? null)) {
            // line 69
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 70
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 72
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 73
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 75
        echo "              </select>
            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-sandbox\"><span data-toggle=\"tooltip\" title=\"";
        // line 78
        echo ($context["entry_sandbox_help"] ?? null);
        echo "\">";
        echo ($context["entry_sandbox"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_sandbox\" id=\"input-sandbox\" class=\"form-control\">
                ";
        // line 81
        if (($context["module_opc_qb_sandbox"] ?? null)) {
            // line 82
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 83
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 85
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 86
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 88
        echo "              </select>
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-order_mapping\"><span data-toggle=\"tooltip\" title=\"";
        // line 93
        echo ($context["entry_order_mapping_help"] ?? null);
        echo "\">";
        echo ($context["entry_order_mapping"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_order_mapping\" id=\"input-order_mapping\" class=\"form-control\">
                <option value=\"SalesReceipt\" ";
        // line 96
        if ((($context["module_opc_qb_order_mapping"] ?? null) == "SalesReceipt")) {
            echo " selected=\"selected\" ";
        }
        echo " >";
        echo ($context["text_sales"] ?? null);
        echo "</option>
                <option value=\"Invoice\" ";
        // line 97
        if ((($context["module_opc_qb_order_mapping"] ?? null) == "Invoice")) {
            echo " selected=\"selected\" ";
        }
        echo " >";
        echo ($context["text_invoice"] ?? null);
        echo "</option>
                <option value=\"Estimate\" ";
        // line 98
        if ((($context["module_opc_qb_order_mapping"] ?? null) == "Estimate")) {
            echo " selected=\"selected\" ";
        }
        echo " >";
        echo ($context["text_estimate"] ?? null);
        echo "</option>
                <option value=\"CreditMemo\" ";
        // line 99
        if ((($context["module_opc_qb_order_mapping"] ?? null) == "CreditMemo")) {
            echo " selected=\"selected\" ";
        }
        echo " >";
        echo ($context["text_credit"] ?? null);
        echo "</option>
              </select>
            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-slot\"><span data-toggle=\"tooltip\" title=\"";
        // line 103
        echo ($context["entry_slot_help"] ?? null);
        echo "\">";
        echo ($context["entry_slot"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <input type=\"number\" min=\"5\" max=\"50\" class=\"form-control\" placeholder=\"";
        // line 105
        echo ($context["entry_slot"] ?? null);
        echo "\" name=\"module_opc_qb_slot\" value=\"";
        echo ($context["module_opc_qb_slot"] ?? null);
        echo "\">
              ";
        // line 106
        if (($context["error_slot"] ?? null)) {
            // line 107
            echo "                <div class=\"text-danger\">";
            echo ($context["error_slot"] ?? null);
            echo "</div>
              ";
        }
        // line 109
        echo "            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-client_key\">";
        // line 113
        echo ($context["entry_client_key"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 115
        echo ($context["entry_client_key"] ?? null);
        echo "\" name=\"module_opc_qb_client_key\" value=\"";
        echo ($context["module_opc_qb_client_key"] ?? null);
        echo "\">
              ";
        // line 116
        if (($context["error_client_key"] ?? null)) {
            // line 117
            echo "                <div class=\"text-danger\">";
            echo ($context["error_client_key"] ?? null);
            echo "</div>
              ";
        }
        // line 119
        echo "            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-client_secret\">";
        // line 121
        echo ($context["entry_client_secret"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 123
        echo ($context["entry_client_secret"] ?? null);
        echo "\" name=\"module_opc_qb_client_secret\" value=\"";
        echo ($context["module_opc_qb_client_secret"] ?? null);
        echo "\">
              ";
        // line 124
        if (($context["error_client_secret"] ?? null)) {
            // line 125
            echo "                <div class=\"text-danger\">";
            echo ($context["error_client_secret"] ?? null);
            echo "</div>
              ";
        }
        // line 127
        echo "            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-access_token\">";
        // line 131
        echo ($context["entry_access_token"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 133
        echo ($context["entry_access_token"] ?? null);
        echo "\" name=\"module_opc_qb_access_token\" value=\"";
        echo ($context["module_opc_qb_access_token"] ?? null);
        echo "\">
              ";
        // line 134
        if (($context["error_access_token"] ?? null)) {
            // line 135
            echo "                <div class=\"text-danger\">";
            echo ($context["error_access_token"] ?? null);
            echo "</div>
              ";
        }
        // line 137
        echo "            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-refresh_token\">";
        // line 139
        echo ($context["entry_refresh_token"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 141
        echo ($context["entry_refresh_token"] ?? null);
        echo "\" name=\"module_opc_qb_refresh_token\" value=\"";
        echo ($context["module_opc_qb_refresh_token"] ?? null);
        echo "\">
              ";
        // line 142
        if (($context["error_refresh_token"] ?? null)) {
            // line 143
            echo "                <div class=\"text-danger\">";
            echo ($context["error_refresh_token"] ?? null);
            echo "</div>
              ";
        }
        // line 145
        echo "            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-realmid\">";
        // line 149
        echo ($context["entry_realmid"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 151
        echo ($context["entry_realmid"] ?? null);
        echo "\" name=\"module_opc_qb_realmid\" value=\"";
        echo ($context["module_opc_qb_realmid"] ?? null);
        echo "\">
              ";
        // line 152
        if (($context["error_realmid"] ?? null)) {
            // line 153
            echo "                <div class=\"text-danger\">";
            echo ($context["error_realmid"] ?? null);
            echo "</div>
              ";
        }
        // line 155
        echo "            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-auto_sync\"><span data-toggle=\"tooltip\" title=\"";
        // line 157
        echo ($context["entry_auto_sync_help"] ?? null);
        echo "\">";
        echo ($context["entry_auto_sync"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_auto_sync\" id=\"input-auto_sync\" class=\"form-control\">
                ";
        // line 160
        if (($context["module_opc_qb_auto_sync"] ?? null)) {
            // line 161
            echo "                <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\">";
            // line 162
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        } else {
            // line 164
            echo "                <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
                <option value=\"0\" selected=\"selected\">";
            // line 165
            echo ($context["text_disabled"] ?? null);
            echo "</option>
                ";
        }
        // line 167
        echo "              </select>
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-asset\">";
        // line 172
        echo ($context["entry_asset"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_asset\" id=\"input-asset\" class=\"form-control\">
                ";
        // line 175
        if ((twig_get_attribute($this->env, $this->source, ($context["accounts"] ?? null), "asset", [], "array", true, true, false, 175) && (($__internal_1cfccaec8dd2e8578ccb026fbe7f2e7e29ac2ed5deb976639c5fc99a6ea8583b = ($context["accounts"] ?? null)) && is_array($__internal_1cfccaec8dd2e8578ccb026fbe7f2e7e29ac2ed5deb976639c5fc99a6ea8583b) || $__internal_1cfccaec8dd2e8578ccb026fbe7f2e7e29ac2ed5deb976639c5fc99a6ea8583b instanceof ArrayAccess ? ($__internal_1cfccaec8dd2e8578ccb026fbe7f2e7e29ac2ed5deb976639c5fc99a6ea8583b["asset"] ?? null) : null))) {
            // line 176
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable((($__internal_68aa442c1d43d3410ea8f958ba9090f3eaa9a76f8de8fc9be4d6c7389ba28002 = ($context["accounts"] ?? null)) && is_array($__internal_68aa442c1d43d3410ea8f958ba9090f3eaa9a76f8de8fc9be4d6c7389ba28002) || $__internal_68aa442c1d43d3410ea8f958ba9090f3eaa9a76f8de8fc9be4d6c7389ba28002 instanceof ArrayAccess ? ($__internal_68aa442c1d43d3410ea8f958ba9090f3eaa9a76f8de8fc9be4d6c7389ba28002["asset"] ?? null) : null));
            foreach ($context['_seq'] as $context["_key"] => $context["asset"]) {
                // line 177
                echo "                    <option value=\"";
                echo (($__internal_d7fc55f1a54b629533d60b43063289db62e68921ee7a5f8de562bd9d4a2b7ad4 = $context["asset"]) && is_array($__internal_d7fc55f1a54b629533d60b43063289db62e68921ee7a5f8de562bd9d4a2b7ad4) || $__internal_d7fc55f1a54b629533d60b43063289db62e68921ee7a5f8de562bd9d4a2b7ad4 instanceof ArrayAccess ? ($__internal_d7fc55f1a54b629533d60b43063289db62e68921ee7a5f8de562bd9d4a2b7ad4["id"] ?? null) : null);
                echo "\" ";
                if ((($context["module_opc_qb_asset"] ?? null) == (($__internal_01476f8db28655ee4ee02ea2d17dd5a92599be76304f08cd8bc0e05aced30666 = $context["asset"]) && is_array($__internal_01476f8db28655ee4ee02ea2d17dd5a92599be76304f08cd8bc0e05aced30666) || $__internal_01476f8db28655ee4ee02ea2d17dd5a92599be76304f08cd8bc0e05aced30666 instanceof ArrayAccess ? ($__internal_01476f8db28655ee4ee02ea2d17dd5a92599be76304f08cd8bc0e05aced30666["id"] ?? null) : null))) {
                    echo "selected=\"selected\"";
                }
                echo ">";
                echo (($__internal_01c35b74bd85735098add188b3f8372ba465b232ab8298cb582c60f493d3c22e = $context["asset"]) && is_array($__internal_01c35b74bd85735098add188b3f8372ba465b232ab8298cb582c60f493d3c22e) || $__internal_01c35b74bd85735098add188b3f8372ba465b232ab8298cb582c60f493d3c22e instanceof ArrayAccess ? ($__internal_01c35b74bd85735098add188b3f8372ba465b232ab8298cb582c60f493d3c22e["name"] ?? null) : null);
                echo "</option>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['asset'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 179
            echo "                ";
        }
        // line 180
        echo "              </select>
            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-income\">";
        // line 183
        echo ($context["entry_income"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_income\" id=\"input-income\" class=\"form-control\">
                ";
        // line 186
        if ((twig_get_attribute($this->env, $this->source, ($context["accounts"] ?? null), "income", [], "array", true, true, false, 186) && (($__internal_63ad1f9a2bf4db4af64b010785e9665558fdcac0e8db8b5b413ed986c62dbb52 = ($context["accounts"] ?? null)) && is_array($__internal_63ad1f9a2bf4db4af64b010785e9665558fdcac0e8db8b5b413ed986c62dbb52) || $__internal_63ad1f9a2bf4db4af64b010785e9665558fdcac0e8db8b5b413ed986c62dbb52 instanceof ArrayAccess ? ($__internal_63ad1f9a2bf4db4af64b010785e9665558fdcac0e8db8b5b413ed986c62dbb52["income"] ?? null) : null))) {
            // line 187
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable((($__internal_f10a4cc339617934220127f034125576ed229e948660ebac906a15846d52f136 = ($context["accounts"] ?? null)) && is_array($__internal_f10a4cc339617934220127f034125576ed229e948660ebac906a15846d52f136) || $__internal_f10a4cc339617934220127f034125576ed229e948660ebac906a15846d52f136 instanceof ArrayAccess ? ($__internal_f10a4cc339617934220127f034125576ed229e948660ebac906a15846d52f136["income"] ?? null) : null));
            foreach ($context['_seq'] as $context["_key"] => $context["income"]) {
                // line 188
                echo "                    <option value=\"";
                echo (($__internal_887a873a4dc3cf8bd4f99c487b4c7727999c350cc3a772414714e49a195e4386 = $context["income"]) && is_array($__internal_887a873a4dc3cf8bd4f99c487b4c7727999c350cc3a772414714e49a195e4386) || $__internal_887a873a4dc3cf8bd4f99c487b4c7727999c350cc3a772414714e49a195e4386 instanceof ArrayAccess ? ($__internal_887a873a4dc3cf8bd4f99c487b4c7727999c350cc3a772414714e49a195e4386["id"] ?? null) : null);
                echo "\" ";
                if ((($context["module_opc_qb_income"] ?? null) == (($__internal_d527c24a729d38501d770b40a0d25e1ce8a7f0bff897cc4f8f449ba71fcff3d9 = $context["income"]) && is_array($__internal_d527c24a729d38501d770b40a0d25e1ce8a7f0bff897cc4f8f449ba71fcff3d9) || $__internal_d527c24a729d38501d770b40a0d25e1ce8a7f0bff897cc4f8f449ba71fcff3d9 instanceof ArrayAccess ? ($__internal_d527c24a729d38501d770b40a0d25e1ce8a7f0bff897cc4f8f449ba71fcff3d9["id"] ?? null) : null))) {
                    echo "selected=\"selected\"";
                }
                echo ">";
                echo (($__internal_f6dde3a1020453fdf35e718e94f93ce8eb8803b28cc77a665308e14bbe8572ae = $context["income"]) && is_array($__internal_f6dde3a1020453fdf35e718e94f93ce8eb8803b28cc77a665308e14bbe8572ae) || $__internal_f6dde3a1020453fdf35e718e94f93ce8eb8803b28cc77a665308e14bbe8572ae instanceof ArrayAccess ? ($__internal_f6dde3a1020453fdf35e718e94f93ce8eb8803b28cc77a665308e14bbe8572ae["name"] ?? null) : null);
                echo "</option>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['income'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 190
            echo "                ";
        }
        // line 191
        echo "              </select>
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-expense\">";
        // line 196
        echo ($context["entry_expense"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_expense\" id=\"input-expense\" class=\"form-control\">
                ";
        // line 199
        if ((twig_get_attribute($this->env, $this->source, ($context["accounts"] ?? null), "expense", [], "array", true, true, false, 199) && (($__internal_25c0fab8152b8dd6b90603159c0f2e8a936a09ab76edb5e4d7bc95d9a8d2dc8f = ($context["accounts"] ?? null)) && is_array($__internal_25c0fab8152b8dd6b90603159c0f2e8a936a09ab76edb5e4d7bc95d9a8d2dc8f) || $__internal_25c0fab8152b8dd6b90603159c0f2e8a936a09ab76edb5e4d7bc95d9a8d2dc8f instanceof ArrayAccess ? ($__internal_25c0fab8152b8dd6b90603159c0f2e8a936a09ab76edb5e4d7bc95d9a8d2dc8f["expense"] ?? null) : null))) {
            // line 200
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable((($__internal_f769f712f3484f00110c86425acea59f5af2752239e2e8596bcb6effeb425b40 = ($context["accounts"] ?? null)) && is_array($__internal_f769f712f3484f00110c86425acea59f5af2752239e2e8596bcb6effeb425b40) || $__internal_f769f712f3484f00110c86425acea59f5af2752239e2e8596bcb6effeb425b40 instanceof ArrayAccess ? ($__internal_f769f712f3484f00110c86425acea59f5af2752239e2e8596bcb6effeb425b40["expense"] ?? null) : null));
            foreach ($context['_seq'] as $context["_key"] => $context["expense"]) {
                // line 201
                echo "                    <option value=\"";
                echo (($__internal_98e944456c0f58b2585e4aa36e3a7e43f4b7c9038088f0f056004af41f4a007f = $context["expense"]) && is_array($__internal_98e944456c0f58b2585e4aa36e3a7e43f4b7c9038088f0f056004af41f4a007f) || $__internal_98e944456c0f58b2585e4aa36e3a7e43f4b7c9038088f0f056004af41f4a007f instanceof ArrayAccess ? ($__internal_98e944456c0f58b2585e4aa36e3a7e43f4b7c9038088f0f056004af41f4a007f["id"] ?? null) : null);
                echo "\" ";
                if ((($context["module_opc_qb_expense"] ?? null) == (($__internal_a06a70691a7ca361709a372174fa669f5ee1c1e4ed302b3a5b61c10c80c02760 = $context["expense"]) && is_array($__internal_a06a70691a7ca361709a372174fa669f5ee1c1e4ed302b3a5b61c10c80c02760) || $__internal_a06a70691a7ca361709a372174fa669f5ee1c1e4ed302b3a5b61c10c80c02760 instanceof ArrayAccess ? ($__internal_a06a70691a7ca361709a372174fa669f5ee1c1e4ed302b3a5b61c10c80c02760["id"] ?? null) : null))) {
                    echo "selected=\"selected\"";
                }
                echo ">";
                echo (($__internal_653499042eb14fd8415489ba6fa87c1e85cff03392e9f57b26d0da09b9be82ce = $context["expense"]) && is_array($__internal_653499042eb14fd8415489ba6fa87c1e85cff03392e9f57b26d0da09b9be82ce) || $__internal_653499042eb14fd8415489ba6fa87c1e85cff03392e9f57b26d0da09b9be82ce instanceof ArrayAccess ? ($__internal_653499042eb14fd8415489ba6fa87c1e85cff03392e9f57b26d0da09b9be82ce["name"] ?? null) : null);
                echo "</option>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['expense'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 203
            echo "                ";
        }
        // line 204
        echo "              </select>
            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-discount\">";
        // line 207
        echo ($context["entry_discount"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_discount\" id=\"input-discount\" class=\"form-control\">
                ";
        // line 210
        if ((twig_get_attribute($this->env, $this->source, ($context["accounts"] ?? null), "discount", [], "array", true, true, false, 210) && (($__internal_ba9f0a3bb95c082f61c9fbf892a05514d732703d52edc77b51f2e6284135900b = ($context["accounts"] ?? null)) && is_array($__internal_ba9f0a3bb95c082f61c9fbf892a05514d732703d52edc77b51f2e6284135900b) || $__internal_ba9f0a3bb95c082f61c9fbf892a05514d732703d52edc77b51f2e6284135900b instanceof ArrayAccess ? ($__internal_ba9f0a3bb95c082f61c9fbf892a05514d732703d52edc77b51f2e6284135900b["discount"] ?? null) : null))) {
            // line 211
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable((($__internal_73db8eef4d2582468dab79a6b09c77ce3b48675a610afd65a1f325b68804a60c = ($context["accounts"] ?? null)) && is_array($__internal_73db8eef4d2582468dab79a6b09c77ce3b48675a610afd65a1f325b68804a60c) || $__internal_73db8eef4d2582468dab79a6b09c77ce3b48675a610afd65a1f325b68804a60c instanceof ArrayAccess ? ($__internal_73db8eef4d2582468dab79a6b09c77ce3b48675a610afd65a1f325b68804a60c["discount"] ?? null) : null));
            foreach ($context['_seq'] as $context["_key"] => $context["discount"]) {
                // line 212
                echo "                    <option value=\"";
                echo (($__internal_d8ad5934f1874c52fa2ac9a4dfae52038b39b8b03cfc82eeb53de6151d883972 = $context["discount"]) && is_array($__internal_d8ad5934f1874c52fa2ac9a4dfae52038b39b8b03cfc82eeb53de6151d883972) || $__internal_d8ad5934f1874c52fa2ac9a4dfae52038b39b8b03cfc82eeb53de6151d883972 instanceof ArrayAccess ? ($__internal_d8ad5934f1874c52fa2ac9a4dfae52038b39b8b03cfc82eeb53de6151d883972["id"] ?? null) : null);
                echo "\" ";
                if ((($context["module_opc_qb_discount"] ?? null) == (($__internal_df39c71428eaf37baa1ea2198679e0077f3699bdd31bb5ba10d084710b9da216 = $context["discount"]) && is_array($__internal_df39c71428eaf37baa1ea2198679e0077f3699bdd31bb5ba10d084710b9da216) || $__internal_df39c71428eaf37baa1ea2198679e0077f3699bdd31bb5ba10d084710b9da216 instanceof ArrayAccess ? ($__internal_df39c71428eaf37baa1ea2198679e0077f3699bdd31bb5ba10d084710b9da216["id"] ?? null) : null))) {
                    echo "selected=\"selected\"";
                }
                echo ">";
                echo (($__internal_bf0e189d688dc2ad611b50a437a32d3692fb6b8be90d2228617cfa6db44e75c0 = $context["discount"]) && is_array($__internal_bf0e189d688dc2ad611b50a437a32d3692fb6b8be90d2228617cfa6db44e75c0) || $__internal_bf0e189d688dc2ad611b50a437a32d3692fb6b8be90d2228617cfa6db44e75c0 instanceof ArrayAccess ? ($__internal_bf0e189d688dc2ad611b50a437a32d3692fb6b8be90d2228617cfa6db44e75c0["name"] ?? null) : null);
                echo "</option>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['discount'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 214
            echo "                ";
        }
        // line 215
        echo "              </select>
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-tax\">";
        // line 220
        echo ($context["entry_tax"] ?? null);
        echo "</label>
            <div class=\"col-sm-5\">
              <select name=\"module_opc_qb_tax\" id=\"input-tax\" class=\"form-control\">
                ";
        // line 223
        if ((twig_get_attribute($this->env, $this->source, ($context["accounts"] ?? null), "tax", [], "array", true, true, false, 223) && (($__internal_674c0abf302105af78b0a38907d86c5dd0028bdc3ee5f24bf52771a16487760c = ($context["accounts"] ?? null)) && is_array($__internal_674c0abf302105af78b0a38907d86c5dd0028bdc3ee5f24bf52771a16487760c) || $__internal_674c0abf302105af78b0a38907d86c5dd0028bdc3ee5f24bf52771a16487760c instanceof ArrayAccess ? ($__internal_674c0abf302105af78b0a38907d86c5dd0028bdc3ee5f24bf52771a16487760c["tax"] ?? null) : null))) {
            // line 224
            echo "                  ";
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable((($__internal_dd839fbfcab68823c49af471c7df7659a500fe72e71b58d6b80d896bdb55e75f = ($context["accounts"] ?? null)) && is_array($__internal_dd839fbfcab68823c49af471c7df7659a500fe72e71b58d6b80d896bdb55e75f) || $__internal_dd839fbfcab68823c49af471c7df7659a500fe72e71b58d6b80d896bdb55e75f instanceof ArrayAccess ? ($__internal_dd839fbfcab68823c49af471c7df7659a500fe72e71b58d6b80d896bdb55e75f["tax"] ?? null) : null));
            foreach ($context['_seq'] as $context["_key"] => $context["tax"]) {
                // line 225
                echo "                    <option value=\"";
                echo (($__internal_a7ed47878554bdc32b70e1ba5ccc67d2302196876fbf62b4c853b20cb9e029fc = $context["tax"]) && is_array($__internal_a7ed47878554bdc32b70e1ba5ccc67d2302196876fbf62b4c853b20cb9e029fc) || $__internal_a7ed47878554bdc32b70e1ba5ccc67d2302196876fbf62b4c853b20cb9e029fc instanceof ArrayAccess ? ($__internal_a7ed47878554bdc32b70e1ba5ccc67d2302196876fbf62b4c853b20cb9e029fc["id"] ?? null) : null);
                echo "\" ";
                if ((($context["module_opc_qb_tax"] ?? null) == (($__internal_e5d7b41e16b744b68da1e9bb49047b8028ced86c782900009b4b4029b83d4b55 = $context["tax"]) && is_array($__internal_e5d7b41e16b744b68da1e9bb49047b8028ced86c782900009b4b4029b83d4b55) || $__internal_e5d7b41e16b744b68da1e9bb49047b8028ced86c782900009b4b4029b83d4b55 instanceof ArrayAccess ? ($__internal_e5d7b41e16b744b68da1e9bb49047b8028ced86c782900009b4b4029b83d4b55["id"] ?? null) : null))) {
                    echo "selected=\"selected\"";
                }
                echo ">";
                echo (($__internal_9e93f398968fa0576dce82fd00f280e95c734ad3f84e7816ff09158ae224f5ba = $context["tax"]) && is_array($__internal_9e93f398968fa0576dce82fd00f280e95c734ad3f84e7816ff09158ae224f5ba) || $__internal_9e93f398968fa0576dce82fd00f280e95c734ad3f84e7816ff09158ae224f5ba instanceof ArrayAccess ? ($__internal_9e93f398968fa0576dce82fd00f280e95c734ad3f84e7816ff09158ae224f5ba["name"] ?? null) : null);
                echo "</option>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['tax'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 227
            echo "                ";
        }
        // line 228
        echo "              </select>
            </div>

            <label class=\"col-sm-1 control-label\" for=\"input-transaction_prefix\"><span data-toggle=\"tooltip\" title=\"";
        // line 231
        echo ($context["entry_transaction_prefix_help"] ?? null);
        echo "\">";
        echo ($context["entry_transaction_prefix"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 233
        echo ($context["entry_transaction_prefix"] ?? null);
        echo "\" name=\"module_opc_qb_transaction_prefix\" value=\"";
        echo ($context["module_opc_qb_transaction_prefix"] ?? null);
        echo "\">
            </div>
          </div>

          <div class=\"form-group\">
            <label class=\"col-sm-1 control-label\" for=\"input-order_date\">";
        // line 238
        echo ($context["entry_order_date"] ?? null);
        echo "</label>
            <div class=\"col-sm-5 date\" style=\"display: inline-table;\">
              <input type=\"text\" class=\"form-control\" placeholder=\"";
        // line 240
        echo ($context["entry_order_date"] ?? null);
        echo "\" name=\"module_opc_qb_date\" value=\"";
        echo ($context["module_opc_qb_date"] ?? null);
        echo "\" data-date-format=\"YYYY-MM-DD\">
              <span class=\"input-group-btn\">
              <button type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
              </span>
            </div>
            
            <label class=\"col-sm-1 control-label\" for=\"input-process-status\"><span data-toggle=\"tooltip\" title=\"";
        // line 246
        echo ($context["help_order_status"] ?? null);
        echo "\">";
        echo ($context["entry_order_status"] ?? null);
        echo "</span></label>
            <div class=\"col-sm-5\">
              <div class=\"well well-sm\" style=\"height: 150px; overflow: auto;\">
                ";
        // line 249
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["order_statuses"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["order_status"]) {
            // line 250
            echo "                <div class=\"checkbox\">
                  <label>
                    ";
            // line 252
            if (((array_key_exists("module_opc_qb_order_status", $context) && twig_test_iterable(($context["module_opc_qb_order_status"] ?? null))) && twig_in_filter((($__internal_0795e3de58b6454b051261c0c2b5be48852e17f25b59d4aeef29fb07c614bd78 = $context["order_status"]) && is_array($__internal_0795e3de58b6454b051261c0c2b5be48852e17f25b59d4aeef29fb07c614bd78) || $__internal_0795e3de58b6454b051261c0c2b5be48852e17f25b59d4aeef29fb07c614bd78 instanceof ArrayAccess ? ($__internal_0795e3de58b6454b051261c0c2b5be48852e17f25b59d4aeef29fb07c614bd78["order_status_id"] ?? null) : null), ($context["module_opc_qb_order_status"] ?? null)))) {
                // line 253
                echo "                    <input type=\"checkbox\" name=\"module_opc_qb_order_status[]\" value=\"";
                echo (($__internal_fecb0565c93d0b979a95c352ff76e401e0ae0c73bb8d3b443c8c6133e1c190de = $context["order_status"]) && is_array($__internal_fecb0565c93d0b979a95c352ff76e401e0ae0c73bb8d3b443c8c6133e1c190de) || $__internal_fecb0565c93d0b979a95c352ff76e401e0ae0c73bb8d3b443c8c6133e1c190de instanceof ArrayAccess ? ($__internal_fecb0565c93d0b979a95c352ff76e401e0ae0c73bb8d3b443c8c6133e1c190de["order_status_id"] ?? null) : null);
                echo "\" checked=\"checked\" />
                    ";
                // line 254
                echo (($__internal_87570a635eac7f6e150744bd218085d17aff15d92d9c80a66d3b911e3355b828 = $context["order_status"]) && is_array($__internal_87570a635eac7f6e150744bd218085d17aff15d92d9c80a66d3b911e3355b828) || $__internal_87570a635eac7f6e150744bd218085d17aff15d92d9c80a66d3b911e3355b828 instanceof ArrayAccess ? ($__internal_87570a635eac7f6e150744bd218085d17aff15d92d9c80a66d3b911e3355b828["name"] ?? null) : null);
                echo "
                    ";
            } else {
                // line 256
                echo "                    <input type=\"checkbox\" name=\"module_opc_qb_order_status[]\" value=\"";
                echo (($__internal_17b5b5f9aaeec4b528bfeed02b71f624021d6a52d927f441de2f2204d0e527cd = $context["order_status"]) && is_array($__internal_17b5b5f9aaeec4b528bfeed02b71f624021d6a52d927f441de2f2204d0e527cd) || $__internal_17b5b5f9aaeec4b528bfeed02b71f624021d6a52d927f441de2f2204d0e527cd instanceof ArrayAccess ? ($__internal_17b5b5f9aaeec4b528bfeed02b71f624021d6a52d927f441de2f2204d0e527cd["order_status_id"] ?? null) : null);
                echo "\" />
                    ";
                // line 257
                echo (($__internal_0db9a23306660395861a0528381e0668025e56a8a99f399e9ec23a4b392422d6 = $context["order_status"]) && is_array($__internal_0db9a23306660395861a0528381e0668025e56a8a99f399e9ec23a4b392422d6) || $__internal_0db9a23306660395861a0528381e0668025e56a8a99f399e9ec23a4b392422d6 instanceof ArrayAccess ? ($__internal_0db9a23306660395861a0528381e0668025e56a8a99f399e9ec23a4b392422d6["name"] ?? null) : null);
                echo "
                    ";
            }
            // line 259
            echo "                  </label>
                </div>
                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['order_status'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 262
        echo "              </div>
              <a onclick=\"\$(this).parent().find(':checkbox').prop('checked', true);\">";
        // line 263
        echo ($context["text_select_all"] ?? null);
        echo "</a> / <a onclick=\"\$(this).parent().find(':checkbox').prop('checked', false);\">";
        echo ($context["text_unselect_all"] ?? null);
        echo "</a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script src=\"view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.js\" type=\"text/javascript\"></script>
<link href=\"view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.css\" type=\"text/css\" rel=\"stylesheet\" media=\"screen\" />
<script type=\"text/javascript\"><!--
\$('.date').datetimepicker({
  pickTime: false
});
//--></script>
";
        // line 278
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/module/opc_qb.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  747 => 278,  727 => 263,  724 => 262,  716 => 259,  711 => 257,  706 => 256,  701 => 254,  696 => 253,  694 => 252,  690 => 250,  686 => 249,  678 => 246,  667 => 240,  662 => 238,  652 => 233,  645 => 231,  640 => 228,  637 => 227,  622 => 225,  617 => 224,  615 => 223,  609 => 220,  602 => 215,  599 => 214,  584 => 212,  579 => 211,  577 => 210,  571 => 207,  566 => 204,  563 => 203,  548 => 201,  543 => 200,  541 => 199,  535 => 196,  528 => 191,  525 => 190,  510 => 188,  505 => 187,  503 => 186,  497 => 183,  492 => 180,  489 => 179,  474 => 177,  469 => 176,  467 => 175,  461 => 172,  454 => 167,  449 => 165,  444 => 164,  439 => 162,  434 => 161,  432 => 160,  424 => 157,  420 => 155,  414 => 153,  412 => 152,  406 => 151,  401 => 149,  395 => 145,  389 => 143,  387 => 142,  381 => 141,  376 => 139,  372 => 137,  366 => 135,  364 => 134,  358 => 133,  353 => 131,  347 => 127,  341 => 125,  339 => 124,  333 => 123,  328 => 121,  324 => 119,  318 => 117,  316 => 116,  310 => 115,  305 => 113,  299 => 109,  293 => 107,  291 => 106,  285 => 105,  278 => 103,  267 => 99,  259 => 98,  251 => 97,  243 => 96,  235 => 93,  228 => 88,  223 => 86,  218 => 85,  213 => 83,  208 => 82,  206 => 81,  198 => 78,  193 => 75,  188 => 73,  183 => 72,  178 => 70,  173 => 69,  171 => 68,  165 => 65,  157 => 60,  152 => 58,  147 => 56,  141 => 53,  137 => 51,  129 => 47,  127 => 46,  121 => 43,  114 => 39,  108 => 35,  97 => 33,  93 => 32,  88 => 30,  82 => 29,  78 => 28,  73 => 27,  56 => 13,  49 => 8,  47 => 7,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/opc_qb.twig", "");
    }
}

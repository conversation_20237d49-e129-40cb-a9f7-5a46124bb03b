<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/module/excelport/tab_support.twig */
class __TwigTemplate_2076277ffc4816ff6b2a67019ffae3b73fd065b22e39a51c2b3b19c76ead12eb extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<div class=\"row\">
    <div class=\"col-md-4\">
        <div class=\"panel\">
            <div class=\"panel-heading\">
                <h3 class=\"panel-title\"><i class=\"fa fa-user\"></i> ";
        // line 5
        echo ($context["license_your_license"] ?? null);
        echo "</h3>
            </div>
            <div class=\"panel-body\">
            ";
        // line 8
        if ( !twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 8), "LicensedOn", [], "any", false, false, false, 8)) {
            // line 9
            echo "                <div class=\"form-group\">
                    <label for=\"moduleLicense\">";
            // line 10
            echo ($context["license_enter_code"] ?? null);
            echo "</label>
                    <div class=\"licenseAlerts\"></div>
                    <div class=\"licenseDiv\"></div>
                    <input type=\"text\" class=\"licenseCodeBox form-control\" placeholder=\"";
            // line 13
            echo ($context["license_placeholder"] ?? null);
            echo "\" value=\"";
            echo (($context["license_code"]) ?? (""));
            echo "\" />
                </div>

                <div class=\"form-group\">
                    <button type=\"button\" class=\"btn btn-success btnActivateLicense\"><i class=\"icon-ok\"></i> ";
            // line 17
            echo ($context["license_activate"] ?? null);
            echo "</button>
                    <div class=\"pull-right\">
                        <button type=\"button\" class=\"btn btn-link small-link\" onclick=\"window.open('http://isenselabs.com/users/purchases/')\">";
            // line 19
            echo ($context["license_get_code"] ?? null);
            echo " <i class=\"fa fa-external-link\"></i></button>
                    </div>
                </div>
                <script type=\"text/javascript\">
                    var domainraw = location.protocol + '//' + location.host;
                    var domain = btoa(domainraw);
                    var timenow = ";
            // line 25
            echo ($context["now"] ?? null);
            echo ";
                    var MID = 'TBY2PJCCI7';
                </script>
                <script type=\"text/javascript\" src=\"//isenselabs.com/external/validate/\"></script>
            ";
        }
        // line 30
        echo "
            ";
        // line 31
        if (twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 31), "LicensedOn", [], "any", false, false, false, 31)) {
            // line 32
            echo "                <input name=\"cHRpbWl6YXRpb24ef4fe\" type=\"hidden\" value=\"";
            echo ($context["licenseEncoded"] ?? null);
            echo "\" />
                <input name=\"OaXRyb1BhY2sgLSBDb21\" type=\"hidden\" value=\"";
            // line 33
            echo twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 33), "LicensedOn", [], "any", false, false, false, 33);
            echo "\" />

                <div class=\"row\">
                    <label class=\"license_label\">";
            // line 36
            echo ($context["license_holder"] ?? null);
            echo "</label>
                    <span class=\"license_info\">";
            // line 37
            echo twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 37), "License", [], "any", false, false, false, 37), "customerName", [], "any", false, false, false, 37);
            echo "</span>
                </div>
                <div class=\"row\">
                    <label class=\"license_label\">";
            // line 40
            echo ($context["license_registered_domains"] ?? null);
            echo "</label>
                    <ul class=\"license_info\">
                        ";
            // line 42
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, ($context["data"] ?? null), "ExcelPort", [], "any", false, false, false, 42), "License", [], "any", false, false, false, 42), "licenseDomainsUsed", [], "any", false, false, false, 42));
            foreach ($context['_seq'] as $context["_key"] => $context["domain"]) {
                // line 43
                echo "                            <li><i class=\"fa fa-check\"></i>&nbsp;";
                echo $context["domain"];
                echo "</li>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['domain'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 45
            echo "                    </ul>
                </div>
                <div class=\"row\">
                    <label class=\"license_label\">";
            // line 48
            echo ($context["license_expires"] ?? null);
            echo "</label>
                    <span class=\"license_info\">";
            // line 49
            echo ($context["expiration_date"] ?? null);
            echo "</span>

                    <div class=\"alert alert-success center\">";
            // line 51
            echo ($context["license_valid"] ?? null);
            echo " [<a href=\"http://isenselabs.com/users/purchases\" target=\"_blank\">";
            echo ($context["license_manage"] ?? null);
            echo "</a>]</div>
                </div>
            ";
        }
        // line 54
        echo "            </div>
        </div>
    </div>

    <div class=\"col-md-8\">
        <div class=\"panel\">
            <div class=\"panel-heading\">
                <h3 class=\"panel-title\"><i class=\"fa fa-users\"></i> ";
        // line 61
        echo ($context["license_get_support"] ?? null);
        echo "</h3>
            </div>
            <div class=\"panel-body\">
                <div class=\"row\">
                    <div class=\"col-md-4\">
                        <div class=\"thumbnail\">
                            <img alt=\"";
        // line 67
        echo ($context["license_community"] ?? null);
        echo "\" src=\"view/image/excelport/community.png\">
                            <div class=\"caption center\">
                                <h3>";
        // line 69
        echo ($context["license_community"] ?? null);
        echo "</h3>
                                <p>";
        // line 70
        echo ($context["license_community_info"] ?? null);
        echo "</p>
                                <p style=\"padding-top: 5px;\"><a href=\"http://isenselabs.com/forum\" target=\"_blank\" class=\"btn btn-lg btn-default\">";
        // line 71
        echo ($context["license_forums"] ?? null);
        echo "</a></p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-md-4\">
                        <div class=\"thumbnail\">
                            <img data-src=\"holder.js/300x200\" alt=\"Ticket support\" src=\"view/image/excelport/tickets.png\">
                            <div class=\"caption center\">
                                <h3>";
        // line 79
        echo ($context["license_tickets"] ?? null);
        echo "</h3>
                                <p>";
        // line 80
        echo ($context["license_tickets_info"] ?? null);
        echo "</p>
                                <p style=\"padding-top: 5px;\"><a href=\"http://isenselabs.com/tickets/open/";
        // line 81
        echo ($context["support_path"] ?? null);
        echo "\" target=\"_blank\" class=\"btn btn-lg btn-default\">";
        echo ($context["license_tickets_open"] ?? null);
        echo "</a></p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-md-4\">
                        <div class=\"thumbnail\">
                            <img alt=\"";
        // line 87
        echo ($context["license_presale"] ?? null);
        echo "\" src=\"view/image/excelport/pre-sale.png\">
                            <div class=\"caption center\">
                                <h3>";
        // line 89
        echo ($context["license_presale"] ?? null);
        echo "</h3>
                                <p>";
        // line 90
        echo ($context["license_presale_info"] ?? null);
        echo "</p>
                                <p style=\"padding-top: 5px;\"><a href=\"https://isenselabs.com/pages/premium-services\" target=\"_blank\" class=\"btn btn-lg btn-default\">";
        // line 91
        echo ($context["license_presale_bump"] ?? null);
        echo "</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/module/excelport/tab_support.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  231 => 91,  227 => 90,  223 => 89,  218 => 87,  207 => 81,  203 => 80,  199 => 79,  188 => 71,  184 => 70,  180 => 69,  175 => 67,  166 => 61,  157 => 54,  149 => 51,  144 => 49,  140 => 48,  135 => 45,  126 => 43,  122 => 42,  117 => 40,  111 => 37,  107 => 36,  101 => 33,  96 => 32,  94 => 31,  91 => 30,  83 => 25,  74 => 19,  69 => 17,  60 => 13,  54 => 10,  51 => 9,  49 => 8,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/module/excelport/tab_support.twig", "");
    }
}

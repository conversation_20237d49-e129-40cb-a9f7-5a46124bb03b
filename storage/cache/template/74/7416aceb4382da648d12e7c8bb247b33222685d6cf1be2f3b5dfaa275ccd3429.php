<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/options.twig */
class __TwigTemplate_66c375d99f1a5ba731786feecaeeaa01dd27cb450f770a9a75be6b94393de5f2 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div>
  <div class=\"row\">
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-language\">";
        // line 6
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t\t<select class=\"form-control\" name=\"filter_language_id\">
\t\t ";
        // line 8
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 9
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 9);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 9);
            echo "</option>
\t\t  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 11
        echo "\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Limit (Note:Export Data limit)</label>
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_start\" value=\"0\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\"/> -
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_limit\" value=\"";
        // line 18
        echo ($context["filter_limit"] ?? null);
        echo "\" placeholder=\"Limit\" id=\"input-limit\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label> </label>
\t\t<button type=\"button\" id=\"buttonoptions\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> Export </button>
\t  </div>
\t</div>
  </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/options.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  73 => 18,  64 => 11,  53 => 9,  49 => 8,  44 => 6,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/options.twig", "/home/<USER>/public_html/admin/view/template/extension/export/options.twig");
    }
}

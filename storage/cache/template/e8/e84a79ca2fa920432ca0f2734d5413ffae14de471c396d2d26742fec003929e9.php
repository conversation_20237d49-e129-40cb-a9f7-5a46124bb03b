<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/options.twig */
class __TwigTemplate_017107761f54045cd2b28acd4241d7aa2e3480e99cef5b865958bf6421ec33ee extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["optionsaction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form_optionsimportx\" class=\"form-horizontal\">

\t<div class=\"row\">
\t\t
\t\t<!-- Coupon Group File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["entry_optionimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t
\t\t
\t\t<!-- Password and Import Button -->
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label>";
        // line 20
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t\t\t<select class=\"form-control\" name=\"language_id\">
\t\t\t\t";
        // line 22
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 23
            echo "\t\t\t\t<option value=\"";
            echo (($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 = $context["language"]) && is_array($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4) || $__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 instanceof ArrayAccess ? ($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4["language_id"] ?? null) : null);
            echo "\">";
            echo (($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 = $context["language"]) && is_array($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144) || $__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 instanceof ArrayAccess ? ($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144["name"] ?? null) : null);
            echo "</option>
\t\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 25
        echo "\t\t\t</select>
\t\t\t<i>Import your products according to Language.</i>
\t\t</div>
\t\t
\t\t<div class=\"clearfix\"></div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#form_optionsimportx').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t</div>
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/options.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  84 => 25,  73 => 23,  69 => 22,  64 => 20,  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/options.twig", "/home/<USER>/public_html/admin/view/template/extension/import/options.twig");
    }
}

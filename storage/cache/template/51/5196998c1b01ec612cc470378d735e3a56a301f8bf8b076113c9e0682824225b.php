<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/upsmodule/about.twig */
class __TwigTemplate_f1784a37d5ff48f5a83ab4433ff47a603d8a3432b45f0548cd034224213e21bd extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo ($context["header"] ?? null);
        echo " ";
        echo ($context["column_left"] ?? null);
        echo "
<div id=\"content\">
    <div class=\"page-header\">
        <div class=\"container-fluid\">
            <h1 class=\"logo\">";
        // line 5
        echo ($context["text_UPS_Shipping_Module"] ?? null);
        echo "</h1>
            <ul class=\"breadcrumb\">
                <li><a href=\"";
        // line 7
        echo ($context["home"] ?? null);
        echo "\">";
        echo ($context["text_home"] ?? null);
        echo "</a></li>
                <li><a href=\"";
        // line 8
        echo ($context["about"] ?? null);
        echo "\">";
        echo ($context["text_About_UPS"] ?? null);
        echo "</a></li>
            </ul>
        </div>
        <div class=\"container-fluid\">
                <div class=\"panel panel-default\">
                <div class=\"panel-heading\">
                    <h3 class=\"panel-title\">";
        // line 14
        echo ($context["text_About_UPS"] ?? null);
        echo "</h3>
                </div>
                <div class=\"panel-body\">
                    <form action=\"";
        // line 17
        echo ($context["action"] ?? null);
        echo "\" method=\"post\" id=\"update-bill\" class=\"form-horizontal\">
                        <input type=\"hidden\" name=\"method\" value=\"\" id=\"save-next\">
                        <h5 class=\"content-bold\">";
        // line 19
        echo ($context["text_About_Version"] ?? null);
        echo "</h5>
                        <p>";
        // line 20
        echo ($context["text_module_version"] ?? null);
        echo " v";
        echo ($context["text_Version"] ?? null);
        echo "<br></p>
                        <p>";
        // line 21
        echo ($context["text_Release"] ?? null);
        echo " Mar 09, 2023<br></p>
                        <h5 class=\"content-bold\">";
        // line 22
        echo ($context["text_About_Changelog"] ?? null);
        echo "</h5>
                        <p>Minor BugFixes</p>
                        <h5 class=\"content-bold\">";
        // line 24
        echo ($context["text_About_Ver_Platform"] ?? null);
        echo "</h5>
                        <p>";
        // line 25
        echo ($context["text_Ver_Platform"] ?? null);
        echo "<br></p>
                        <h5 class=\"content-bold\">";
        // line 26
        echo ($context["text_About_Blacklink"] ?? null);
        echo "</h5>
                        <p>";
        // line 27
        echo ($context["text_Blacklink"] ?? null);
        echo "</p>
                        <h5 class=\"content-bold\">";
        // line 28
        echo ($context["text_About_Support"] ?? null);
        echo "</h5>
                        <p>";
        // line 29
        echo ($context["text_Docs"] ?? null);
        echo "<a href=\"https://support.ecommerce.help/hc/en-us/sections/4405591762193-Official-Extension-for-OpenCart-\" target=\"_blank\">";
        echo ($context["text_Docs_link"] ?? null);
        echo "</a>.</p>
                        <p>";
        // line 30
        echo ($context["text_non_tech_Support"] ?? null);
        echo "</p>
                        <p>";
        // line 31
        echo ($context["text_Support"] ?? null);
        echo "</p>
                        <h5 class=\"content-bold\">Download Log</h5>
                        <p>Get API logs by clicking the button <button class=\"btn btn-info\" name=\"ups_dwnld_api_log\">Download log</button></p>
                        ";
        // line 75
        echo "                    </form>
                    ";
        // line 76
        echo ($context["ups_footer"] ?? null);
        echo "
                </div>
            </div>
        </div>
    </div>
    <div class=\"modal fade\" id=\"log-api-detail\" role=\"dialog\">
        <div class=\"modal-dialog\">
            <div class=\"modal-content\">
                <div class=\"modal-header\">
                    <button type=\"button\" class=\"close\" data-dismiss=\"modal\">&times;</button>
                    <label class=\"modal-title\"><image class=\"popup-logo\" src=\"view/image/upsmodule/ups_logo.svg\" />Log API Detail</label>
                </div>
                <div class=\"modal-body scroll-popup\">
                    <pre id=\"log-api-content\"></pre>
                </div>
                <div class=\"modal-footer\">
                    <button type=\"button\" class=\"btn btn-primary\" data-dismiss=\"modal\">";
        // line 92
        echo ($context["button_ok"] ?? null);
        echo "</button>
                </div>
            </div>
        </div>
    </div>
</div>
    <script type=\"text/javascript\" src=\"view/javascript/summernote/summernote.js\"></script>
    <link href=\"view/javascript/summernote/summernote.css\" rel=\"stylesheet\" />
    <!--  Disabled by Power Image Manager. Moved in the footer; <script type=\"text/javascript\" src=\"view/javascript/summernote/opencart.js\"></script> -->
    <link rel=\"stylesheet\" type=\"text/css\" href=\"view/stylesheet/upsmodule/billingpreference.css\" />
    <link rel=\"stylesheet\" type=\"text/css\" href=\"view/stylesheet/upsmodule/common.css\" />
    <script type=\"text/javascript\" src=\"view/javascript/upsmodule/common.js\"></script>
    <script type=\"text/javascript\" src=\"view/javascript/upsmodule/billingreference.js\"></script>
    <script type=\"text/javascript\">
        \$(document).ready(function () {
            \$(document).on('click', '#log-api-request a', function() {
                var id = \$(this).parent().parent().find(\".log-api-id\").attr(\"value\");
                showLogContent(id, \"request\");
            });

            \$(document).on('click', '#log-api-response a', function() {
                var id = \$(this).parent().parent().find(\".log-api-id\").attr(\"value\");
                showLogContent(id, \"response\");
            });

            \$(document).on('click', '#log-api-url a', function() {
                var id = \$(this).parent().parent().find(\".log-api-id\").attr(\"value\");
                showLogContent(id, \"response\");
            });

            function showLogContent (id, column) {
                \$.ajax(
                    {
                        type: 'POST',
                        url: \"";
        // line 126
        echo ($context["link"] ?? null);
        echo "\",
                        data: {
                            'id': id,
                        },
                        dataType: 'json',
                        success: function (data) {
                            var show_data = '';
                            if (data.content[column].length > 0) {
                                try {
                                    show_data = JSON.parse(data.content[column]);
                                } catch(e) {
                                    show_data = data.content[column];
                                }
                                var content = JSON.stringify(show_data, null, 2);
                                \$('#log-api-content').text(content);
                            }
                            \$('#log-api-detail').modal();
                        }
                    }
                );
            }
        });
    </script>
";
        // line 149
        echo ($context["footer"] ?? null);
        echo "
";
    }

    public function getTemplateName()
    {
        return "extension/upsmodule/about.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  219 => 149,  193 => 126,  156 => 92,  137 => 76,  134 => 75,  128 => 31,  124 => 30,  118 => 29,  114 => 28,  110 => 27,  106 => 26,  102 => 25,  98 => 24,  93 => 22,  89 => 21,  83 => 20,  79 => 19,  74 => 17,  68 => 14,  57 => 8,  51 => 7,  46 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/upsmodule/about.twig", "");
    }
}

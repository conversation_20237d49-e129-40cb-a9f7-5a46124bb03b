<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/customerGroupexport.twig */
class __TwigTemplate_4ae6a5c7122c8ab04df0198c3c7467fc4a63f22594a30304c5c60745f1a3ce78 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div class=\"row\">
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-language\">";
        // line 5
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t\t<select class=\"form-control\" name=\"filter_language_id\">
\t\t";
        // line 7
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 8
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 8);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 8);
            echo "</option>
\t\t ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 10
        echo "\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-6\">
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">";
        // line 15
        echo ($context["entry_limit"] ?? null);
        echo " (Note:Export Data limit)</label>
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_start\" value=\"0\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\"/> -
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_limit\" value=\"";
        // line 17
        echo ($context["filter_limit"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_limit"] ?? null);
        echo "\" id=\"input-limit\" class=\"form-control\" />
\t  </div>
\t</div>
\t<div class=\"col-sm-6\">
\t\t<div class=\"form-group\">
\t\t\t<label class=\"control-label\" for=\"input-status\"></label>
\t\t\t<button type=\"button\" id=\"button_filter_customerGroup\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 23
        echo ($context["button_export"] ?? null);
        echo "</button>
\t\t</div>
\t</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/customerGroupexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  86 => 23,  75 => 17,  70 => 15,  63 => 10,  52 => 8,  48 => 7,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/customerGroupexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/customerGroupexport.twig");
    }
}

<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/pexport.twig */
class __TwigTemplate_e216a910a6102a018ae0832f6545ea2fcf103e34bf5795cd69f9a9addee28e2f extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr/>
<div class=\"row\">
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">";
        // line 5
        echo ($context["entry_store"] ?? null);
        echo "</label>
\t\t<select class=\"form-control\" name=\"filter_store\">
\t\t <option value=\"0\">";
        // line 7
        echo ($context["text_default"] ?? null);
        echo "</option>
\t\t  ";
        // line 8
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 9
            echo "\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 9);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "name", [], "any", false, false, false, 9);
            echo "</option>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 11
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">";
        // line 14
        echo ($context["entry_categories"] ?? null);
        echo "</label>
\t\t<select name=\"filter_categories\" id=\"input-categories\" class=\"form-control\">
\t\t\t<option value=\"*\">";
        // line 16
        echo ($context["select_categories"] ?? null);
        echo "</option>
\t\t\t";
        // line 17
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["categories"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 18
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["category"], "category_id", [], "any", false, false, false, 18);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 18);
            echo "</option>
\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['category'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 20
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%;\" class=\"control-label\" for=\"input-quantity\">";
        // line 23
        echo ($context["entry_quantityrange"] ?? null);
        echo "</label>
\t\t   <input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_quantity_to\" value=\"";
        // line 24
        echo ($context["filter_price"] ?? null);
        echo "\" placeholder=\"To\" id=\"input-price\" class=\"form-control\" /> - <input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_quantity_form\" value=\"";
        echo ($context["filter_quantity"] ?? null);
        echo "\" placeholder=\"From\" id=\"input-quantity\" class=\"form-control\"/>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-quantity\">";
        // line 27
        echo ($context["entry_stock_status"] ?? null);
        echo "</label>
\t\t<select name=\"filter_stock_status\" id=\"input-status\" class=\"form-control\">
\t\t\t<option value=\"*\">";
        // line 29
        echo ($context["select_stock_status"] ?? null);
        echo "</option>
\t\t\t";
        // line 30
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stock_statuses"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["stock"]) {
            // line 31
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["stock"], "stock_status_id", [], "any", false, false, false, 31);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["stock"], "name", [], "any", false, false, false, 31);
            echo "</option>
\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['stock'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 33
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-eformat\">";
        // line 36
        echo ($context["export_format"] ?? null);
        echo "</label>
\t\t<select name=\"filter_eformat\" id=\"input-eformat\" class=\"form-control\">
\t\t\t<option value=\"xls\">XLS</option>
\t\t\t<option value=\"csv\">CSV</option>
\t\t\t<option value=\"xlsx\">XLSX</option>
\t\t\t<option value=\"xml\">XML</option>
\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-language\">";
        // line 47
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t\t<select class=\"form-control\" name=\"filter_language_id\">
\t\t ";
        // line 49
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 50
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 50);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 50);
            echo "</option>
\t\t ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 52
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">";
        // line 55
        echo ($context["entry_manufacturer"] ?? null);
        echo "</label>
\t\t<select name=\"filter_manufacturer\" id=\"input-categories\" class=\"form-control\">
\t\t\t<option value=\"*\">";
        // line 57
        echo ($context["select_manufacture"] ?? null);
        echo "</option>
\t\t\t";
        // line 58
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["manufacturers"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["manufacturer"]) {
            // line 59
            echo "\t\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["manufacturer"], "manufacturer_id", [], "any", false, false, false, 59);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["manufacturer"], "name", [], "any", false, false, false, 59);
            echo "</option>
\t\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['manufacturer'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 61
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\">";
        // line 64
        echo ($context["entry_status"] ?? null);
        echo "</label>
\t\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t\t  <option value=\"*\">";
        // line 66
        echo ($context["select_status"] ?? null);
        echo "</option>
\t\t  ";
        // line 67
        if (($context["filter_status"] ?? null)) {
            // line 68
            echo "\t\t  <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t\t  ";
        } else {
            // line 70
            echo "\t\t  <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t\t ";
        }
        // line 72
        echo "\t\t  ";
        if (( !($context["filter_status"] ?? null) &&  !(null === ($context["filter_status"] ?? null)))) {
            // line 73
            echo "\t\t  <option value=\"0\" selected=\"selected\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t\t  ";
        } else {
            // line 75
            echo "\t\t  <option value=\"0\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t\t ";
        }
        // line 77
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%;\" class=\"control-label\" for=\"input-limit\">Export in batches</label>
\t\t\t<input type=\"text\" name=\"filter_limit\" value=\"\" placeholder=\"export data in 2 or more files\" id=\"input-limit\" class=\"form-control\" />
\t\t\t<i><b>Note:</b> If you have large data then use this feature and export data in batches.</i>
\t  </div>
\t   <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-status\"></label>
\t\t<button type=\"button\" id=\"button-filter_product\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 86
        echo ($context["button_export"] ?? null);
        echo "</button>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t <label class=\"control-label\" for=\"input-pimage\">";
        // line 91
        echo ($context["product_image"] ?? null);
        echo "</label>
\t\t <select name=\"filter_pimage\" id=\"input-pimage\" class=\"form-control selectpicker\">
\t\t  <option value=\"no\">No</option>
\t\t  <option value=\"yes\">Yes</option>
\t\t </select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%;\" class=\"control-label\" for=\"input-limit\">Product ID - Condition </label>
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_idstart\" value=\"";
        // line 99
        echo ($context["miniproduct_id"] ?? null);
        echo "\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\" />
\t\t-
\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_idend\" value=\"";
        // line 101
        echo ($context["maxproduct_id"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_limit"] ?? null);
        echo "\" id=\"input-idend\" class=\"form-control\" />
\t  </div> 
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%;\" class=\"control-label\" for=\"input-price\">";
        // line 104
        echo ($context["entry_pricerange"] ?? null);
        echo "</label>
\t\t   <input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_price_to\" value=\"";
        // line 105
        echo ($context["filter_price"] ?? null);
        echo "\" placeholder=\"To\" id=\"input-price\" class=\"form-control\" /> - <input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_price_form\" value=\"";
        echo ($context["filter_price"] ?? null);
        echo "\" placeholder=\"From\" id=\"input-price\" class=\"form-control\"/>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">";
        // line 108
        echo ($context["entry_name"] ?? null);
        echo "</label>
\t\t<input type=\"text\" name=\"filter_name\" value=\"";
        // line 109
        echo ($context["filter_name"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_name"] ?? null);
        echo "\" id=\"input-name\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-model\">";
        // line 112
        echo ($context["entry_model"] ?? null);
        echo "</label>
\t\t<input type=\"text\" name=\"filter_model\" value=\"";
        // line 113
        echo ($context["filter_model"] ?? null);
        echo "\" placeholder=\"";
        echo ($context["entry_model"] ?? null);
        echo "\" id=\"input-model\" class=\"form-control\" />
\t  </div>
\t</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/pexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  319 => 113,  315 => 112,  307 => 109,  303 => 108,  295 => 105,  291 => 104,  283 => 101,  278 => 99,  267 => 91,  259 => 86,  248 => 77,  242 => 75,  236 => 73,  233 => 72,  227 => 70,  221 => 68,  219 => 67,  215 => 66,  210 => 64,  205 => 61,  194 => 59,  190 => 58,  186 => 57,  181 => 55,  176 => 52,  165 => 50,  161 => 49,  156 => 47,  142 => 36,  137 => 33,  126 => 31,  122 => 30,  118 => 29,  113 => 27,  105 => 24,  101 => 23,  96 => 20,  85 => 18,  81 => 17,  77 => 16,  72 => 14,  67 => 11,  56 => 9,  52 => 8,  48 => 7,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/pexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/pexport.twig");
    }
}

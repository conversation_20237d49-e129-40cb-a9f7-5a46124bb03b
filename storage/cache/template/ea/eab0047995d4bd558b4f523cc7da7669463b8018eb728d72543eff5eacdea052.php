<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/couponexport.twig */
class __TwigTemplate_ead5d05d0363d1d5e602d023ed550dda7ec09a219303deccab4be20d9d571d84 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div class=\"row\">
\t<div class=\"col-sm-12\">
\t\t<div class=\"form-group\">
\t\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Limit (Note:Export Data limit)</label>
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_start\" value=\"0\" placeholder=\"Start\" id=\"input-start\" class=\"form-control\"/> -
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_limit\" value=\"";
        // line 7
        echo ($context["filter_limit"] ?? null);
        echo "\" placeholder=\"Limit\" id=\"input-limit\" class=\"form-control\" />
\t    </div>
\t\t<div class=\"form-group\">
\t\t  <button type=\"button\" id=\"buttoncoupons\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 10
        echo ($context["button_export"] ?? null);
        echo "</button>
\t   </div>
\t</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/couponexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  51 => 10,  45 => 7,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/couponexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/couponexport.twig");
    }
}

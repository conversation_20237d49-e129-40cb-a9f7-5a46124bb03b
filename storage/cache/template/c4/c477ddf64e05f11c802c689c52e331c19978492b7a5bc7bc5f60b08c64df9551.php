<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/userimport.twig */
class __TwigTemplate_e1826b4cd3d8ecb3a1f6c2dc4139c257538e514ad8d38a12d659f314947dc036 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["useraction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"form_userimportx\" class=\"form-horizontal\">

\t<div class=\"row\">
\t\t
\t\t<!-- Coupon Group File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\"/>\t
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["entry_userimport"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t
\t\t
\t\t<!-- Password and Import Button -->
\t\t
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label class=\"control-label\" for=\"input-password\"><span data-toggle=\"tooltip\" title=\"";
        // line 20
        echo ($context["help_password"] ?? null);
        echo "\">";
        echo ($context["text_password"] ?? null);
        echo "</span></label>
\t\t\t<table class=\"table table-responsive\">
\t\t\t\t<tr>
\t\t\t\t\t<td><input type=\"radio\"  name=\"password_format\" value=\"P\"/> Plan Password</td>
\t\t\t\t\t<td><input type=\"radio\" checked=\"checked\" name=\"password_format\" value=\"E\"/> Encript Password</td>
\t\t\t\t</tr>
\t\t\t</table>
\t\t\t<b>Note:</b> ";
        // line 27
        echo ($context["help_password"] ?? null);
        echo "
\t\t\t
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#form_userimportx').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t</div>
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/userimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  76 => 27,  64 => 20,  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/userimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/userimport.twig");
    }
}

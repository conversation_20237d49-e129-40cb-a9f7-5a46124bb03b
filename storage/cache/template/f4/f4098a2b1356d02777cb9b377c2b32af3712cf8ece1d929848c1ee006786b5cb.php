<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/import/customerimport.twig */
class __TwigTemplate_408f3a12e37f3b0a0e7ffd8a50ca91bc027aa34161fde0c3bb7e8581ddecbd3e extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<form action=\"";
        // line 2
        echo ($context["customeraction"] ?? null);
        echo "\" method=\"post\" enctype=\"multipart/form-data\" id=\"customerimportform\" class=\"form-horizontal\">
\t
\t<div class=\"row\">
\t
\t\t<!-- Customer File Import Input -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<input type=\"file\" name=\"import\" value=\"\" />
\t\t</div>
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t";
        // line 11
        echo ($context["text_import_customer"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Exiting Email and password -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<label class=\"control-label\" for=\"input-password\"><span data-toggle=\"tooltip\" title=\"";
        // line 17
        echo ($context["help_password"] ?? null);
        echo "\">";
        echo ($context["text_password"] ?? null);
        echo "</span></label>
\t\t\t
\t\t\t<table class=\"table table-responsive\">
\t\t\t\t<tr>
\t\t\t\t\t<td><input type=\"radio\"  name=\"password_format\" value=\"P\"/> Plan Password</td>
\t\t\t\t\t<td><input type=\"radio\" checked=\"checked\" name=\"password_format\" value=\"E\"/> Encript Password</td>
\t\t\t\t</tr>
\t\t\t</table>
\t\t\t<b>Note:</b> ";
        // line 25
        echo ($context["help_password"] ?? null);
        echo "
\t\t</div>
\t\t<div class=\"clearfix\"></div>
\t\t
\t\t<!-- Import Button -->
\t\t<div class=\"col-sm-6 tbpadding\">
\t\t\t<button onclick=\"\$('#customerimportform').submit()\"; type=\"button\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-upload\"></i> Import</button>
\t\t</div>
\t\t
\t</div>
</form>";
    }

    public function getTemplateName()
    {
        return "extension/import/customerimport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  74 => 25,  61 => 17,  52 => 11,  40 => 2,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/import/customerimport.twig", "/home/<USER>/public_html/admin/view/template/extension/import/customerimport.twig");
    }
}

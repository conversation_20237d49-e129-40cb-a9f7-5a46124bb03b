<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/orderexport.twig */
class __TwigTemplate_67ab24c9131dd3be7f986e14a723e92073509922590549251fcf37ac7be7a6c5 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div>
  <div class=\"row\">
\t<div class=\"col-sm-4\">
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-name\">Customer Name</label>
\t\t<input type=\"text\" name=\"customer_name\" value=\"\" placeholder=\"Customer Name\" id=\"input-name\" class=\"form-control\" />
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-order-status\">";
        // line 10
        echo ($context["entry_order_status"] ?? null);
        echo "</label>
\t\t<select name=\"filter_order_status\" id=\"input-order-status\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  <option value=\"0\">";
        // line 13
        echo ($context["text_missing"] ?? null);
        echo "</option>
\t\t  ";
        // line 14
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["order_statuses"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["order_status"]) {
            // line 15
            echo "\t\t  <option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["order_status"], "order_status_id", [], "any", false, false, false, 15);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["order_status"], "name", [], "any", false, false, false, 15);
            echo "</option>
\t\t ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['order_status'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 17
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-total\">";
        // line 20
        echo ($context["entry_total"] ?? null);
        echo "</label>
\t\t<input type=\"text\" name=\"filter_total\" value=\"\" placeholder=\"\" id=\"input-total\" class=\"form-control\" />
\t  </div>
\t   <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-eformat\">";
        // line 24
        echo ($context["export_format"] ?? null);
        echo "</label>
\t\t<select name=\"filter_eformat\" id=\"input-eformat\" class=\"form-control\">
\t\t\t<option value=\"xls\">XLS</option>
\t\t\t<option value=\"xlsx\">XLSX</option>
\t\t\t<option value=\"xml\">XML</option>
\t\t</select>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t   <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-customer-group\">";
        // line 34
        echo ($context["entry_customer_group"] ?? null);
        echo "</label>
\t\t<select name=\"filter_customer_group_id\" id=\"input-customer-group\" class=\"form-control\">
\t\t  <option value=\"*\"></option>
\t\t  ";
        // line 37
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["customer_groups"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["customer_group"]) {
            // line 38
            echo "\t\t  <option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["customer_group"], "customer_group_id", [], "any", false, false, false, 38);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["customer_group"], "name", [], "any", false, false, false, 38);
            echo "</option>
\t\t\t  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['customer_group'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 40
        echo "\t\t</select>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-to-date-added\">To Date Added</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_to_date_added\" value=\"\" placeholder=\"To Date Added\" data-date-format=\"YYYY-MM-DD\" id=\"input-to-date-added\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t  <button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span></div>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-to-date-modified\">To Date Modified</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_to_date_modified\" value=\"\" placeholder=\"To Date Modified\" data-date-format=\"YYYY-MM-DD\" id=\"input-to-date-modified\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t  <button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span></div>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\"></label>
\t\t<button type=\"button\" id=\"button-order_export\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 60
        echo ($context["button_export"] ?? null);
        echo "</button>
\t  </div>
\t</div>
\t<div class=\"col-sm-4\">
\t\t<div class=\"form-group\">
\t\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-order-id\">";
        // line 65
        echo ($context["entry_order_id"] ?? null);
        echo " - Condition </label>
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_to_order_id\" value=\"";
        // line 66
        echo ($context["miniorder_id"] ?? null);
        echo "\" placeholder=\"To Order\" id=\"input-to_order-id\" class=\"form-control\" /> -
\t\t\t<input style=\"display:inline-block; width:47%;\"; type=\"text\" name=\"filter_from_order_id\" value=\"";
        // line 67
        echo ($context["maxorder_id"] ?? null);
        echo "\" placeholder=\"From Order\" id=\"input-from_order-id\" class=\"form-control\" />
\t   </div>
\t   <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-from-date-added\">From Date Added</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_from_date_added\" value=\"\" placeholder=\"From Date Added\" data-date-format=\"YYYY-MM-DD\" id=\"input-from-date-added\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t  <button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span></div>
\t  </div>
\t  <div class=\"form-group\">
\t\t<label class=\"control-label\" for=\"input-form-date-modified\">From Date Modified</label>
\t\t<div class=\"input-group date\">
\t\t  <input type=\"text\" name=\"filter_form_date_modified\" value=\"\" placeholder=\"From Date Modified\" data-date-format=\"YYYY-MM-DD\" id=\"input-form-date-modified\" class=\"form-control\" />
\t\t  <span class=\"input-group-btn\">
\t\t  <button style=\"padding: 8pt 19px;\" type=\"button\" class=\"btn btn-default\"><i class=\"fa fa-calendar\"></i></button>
\t\t  </span></div>
\t  </div>
\t    <div class=\"form-group\">
\t\t\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Export in batches</label>
\t\t\t<input type=\"text\" name=\"filter_limit\" value=\"\" placeholder=\"export data in 2 or more files\" id=\"input-limit\" class=\"form-control\" />
\t\t\t<i><b>Note:</b> If you have large data then use this feature and export data in batches.</i>
\t\t</div>
\t</div>
  </div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/orderexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  157 => 67,  153 => 66,  149 => 65,  141 => 60,  119 => 40,  108 => 38,  104 => 37,  98 => 34,  85 => 24,  78 => 20,  73 => 17,  62 => 15,  58 => 14,  54 => 13,  48 => 10,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/orderexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/orderexport.twig");
    }
}

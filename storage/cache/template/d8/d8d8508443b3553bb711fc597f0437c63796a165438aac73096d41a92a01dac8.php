<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* extension/export/categoriesexport.twig */
class __TwigTemplate_d1fcfe81e3f76636b0c3229007eee425e8399a14d0e6cd21a2515c2331932f62 extends \Twig\Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 1
        echo "<hr />
<div class=\"row\">
<div class=\"col-sm-6\">
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-name\">";
        // line 5
        echo ($context["entry_store"] ?? null);
        echo "</label>
\t<select class=\"form-control\" name=\"filter_store\">
\t <option value=\"0\">";
        // line 7
        echo ($context["text_default"] ?? null);
        echo "</option>
\t\t ";
        // line 8
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["stores"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["store"]) {
            // line 9
            echo "\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "store_id", [], "any", false, false, false, 9);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["store"], "name", [], "any", false, false, false, 9);
            echo "</option>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['store'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 11
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-language\">";
        // line 14
        echo ($context["entry_language"] ?? null);
        echo "</label>
\t<select class=\"form-control\" name=\"filter_language_id\">
\t ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["languages"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["language"]) {
            // line 17
            echo "\t\t<option value=\"";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "language_id", [], "any", false, false, false, 17);
            echo "\">";
            echo twig_get_attribute($this->env, $this->source, $context["language"], "name", [], "any", false, false, false, 17);
            echo "</option>
\t  ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['language'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 19
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t<label style=\"width:100%\" class=\"control-label\" for=\"input-limit\">Export in batches</label>
\t<input type=\"text\" name=\"filter_limit\" value=\"\" placeholder=\"export data in 2 or more files\" id=\"input-limit\" class=\"form-control\" />
\t<i><b>Note:</b> If you have large data then use this feature and export data in batches.</i>
  </div>
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-eformat\">";
        // line 27
        echo ($context["export_format"] ?? null);
        echo "</label>
\t<select name=\"filter_eformat\" id=\"input-eformat\" class=\"form-control\">
\t\t<option value=\"xls\">XLS</option>
\t\t<option value=\"csv\">CSV</option>
\t\t<option value=\"xlsx\">XLSX</option>
\t\t<option value=\"xml\">XML</option>
\t</select>
  </div>
</div>
<div class=\"col-sm-6\">
  <div class=\"form-group\">
\t <label class=\"control-label\" for=\"input-pimage\">Fetch Category Image</label>
\t <select name=\"filter_pimage\" id=\"input-pimage\" class=\"form-control selectpicker\">
\t  <option value=\"no\">No</option>
\t  <option value=\"yes\">Yes</option>
\t </select>
  </div>
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-name\">";
        // line 45
        echo ($context["entry_categories"] ?? null);
        echo "</label>
\t<select name=\"filter_categories\" id=\"input-categories\" class=\"form-control\">
\t\t<option value=\"*\"></option>
\t\t";
        // line 48
        $context['_parent'] = $context;
        $context['_seq'] = twig_ensure_traversable(($context["categories"] ?? null));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 49
            echo "\t\t<option value=\"";
            echo (($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 = $context["category"]) && is_array($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4) || $__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4 instanceof ArrayAccess ? ($__internal_f607aeef2c31a95a7bf963452dff024ffaeb6aafbe4603f9ca3bec57be8633f4["category_id"] ?? null) : null);
            echo "\">";
            echo (($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 = $context["category"]) && is_array($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144) || $__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144 instanceof ArrayAccess ? ($__internal_62824350bc4502ee19dbc2e99fc6bdd3bd90e7d8dd6e72f42c35efd048542144["name"] ?? null) : null);
            echo "</option>
\t\t";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_iterated'], $context['_key'], $context['category'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 51
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-status\">";
        // line 54
        echo ($context["entry_status"] ?? null);
        echo "</label>
\t<select name=\"filter_status\" id=\"input-status\" class=\"form-control\">
\t  <option value=\"*\"></option>
\t  ";
        // line 57
        if (($context["filter_status"] ?? null)) {
            // line 58
            echo "\t  <option value=\"1\" selected=\"selected\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t  ";
        } else {
            // line 60
            echo "\t  <option value=\"1\">";
            echo ($context["text_enabled"] ?? null);
            echo "</option>
\t ";
        }
        // line 62
        echo "\t  ";
        if (( !($context["filter_status"] ?? null) &&  !(null === ($context["filter_status"] ?? null)))) {
            // line 63
            echo "\t  <option value=\"0\" selected=\"selected\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t  ";
        } else {
            // line 65
            echo "\t  <option value=\"0\">";
            echo ($context["text_disabled"] ?? null);
            echo "</option>
\t ";
        }
        // line 67
        echo "\t</select>
  </div>
  <div class=\"form-group\">
\t<label class=\"control-label\" for=\"input-status\"></label>
\t<button type=\"button\" id=\"button_filter_categories\" class=\"ourbtn btn btn-primary form-control\"><i class=\"fa fa-download\"></i> ";
        // line 71
        echo ($context["button_export"] ?? null);
        echo "</button>
  </div>
</div>
</div>";
    }

    public function getTemplateName()
    {
        return "extension/export/categoriesexport.twig";
    }

    public function isTraitable()
    {
        return false;
    }

    public function getDebugInfo()
    {
        return array (  190 => 71,  184 => 67,  178 => 65,  172 => 63,  169 => 62,  163 => 60,  157 => 58,  155 => 57,  149 => 54,  144 => 51,  133 => 49,  129 => 48,  123 => 45,  102 => 27,  92 => 19,  81 => 17,  77 => 16,  72 => 14,  67 => 11,  56 => 9,  52 => 8,  48 => 7,  43 => 5,  37 => 1,);
    }

    public function getSourceContext()
    {
        return new Source("", "extension/export/categoriesexport.twig", "/home/<USER>/public_html/admin/view/template/extension/export/categoriesexport.twig");
    }
}

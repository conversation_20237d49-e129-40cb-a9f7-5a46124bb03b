<?php $val = array (
  'settings' => 
  array (
    'column_left' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'column_right' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'top' => 
    array (
      'rows' => 
      array (
        1 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-top-1',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-top-1-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-1-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '104',
                    'name' => 'Map',
                    'type' => 'blocks',
                  ),
                ),
              ),
            ),
          ),
        ),
        2 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-top-2',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-top-2-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-2-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '194',
                    'name' => 'Info Blocks Home Copy',
                    'type' => 'info_blocks',
                  ),
                ),
              ),
            ),
            2 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-top-2-2',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-top-2-2-1',
                  ),
                  'item' => 
                  array (
                    'id' => '20',
                    'name' => 'Form',
                    'type' => 'form',
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'header_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'global' => 
    array (
      0 => 
      array (
        'module_id' => '195',
        'module_type' => 'popup',
      ),
      1 => 
      array (
        'module_id' => '56',
        'module_type' => 'header_notice',
      ),
    ),
  ),
  'php' => 
  array (
    'headerDesktop' => '',
    'headerMobile' => '',
    'footerMenu' => '',
    'footerMenuPhone' => '',
  ),
  'js' => 
  array (
  ),
  'fonts' => 
  array (
  ),
  'css' => '.layout-8 .page-title{display:none !important}.grid-row-top-1::before{display:block;left:0;width:100vw}.grid-row-top-1>.grid-cols{max-width:100% !important}.grid-row-top-1 .wave-top{display:block}.grid-row-top-1 .wave-bottom{display:block}.grid-col-top-1-1{width:100%}.grid-col-top-1-1 .grid-item{height:auto}.grid-col-top-1-1 .grid-items{justify-content:flex-start}.grid-item-top-1-1-1{width:100%}.grid-row-top-2::before{display:block;left:-50%;width:100%}.grid-row-top-2{margin-top:20px}.grid-row-top-2 .wave-top{display:block}.grid-row-top-2 .wave-bottom{display:block}@media (max-width: 1300px){.grid-row-top-2{padding:10px}}@media (max-width: 760px){.grid-row-top-2{margin-top:0px}}.grid-col-top-2-1{width:33.33333333333333%;background:rgba(238, 238, 238, 1);padding:20px}.grid-col-top-2-1 .grid-item{height:auto}.grid-col-top-2-1 .grid-items{justify-content:flex-start}@media (max-width: 760px){.grid-col-top-2-1{width:100%}}.grid-item-top-2-1-1{width:100%}.grid-col-top-2-2{width:66.66666666666666%;background:rgba(255, 255, 255, 1);padding:10px;padding-left:30px}.grid-col-top-2-2 .grid-item{height:auto}.grid-col-top-2-2 .grid-items{justify-content:flex-start}@media (max-width: 760px){.grid-col-top-2-2{width:100%;padding:10px;padding-top:30px}}.grid-item-top-2-2-1{width:100%}',
);
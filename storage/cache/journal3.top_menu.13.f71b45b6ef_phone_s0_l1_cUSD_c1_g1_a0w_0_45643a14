<?php $val = array (
  'css' => '.top-menu-13 .j-menu li.top-menu-item-3>a::before{content:\'\\e9a9\' !important;font-family:icomoon !important}.top-menu-13>ul>.top-menu-item-3>a{text-align:left}.top-menu-13>ul>.top-menu-item-3>a>.links-text{display:block}.top-menu-13 > ul > .top-menu-item-3 > a .count-badge{position:relative}.top-menu-13 .j-menu li.top-menu-item-4>a::before{content:\'\\e98a\' !important;font-family:icomoon !important}.top-menu-13>ul>.top-menu-item-4>a{text-align:left}.top-menu-13>ul>.top-menu-item-4>a>.links-text{display:block}.top-menu-13 > ul > .top-menu-item-4 > a .count-badge{position:relative}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'scheduledStatus' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'status' => true,
    'id' => 'top-menu-68a78efaa46b0',
    'module_id' => 13,
    'classes' => 
    array (
      0 => 'top-menu',
      1 => 'top-menu-13',
    ),
    'items' => 
    array (
      3 => 
      array (
        'title' => 'Account',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/account',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-3',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
      4 => 
      array (
        'title' => 'Logout',
        'link' => 
        array (
          'type' => 'page',
          'id' => '',
          'href' => 'https://easoneyewear.com/index.php?route=account/logout',
          'name' => '',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'top-menu-item',
          2 => 'top-menu-item-4',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
          'icon-only' => false,
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);
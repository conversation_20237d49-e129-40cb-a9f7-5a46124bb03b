<?php $val = array (
  'settings' => 
  array (
    'column_left' => 
    array (
      'rows' => 
      array (
        1 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-column-left-1',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-column-left-1-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-column-left-1-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '36',
                    'name' => 'Filter',
                    'type' => 'filter',
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'column_right' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_top' => 
    array (
      'rows' => 
      array (
        1 => 
        array (
          'videoBgStatus' => false,
          'videoBg' => '',
          'videoInline' => false,
          'waveStatus' => false,
          'waveDirection' => '5',
          'wave2Status' => false,
          'wave2Direction' => '5',
          'classes' => 
          array (
            0 => 'grid-row',
            1 => 'grid-row-content-top-1',
          ),
          'columns' => 
          array (
            1 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-content-top-1-1',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-content-top-1-1-1',
                  ),
                  'item' => 
                  array (
                    'id' => '201',
                    'name' => 'Specials Banner',
                    'type' => 'banners',
                  ),
                ),
              ),
            ),
            2 => 
            array (
              'classes' => 
              array (
                0 => 'grid-col',
                1 => 'grid-col-content-top-1-2',
              ),
              'items' => 
              array (
                1 => 
                array (
                  'classes' => 
                  array (
                    0 => 'grid-item',
                    1 => 'grid-item-content-top-1-2-1',
                  ),
                  'item' => 
                  array (
                    'id' => '298',
                    'name' => 'Category Page Description',
                    'type' => 'blocks',
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'content_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'header_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_top' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'footer_bottom' => 
    array (
      'rows' => 
      array (
      ),
      'grid_classes' => 
      array (
        0 => 'grid-rows',
      ),
    ),
    'global' => 
    array (
      0 => 
      array (
        'module_id' => '195',
        'module_type' => 'popup',
      ),
      1 => 
      array (
        'module_id' => '56',
        'module_type' => 'header_notice',
      ),
    ),
  ),
  'php' => 
  array (
    'headerDesktop' => '',
    'headerMobile' => '',
    'footerMenu' => '',
    'footerMenuPhone' => '',
  ),
  'js' => 
  array (
  ),
  'fonts' => 
  array (
  ),
  'css' => '.grid-row-column-left-1::before{display:block;left:0;width:100vw}.grid-row-column-left-1 .wave-top{display:block}.grid-row-column-left-1 .wave-bottom{display:block}.grid-col-column-left-1-1{width:100%}.grid-col-column-left-1-1 .grid-item{height:auto}.grid-col-column-left-1-1 .grid-items{justify-content:flex-start}.grid-item-column-left-1-1-1{width:100%}.grid-row-content-top-1::before{display:block;left:0;width:100vw}.grid-row-content-top-1>.grid-cols{max-width:100% !important}.grid-row-content-top-1{margin-bottom:20px}.grid-row-content-top-1 .wave-top{display:block}.grid-row-content-top-1 .wave-bottom{display:block}.grid-col-content-top-1-1{width:100%}.grid-col-content-top-1-1 .grid-item{height:auto}.grid-col-content-top-1-1 .grid-items{justify-content:flex-start}.grid-item-content-top-1-1-1{width:100%}.grid-col-content-top-1-2{width:100%}.grid-col-content-top-1-2 .grid-item{height:auto}.grid-col-content-top-1-2 .grid-items{justify-content:flex-start}.grid-item-content-top-1-2-1{width:100%}',
);
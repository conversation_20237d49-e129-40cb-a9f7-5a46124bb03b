<?php $val = array (
  'css' => '.accordion-menu-338>.j-menu>li>a::before{content:\'\\e93f\' !important;font-family:icomoon !important}.accordion-menu-338.accordion-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-338.accordion-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-338.accordion-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-338.accordion-menu .j-menu>li>a{font-size:13px;color:rgba(51, 51, 51, 1);font-weight:700;text-transform:uppercase;padding:6px;padding-right:0px;padding-left:0px}.desktop .accordion-menu-338.accordion-menu .j-menu > li:hover > a, .accordion-menu-338.accordion-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.accordion-menu-338.accordion-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-338.accordion-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-338.accordion-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-338 .open-menu i::before{content:\'\\eba1\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-338 .open-menu[aria-expanded=\'true\'] i::before{content:\'\\eb86\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-338.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-338.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-338.accordion-menu .j-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-338.accordion-menu .j-menu .j-menu>li>a{font-size:14px;font-weight:400;text-transform:none;padding:5px}.accordion-menu-338.accordion-menu .j-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-338.accordion-menu .j-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-338.accordion-menu .j-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-338 .j-menu > li > div .j-menu>li>a{padding-left:18px !important}.accordion-menu-338 .j-menu > li > div .j-menu>li>div>.j-menu>li>a{padding-left:30px !important}.accordion-menu-338 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:40px !important}.accordion-menu-338 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:50px !important}.accordion-menu-338>.j-menu{padding-bottom:10px}.accordion-menu-338>.j-menu>li.accordion-menu-item>a::before{min-width:10px;font-size:12px !important}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'title' => 'Cosmetic Bags',
    'status' => true,
    'id' => 'accordion-menu-68a78775e070d',
    'module_id' => 338,
    'classes' => 
    array (
      0 => 'accordion-menu',
      1 => 'accordion-menu-338',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'Cosmetic Bags',
        'link' => 
        array (
          'type' => 'category',
          'id' => '163',
          'href' => 'https://easoneyewear.com/index.php?route=product/category&amp;path=163',
          'name' => 'cosmetic bags',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-1',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);
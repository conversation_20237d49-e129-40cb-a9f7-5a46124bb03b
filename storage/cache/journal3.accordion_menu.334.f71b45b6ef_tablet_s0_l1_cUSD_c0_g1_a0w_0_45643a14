<?php $val = array (
  'css' => '.accordion-menu-334>.j-menu>li>a::before{content:\'\\e93f\' !important;font-family:icomoon !important}.accordion-menu-334.accordion-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-334.accordion-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-334.accordion-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-334.accordion-menu .j-menu>li>a{font-size:13px;color:rgba(51, 51, 51, 1);font-weight:700;text-transform:uppercase;padding:6px;padding-right:0px;padding-left:0px}.desktop .accordion-menu-334.accordion-menu .j-menu > li:hover > a, .accordion-menu-334.accordion-menu .j-menu>li.active>a{color:rgba(217, 185, 110, 1)}.accordion-menu-334.accordion-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-334.accordion-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-334.accordion-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-334 .open-menu i::before{content:\'\\eba1\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-334 .open-menu[aria-expanded=\'true\'] i::before{content:\'\\eb86\' !important;font-family:icomoon !important;font-size:16px;left:5px}.accordion-menu-334.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge{margin-right:5px}.accordion-menu-334.accordion-menu .j-menu .j-menu .dropdown>a>.count-badge+.open-menu+.menu-label{margin-left:0}.accordion-menu-334.accordion-menu .j-menu .j-menu .dropdown>a::after{display:none}.accordion-menu-334.accordion-menu .j-menu .j-menu>li>a{font-size:14px;font-weight:400;text-transform:none;padding:5px}.accordion-menu-334.accordion-menu .j-menu .j-menu .links-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.accordion-menu-334.accordion-menu .j-menu .j-menu>li>a::before{margin-right:8px}.accordion-menu-334.accordion-menu .j-menu .j-menu a .count-badge{display:none;position:relative}.accordion-menu-334 .j-menu > li > div .j-menu>li>a{padding-left:18px !important}.accordion-menu-334 .j-menu > li > div .j-menu>li>div>.j-menu>li>a{padding-left:30px !important}.accordion-menu-334 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:40px !important}.accordion-menu-334 .j-menu > li > div .j-menu>li>div>.j-menu>li>div>.j-menu>li>div>.j-menu>li>a{padding-left:50px !important}.accordion-menu-334>.j-menu{padding-bottom:10px}.accordion-menu-334>.j-menu>li.accordion-menu-item>a::before{min-width:10px;font-size:12px !important}',
  'fonts' => 
  array (
  ),
  'settings' => 
  array (
    'schedule' => 
    array (
      'from' => '',
      'to' => '',
      'between' => true,
    ),
    'title' => 'Function',
    'status' => true,
    'id' => 'accordion-menu-68a7900ae3d6e',
    'module_id' => 334,
    'classes' => 
    array (
      0 => 'accordion-menu',
      1 => 'accordion-menu-334',
    ),
    'items' => 
    array (
      1 => 
      array (
        'title' => 'Sunglasses',
        'link' => 
        array (
          'type' => 'category',
          'id' => '86',
          'href' => 'https://easoneyewear.com/sunglasses',
          'name' => 'Shades',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-1',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      2 => 
      array (
        'title' => 'Reader',
        'link' => 
        array (
          'type' => 'category',
          'id' => '91',
          'href' => 'https://easoneyewear.com/index.php?route=product/category&amp;path=91',
          'name' => 'Readers',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-2',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
      3 => 
      array (
        'title' => 'Clear Lens',
        'link' => 
        array (
          'type' => 'category',
          'id' => '77',
          'href' => 'https://easoneyewear.com/index.php?route=product/category&amp;path=77',
          'name' => 'Clear Lens',
          'total' => NULL,
          'attrs' => 
          array (
          ),
          'classes' => 
          array (
          ),
        ),
        'classes' => 
        array (
          0 => 'menu-item',
          1 => 'accordion-menu-item',
          2 => 'accordion-menu-item-3',
          'multi-level' => false,
          'dropdown' => false,
          'drop-menu' => false,
        ),
        'items' => 
        array (
        ),
      ),
    ),
  ),
);